/**
 * SimplifiedTabManager.js (1/5)
 * Core tab management functionality for the SimplifiedTabSystem
 * Handles tab creation, activation, closing, and lifecycle management
 */

class SimplifiedTabManager {
    constructor() {
        this.tabs = new Map();
        this.activeTabId = null;
        this.tabContainer = null;
        this.contentContainer = null;
        this.tabIdCounter = 1;
        this.maxTabs = 20; // Maximum number of tabs allowed
        
        this.initialize();
    }

    /**
     * Initialize the tab manager
     */
    initialize() {
        console.log('SimplifiedTabManager', '[SimplifiedTabManager] Initializing...', );
        
        // Get DOM containers
        this.tabContainer = document.getElementById('tab-list');
        this.contentContainer = document.getElementById('tab-content');
        
        if (!this.tabContainer || !this.contentContainer) {
            console.error('SimplifiedTabManager', '[SimplifiedTabManager] Required DOM elements not found', );
            return;
        }
        
        // Set up event listeners
        this.setupEventListeners();
        
        console.log('SimplifiedTabManager', '[SimplifiedTabManager] Initialized successfully', );
    }

    /**
     * Create a new tab
     * @param {Object} options - Tab options
     * @returns {string} Tab ID
     */
    createTab(options) {
        const {
            id = this.generateTabId(),
            title = 'New Tab',
            module = null,
            content = null,
            closable = true,
            icon = null,
            data = {}
        } = options;

        // Check if tab limit reached
        if (this.tabs.size >= this.maxTabs) {
            console.warn('SimplifiedTabManager', '[SimplifiedTabManager] Maximum tab limit reached', );
            return null;
        }

        // Check if tab with same ID already exists
        if (this.tabs.has(id)) {
            console.warn(`[SimplifiedTabManager] Tab with ID ${id} already exists`);
            return null;
        }

        const tab = {
            id,
            title,
            module,
            content,
            closable,
            icon,
            data,
            hasUnsavedChanges: false,
            isActive: false,
            createdAt: new Date().toISOString(),
            lastActivatedAt: null
        };

        // Add to tabs collection
        this.tabs.set(id, tab);

        // Render tab in UI
        this.renderTab(tab);

        // Activate the new tab
        this.activateTab(id);

        // Emit tab created event
        window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.TAB_CREATED, {
            tabId: id,
            tab: { ...tab }
        });

        console.log("Component", `[SimplifiedTabManager] Tab created: ${id} (${title})`);

        return id;
    }

    /**
     * Close a tab
     * @param {string} tabId - Tab ID to close
     * @param {boolean} force - Force close without confirmation
     * @returns {Promise<boolean>} True if tab was closed
     */
    async closeTab(tabId, force = false) {
        const tab = this.tabs.get(tabId);
        if (!tab) {
            console.warn(`[SimplifiedTabManager] Tab ${tabId} not found`);
            return false;
        }

        // Check for unsaved changes
        if (!force && tab.hasUnsavedChanges) {
            const shouldClose = await this.confirmCloseTab(tab);
            if (!shouldClose) {
                return false;
            }
        }

        // Remove from DOM
        this.removeTabElement(tabId);

        // Remove from collection
        this.tabs.delete(tabId);

        // If this was the active tab, activate another
        if (this.activeTabId === tabId) {
            this.activateNextAvailableTab();
        }

        // Emit tab closed event
        window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.TAB_CLOSED, {
            tabId,
            tab: { ...tab }
        });

        console.log("Component", `[SimplifiedTabManager] Tab closed: ${tabId}`);

        return true;
    }

    /**
     * Activate a tab
     * @param {string} tabId - Tab ID to activate
     * @returns {boolean} True if tab was activated
     */
    activateTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) {
            console.warn(`[SimplifiedTabManager] Tab ${tabId} not found`);
            return false;
        }

        // Deactivate current active tab
        if (this.activeTabId && this.activeTabId !== tabId) {
            const currentTab = this.tabs.get(this.activeTabId);
            if (currentTab) {
                currentTab.isActive = false;
                this.updateTabElement(currentTab);
            }
        }

        // Activate new tab
        tab.isActive = true;
        tab.lastActivatedAt = new Date().toISOString();
        this.activeTabId = tabId;

        // Update UI
        this.updateTabElement(tab);
        this.showTabContent(tab);

        // Emit tab activated event
        window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.TAB_ACTIVATED, {
            tabId,
            tab: { ...tab }
        });

        console.log("Component", `[SimplifiedTabManager] Tab activated: ${tabId}`);

        return true;
    }

    /**
     * Update tab properties
     * @param {string} tabId - Tab ID
     * @param {Object} updates - Properties to update
     * @returns {boolean} True if tab was updated
     */
    updateTab(tabId, updates) {
        const tab = this.tabs.get(tabId);
        if (!tab) {
            console.warn(`[SimplifiedTabManager] Tab ${tabId} not found`);
            return false;
        }

        // Apply updates
        Object.assign(tab, updates);

        // Update UI
        this.updateTabElement(tab);

        // If content was updated and tab is active, refresh content
        if (updates.content && tab.isActive) {
            this.showTabContent(tab);
        }

        // Emit tab changed event
        window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.TAB_CHANGED, {
            tabId,
            updates,
            tab: { ...tab }
        });

        return true;
    }

    /**
     * Get tabs by module
     * @param {string} moduleCode - Module code
     * @returns {Array} Array of tabs for the module
     */
    getTabsByModule(moduleCode) {
        return Array.from(this.tabs.values())
            .filter(tab => tab.module === moduleCode);
    }

    /**
     * Get all tabs
     * @returns {Array} Array of all tabs
     */
    getAllTabs() {
        return Array.from(this.tabs.values());
    }

    /**
     * Get active tab
     * @returns {Object|null} Active tab or null
     */
    getActiveTab() {
        return this.activeTabId ? this.tabs.get(this.activeTabId) : null;
    }

    /**
     * Close all tabs
     * @param {string} exceptModule - Module to exclude from closing
     * @returns {Promise<number>} Number of tabs closed
     */
    async closeAllTabs(exceptModule = null) {
        const tabsToClose = Array.from(this.tabs.values())
            .filter(tab => !exceptModule || tab.module !== exceptModule);

        let closedCount = 0;

        for (const tab of tabsToClose) {
            const closed = await this.closeTab(tab.id);
            if (closed) {
                closedCount++;
            }
        }

        console.log("Component", `[SimplifiedTabManager] Closed ${closedCount} tabs`);

        return closedCount;
    }

    /**
     * Render tab in UI
     * @param {Object} tab - Tab object
     * @private
     */
    renderTab(tab) {
        const tabElement = document.createElement('div');
        tabElement.className = 'tab';
        tabElement.id = `tab-${tab.id}`;
        tabElement.dataset.tabId = tab.id;
        tabElement.dataset.module = tab.module || '';

        tabElement.innerHTML = `
            ${tab.icon ? `<img src="${tab.icon}" alt="" class="tab-icon">` : ''}
            <span class="tab-title">${this.escapeHtml(tab.title)}</span>
            ${tab.closable ? '<button class="tab-close" title="Close tab">×</button>' : ''}
        `;

        // Add event listeners
        tabElement.addEventListener('click', (e) => {
            if (!e.target.classList.contains('tab-close')) {
                this.activateTab(tab.id);
            }
        });

        if (tab.closable) {
            const closeButton = tabElement.querySelector('.tab-close');
            closeButton.addEventListener('click', (e) => {
                e.stopPropagation();
                this.closeTab(tab.id);
            });
        }

        // Add to container
        this.tabContainer.appendChild(tabElement);

        // Add animation
        tabElement.classList.add('tab-opening');
        setTimeout(() => {
            tabElement.classList.remove('tab-opening');
        }, 200);
    }

    /**
     * Update tab element in UI
     * @param {Object} tab - Tab object
     * @private
     */
    updateTabElement(tab) {
        const tabElement = document.getElementById(`tab-${tab.id}`);
        if (!tabElement) return;

        // Update active state
        tabElement.classList.toggle('active', tab.isActive);

        // Update unsaved changes indicator
        tabElement.classList.toggle('has-changes', tab.hasUnsavedChanges);

        // Update title
        const titleElement = tabElement.querySelector('.tab-title');
        if (titleElement) {
            titleElement.textContent = tab.title;
        }

        // Update module data attribute
        tabElement.dataset.module = tab.module || '';
    }

    /**
     * Remove tab element from UI
     * @param {string} tabId - Tab ID
     * @private
     */
    removeTabElement(tabId) {
        const tabElement = document.getElementById(`tab-${tabId}`);
        if (tabElement) {
            tabElement.classList.add('tab-closing');
            setTimeout(() => {
                if (tabElement.parentNode) {
                    tabElement.parentNode.removeChild(tabElement);
                }
            }, 200);
        }
    }

    /**
     * Show tab content
     * @param {Object} tab - Tab object
     * @private
     */
    showTabContent(tab) {
        // Hide all tab panes
        const allPanes = this.contentContainer.querySelectorAll('.tab-pane');
        allPanes.forEach(pane => {
            pane.classList.remove('active');
        });

        // Show or create tab pane
        let tabPane = document.getElementById(`tab-pane-${tab.id}`);
        
        if (!tabPane) {
            tabPane = document.createElement('div');
            tabPane.className = 'tab-pane';
            tabPane.id = `tab-pane-${tab.id}`;
            this.contentContainer.appendChild(tabPane);
        }

        tabPane.classList.add('active');

        // Render content if TabContentRenderer is available
        if (window.tabContentRenderer) {
            window.tabContentRenderer.render(tab, tabPane);
        } else {
            // Fallback rendering
            tabPane.innerHTML = `
                <div class="tab-content-placeholder">
                    <h3>${tab.title}</h3>
                    <p>Module: ${tab.module || 'None'}</p>
                    <p>Content type: ${tab.content?.type || 'Unknown'}</p>
                </div>
            `;
        }
    }

    /**
     * Activate next available tab
     * @private
     */
    activateNextAvailableTab() {
        const availableTabs = Array.from(this.tabs.values());
        
        if (availableTabs.length === 0) {
            this.activeTabId = null;
            return;
        }

        // Try to activate the most recently used tab
        const sortedTabs = availableTabs.sort((a, b) => {
            const aTime = new Date(a.lastActivatedAt || a.createdAt);
            const bTime = new Date(b.lastActivatedAt || b.createdAt);
            return bTime - aTime;
        });

        this.activateTab(sortedTabs[0].id);
    }

    /**
     * Confirm tab close with unsaved changes
     * @param {Object} tab - Tab to close
     * @returns {Promise<boolean>} User confirmation
     * @private
     */
    async confirmCloseTab(tab) {
        return new Promise((resolve) => {
            if (confirm(`The tab "${tab.title}" has unsaved changes. Do you want to close it anyway?`)) {
                resolve(true);
            } else {
                resolve(false);
            }
        });
    }

    /**
     * Generate unique tab ID
     * @returns {string} Unique tab ID
     * @private
     */
    generateTabId() {
        return `tab_${Date.now()}_${this.tabIdCounter++}`;
    }

    /**
     * Escape HTML to prevent XSS
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     * @private
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Set up event listeners
     * @private
     */
    setupEventListeners() {
        // New tab button
        const newTabButton = document.getElementById('new-tab-button');
        if (newTabButton) {
            newTabButton.addEventListener('click', () => {
                this.createTab({
                    title: 'New Tab',
                    content: { type: 'empty' }
                });
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 't':
                        e.preventDefault();
                        this.createTab({ title: 'New Tab' });
                        break;
                    case 'w':
                        e.preventDefault();
                        if (this.activeTabId) {
                            this.closeTab(this.activeTabId);
                        }
                        break;
                }
            }
        });
    }

    /**
     * Get tab manager statistics
     * @returns {Object} Statistics
     */
    getStats() {
        return {
            totalTabs: this.tabs.size,
            activeTabId: this.activeTabId,
            maxTabs: this.maxTabs,
            moduleBreakdown: this.getModuleBreakdown()
        };
    }

    /**
     * Get breakdown of tabs by module
     * @returns {Object} Module breakdown
     * @private
     */
    getModuleBreakdown() {
        const breakdown = {};
        this.tabs.forEach(tab => {
            const module = tab.module || 'none';
            breakdown[module] = (breakdown[module] || 0) + 1;
        });
        return breakdown;
    }
}

console.log('SimplifiedTabManager', '[SimplifiedTabManager] Class defined successfully', );