/**
 * TabContentRenderer.js (2/5)
 * Content rendering engine for the SimplifiedTabSystem
 * Handles different content types and provides extensible rendering system
 */

class TabContentRenderer {
    constructor() {
        this.renderers = new Map();
        this.templateCache = new Map();
        this.renderQueue = [];
        this.isProcessing = false;
        
        this.initialize();
    }

    /**
     * Initialize the content renderer
     */
    initialize() {
        console.log('TabContentRenderer', '[TabContentRenderer] Initializing...', );
        
        // Register default renderers
        this.registerDefaultRenderers();
        
        // Set up template loading
        this.loadTemplates();
        
        console.log('TabContentRenderer', '[TabContentRenderer] Initialized successfully', );
    }

    /**
     * Register a content renderer
     * @param {string} contentType - Content type identifier
     * @param {Function} renderer - Renderer function
     */
    registerRenderer(contentType, renderer) {
        if (typeof renderer !== 'function') {
            throw new Error('Renderer must be a function');
        }
        
        this.renderers.set(contentType, renderer);
        console.log("Component", `[TabContentRenderer] Renderer registered for type: ${contentType}`);
    }

    /**
     * Render tab content
     * @param {Object} tab - Tab object
     * @param {HTMLElement} container - Container element
     * @returns {Promise<void>}
     */
    async render(tab, container) {
        if (!tab || !container) {
            console.error('TabContentRenderer', '[TabContentRenderer] Invalid tab or container', );
            return;
        }

        try {
            const contentType = tab.content?.type || 'default';
            const renderer = this.renderers.get(contentType);

            if (!renderer) {
                console.warn(`[TabContentRenderer] No renderer found for type: ${contentType}`);
                this.renderFallback(tab, container);
                return;
            }

            // Add to render queue for performance
            this.queueRender(tab, container, renderer);
            
        } catch (error) {
            console.error('TabContentRenderer', '[TabContentRenderer] Render error:', error);
            this.renderError(tab, container, error);
        }
    }

    /**
     * Queue render operation
     * @param {Object} tab - Tab object
     * @param {HTMLElement} container - Container element
     * @param {Function} renderer - Renderer function
     * @private
     */
    queueRender(tab, container, renderer) {
        this.renderQueue.push({ tab, container, renderer });
        
        if (!this.isProcessing) {
            this.processRenderQueue();
        }
    }

    /**
     * Process render queue
     * @private
     */
    async processRenderQueue() {
        this.isProcessing = true;
        
        while (this.renderQueue.length > 0) {
            const { tab, container, renderer } = this.renderQueue.shift();
            
            try {
                await this.executeRenderer(tab, container, renderer);
            } catch (error) {
                console.error('TabContentRenderer', '[TabContentRenderer] Queue processing error:', error);
                this.renderError(tab, container, error);
            }
        }
        
        this.isProcessing = false;
    }

    /**
     * Execute renderer function
     * @param {Object} tab - Tab object
     * @param {HTMLElement} container - Container element
     * @param {Function} renderer - Renderer function
     * @private
     */
    async executeRenderer(tab, container, renderer) {
        // Clear container
        container.innerHTML = '';
        
        // Add loading state
        container.classList.add('loading');
        
        try {
            // Execute renderer
            const result = await renderer(tab, container);
            
            // Handle different return types
            if (typeof result === 'string') {
                container.innerHTML = result;
            } else if (result instanceof HTMLElement) {
                container.appendChild(result);
            }
            
            // Remove loading state
            container.classList.remove('loading');
            
            // Add loaded state
            container.classList.add('loaded');
            
            console.log("Component", `[TabContentRenderer] Content rendered for tab: ${tab.id}`);
            
        } catch (error) {
            container.classList.remove('loading');
            throw error;
        }
    }

    /**
     * Register default content renderers
     * @private
     */
    registerDefaultRenderers() {
        // Dashboard renderer
        this.registerRenderer('dashboard', async (tab, container) => {
            const moduleInfo = window.exclusiveModuleController?.getModuleInfo(tab.module);
            const moduleColor = moduleInfo?.color || '#4a90e2';
            
            return `
                <div class="dashboard-content" style="--module-color: ${moduleColor}">
                    <div class="dashboard-header">
                        <h1>${tab.title}</h1>
                        <p class="dashboard-subtitle">Módulo ${moduleInfo?.name || tab.module}</p>
                    </div>
                    
                    <div class="dashboard-widgets">
                        <div class="widget-grid">
                            <div class="widget card">
                                <h3>Resumen Rápido</h3>
                                <div class="widget-content">
                                    <p>Bienvenido al módulo ${moduleInfo?.name || tab.module}</p>
                                    <p>Usa el panel lateral para navegar por las funciones disponibles.</p>
                                </div>
                            </div>
                            
                            <div class="widget card">
                                <h3>Acciones Rápidas</h3>
                                <div class="widget-content">
                                    <button class="btn btn-primary" onclick="window.tabContentRenderer.createNewDocument('${tab.module}')">
                                        Nuevo Documento
                                    </button>
                                    <button class="btn btn-secondary" onclick="window.tabContentRenderer.showSearch('${tab.module}')">
                                        Buscar
                                    </button>
                                </div>
                            </div>
                            
                            <div class="widget card">
                                <h3>Estadísticas</h3>
                                <div class="widget-content">
                                    <div class="stat">
                                        <span class="stat-value">0</span>
                                        <span class="stat-label">Documentos</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-value">0</span>
                                        <span class="stat-label">Pendientes</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        // Document renderer
        this.registerRenderer('document', async (tab, container) => {
            const docId = tab.content?.documentId;
            const docType = tab.content?.documentType || 'text';
            
            return `
                <div class="document-viewer">
                    <div class="document-toolbar">
                        <div class="toolbar-left">
                            <button class="btn btn-sm" onclick="window.tabContentRenderer.saveDocument('${tab.id}')">
                                💾 Guardar
                            </button>
                            <button class="btn btn-sm" onclick="window.tabContentRenderer.exportDocument('${tab.id}')">
                                📤 Exportar
                            </button>
                        </div>
                        <div class="toolbar-center">
                            <span class="document-status">Documento ${docId || 'nuevo'}</span>
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-sm" onclick="window.tabContentRenderer.showDocumentInfo('${tab.id}')">
                                ℹ️ Info
                            </button>
                        </div>
                    </div>
                    
                    <div class="document-content">
                        ${this.renderDocumentContent(docType, tab.content)}
                    </div>
                </div>
            `;
        });

        // Form renderer
        this.registerRenderer('form', async (tab, container) => {
            const formType = tab.content?.formType || 'generic';
            const formData = tab.content?.data || {};
            
            return `
                <div class="form-container">
                    <div class="form-header">
                        <h2>${tab.title}</h2>
                        <p class="form-description">${tab.content?.description || ''}</p>
                    </div>
                    
                    <form class="form" id="form-${tab.id}">
                        ${this.renderFormFields(formType, formData)}
                        
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="window.tabContentRenderer.resetForm('${tab.id}')">
                                Resetear
                            </button>
                            <button type="submit" class="btn btn-primary">
                                Guardar
                            </button>
                        </div>
                    </form>
                </div>
            `;
        });

        // List renderer
        this.registerRenderer('list', async (tab, container) => {
            const listType = tab.content?.listType || 'generic';
            const items = tab.content?.items || [];
            
            return `
                <div class="list-container">
                    <div class="list-header">
                        <h2>${tab.title}</h2>
                        <div class="list-controls">
                            <input type="search" placeholder="Buscar..." class="search-input">
                            <button class="btn btn-primary" onclick="window.tabContentRenderer.createNew('${tab.module}', '${listType}')">
                                ➕ Nuevo
                            </button>
                        </div>
                    </div>
                    
                    <div class="list-content">
                        ${this.renderListItems(listType, items)}
                    </div>
                </div>
            `;
        });

        // Settings renderer
        this.registerRenderer('settings', async (tab, container) => {
            return `
                <div class="settings-container">
                    <div class="settings-header">
                        <h2>Configuración</h2>
                        <p>Personaliza tu experiencia en CoreDesk</p>
                    </div>
                    
                    <div class="settings-content">
                        <div class="settings-section">
                            <h3>Apariencia</h3>
                            <div class="setting-item">
                                <label>Tema</label>
                                <select class="form-select" onchange="window.configurationPanel?.applyTheme(this.value)">
                                    <option value="dark">Oscuro</option>
                                    <option value="light">Claro</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h3>Idioma y Región</h3>
                            <div class="setting-item">
                                <label>Idioma</label>
                                <select class="form-select">
                                    <option value="es">Español</option>
                                    <option value="en">English</option>
                                    <option value="pt">Português</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        // Empty/placeholder renderer
        this.registerRenderer('empty', async (tab, container) => {
            return `
                <div class="empty-content">
                    <div class="empty-state">
                        <h3>Pestaña vacía</h3>
                        <p>Esta pestaña no tiene contenido asignado.</p>
                        <button class="btn btn-primary" onclick="window.simplifiedTabSystem?.closeTab('${tab.id}')">
                            Cerrar pestaña
                        </button>
                    </div>
                </div>
            `;
        });

        // Module content renderer
        this.registerRenderer('module', async (tab, container) => {
            const { moduleCode, element } = tab.content;
            
            // If the module provided a rendered element, use it
            if (element && element instanceof HTMLElement) {
                container.appendChild(element);
                return;
            }
            
            // If the module provided HTML content as a string
            if (element && typeof element === 'string') {
                container.innerHTML = element;
                return;
            }
            
            // If no element provided, try to get the module instance and render it
            const moduleInstance = window.exclusiveModuleController?.dynamicModuleManager?.getModuleInstance(moduleCode);
            if (moduleInstance && typeof moduleInstance.render === 'function') {
                try {
                    const moduleContent = moduleInstance.render();
                    if (moduleContent instanceof HTMLElement) {
                        container.appendChild(moduleContent);
                    } else if (typeof moduleContent === 'string') {
                        container.innerHTML = moduleContent;
                    }
                    return;
                } catch (error) {
                    console.error(`[TabContentRenderer] Error rendering module ${moduleCode}:`, error);
                }
            }
            
            // Fallback: show module information
            const moduleInfo = window.exclusiveModuleController?.getModuleInfo(moduleCode);
            container.innerHTML = `
                <div class="module-content-container">
                    <div class="module-header">
                        <div class="module-icon">${moduleInfo?.icon || '📦'}</div>
                        <div class="module-title">
                            <h2>${moduleInfo?.name || moduleCode}</h2>
                            <p class="module-description">${moduleInfo?.description || 'Módulo activo'}</p>
                        </div>
                    </div>
                    <div class="module-placeholder">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🔧</div>
                            <h3>Módulo Cargado</h3>
                            <p>El módulo ${moduleInfo?.name || moduleCode} está activo y funcionando.</p>
                            <small>Si no ves el contenido del módulo, verifica que esté correctamente configurado.</small>
                        </div>
                    </div>
                </div>
            `;
        });

        // Default fallback renderer
        this.registerRenderer('default', async (tab, container) => {
            return this.renderFallback(tab, container);
        });
    }

    /**
     * Render document content based on type
     * @param {string} docType - Document type
     * @param {Object} content - Content object
     * @returns {string} HTML content
     * @private
     */
    renderDocumentContent(docType, content) {
        switch (docType) {
            case 'text':
                return `<textarea class="document-editor" placeholder="Escribe aquí...">${content?.text || ''}</textarea>`;
            case 'rich':
                return `<div class="rich-editor" contenteditable="true">${content?.html || ''}</div>`;
            case 'pdf':
                return `<iframe src="${content?.url}" class="pdf-viewer"></iframe>`;
            default:
                return `<div class="unsupported-document">Tipo de documento no soportado: ${docType}</div>`;
        }
    }

    /**
     * Render form fields based on type
     * @param {string} formType - Form type
     * @param {Object} data - Form data
     * @returns {string} HTML content
     * @private
     */
    renderFormFields(formType, data) {
        // This would be extended based on actual form requirements
        return `
            <div class="form-group">
                <label class="form-label">Título</label>
                <input type="text" class="form-input" name="title" value="${data.title || ''}">
            </div>
            <div class="form-group">
                <label class="form-label">Descripción</label>
                <textarea class="form-input" name="description">${data.description || ''}</textarea>
            </div>
        `;
    }

    /**
     * Render list items based on type
     * @param {string} listType - List type
     * @param {Array} items - List items
     * @returns {string} HTML content
     * @private
     */
    renderListItems(listType, items) {
        if (!items || items.length === 0) {
            return `
                <div class="empty-list">
                    <p>No hay elementos para mostrar</p>
                    <button class="btn btn-primary" onclick="window.tabContentRenderer.createNew('', '${listType}')">
                        Crear el primero
                    </button>
                </div>
            `;
        }

        return `
            <div class="list-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Título</th>
                            <th>Fecha</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${items.map(item => `
                            <tr>
                                <td>${item.title || 'Sin título'}</td>
                                <td>${item.date || '-'}</td>
                                <td>${item.status || 'Activo'}</td>
                                <td>
                                    <button class="btn btn-sm btn-secondary" onclick="window.tabContentRenderer.editItem('${item.id}')">
                                        Editar
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Render fallback content
     * @param {Object} tab - Tab object
     * @param {HTMLElement} container - Container element
     * @private
     */
    renderFallback(tab, container) {
        container.innerHTML = `
            <div class="fallback-content">
                <div class="fallback-header">
                    <h3>${tab.title}</h3>
                    <p>Contenido no disponible</p>
                </div>
                <div class="fallback-info">
                    <p><strong>Módulo:</strong> ${tab.module || 'None'}</p>
                    <p><strong>Tipo:</strong> ${tab.content?.type || 'Unknown'}</p>
                    <p><strong>ID:</strong> ${tab.id}</p>
                </div>
            </div>
        `;
    }

    /**
     * Render error content
     * @param {Object} tab - Tab object
     * @param {HTMLElement} container - Container element
     * @param {Error} error - Error object
     * @private
     */
    renderError(tab, container, error) {
        container.innerHTML = `
            <div class="error-content">
                <div class="error-header">
                    <h3>Error rendering tab</h3>
                    <p>${tab.title}</p>
                </div>
                <div class="error-details">
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>Tab ID:</strong> ${tab.id}</p>
                    <p><strong>Content Type:</strong> ${tab.content?.type || 'Unknown'}</p>
                </div>
                <div class="error-actions">
                    <button class="btn btn-secondary" onclick="window.simplifiedTabSystem?.render('${tab.id}')">
                        Retry
                    </button>
                    <button class="btn btn-secondary" onclick="window.simplifiedTabSystem?.closeTab('${tab.id}')">
                        Close Tab
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Load content templates
     * @private
     */
    loadTemplates() {
        // TODO [2025-06-29] [Templates]: Implement template loading system
        console.log('TabContentRenderer', '[TabContentRenderer] Template loading system ready', );
    }

    // Public action methods for UI interactions

    /**
     * Create new document
     * @param {string} module - Module code
     */
    createNewDocument(module) {
        window.simplifiedTabSystem?.createTab({
            title: 'Nuevo Documento',
            module,
            content: {
                type: 'document',
                documentType: 'text'
            }
        });
    }

    /**
     * Show search interface
     * @param {string} module - Module code
     */
    showSearch(module) {
        window.simplifiedTabSystem?.createTab({
            title: 'Buscar',
            module,
            content: {
                type: 'search',
                module
            }
        });
    }

    /**
     * Save document
     * @param {string} tabId - Tab ID
     */
    saveDocument(tabId) {
        console.log("Component", `[TabContentRenderer] Saving document in tab: ${tabId}`);
        // TODO [2025-06-29] [Documents]: Implement document saving
    }

    /**
     * Export document
     * @param {string} tabId - Tab ID
     */
    exportDocument(tabId) {
        console.log("Component", `[TabContentRenderer] Exporting document from tab: ${tabId}`);
        // TODO [2025-06-29] [Documents]: Implement document export
    }

    /**
     * Create new item
     * @param {string} module - Module code
     * @param {string} type - Item type
     */
    createNew(module, type) {
        window.simplifiedTabSystem?.createTab({
            title: `Nuevo ${type}`,
            module,
            content: {
                type: 'form',
                formType: type
            }
        });
    }

    /**
     * Edit item
     * @param {string} itemId - Item ID
     */
    editItem(itemId) {
        console.log("Component", `[TabContentRenderer] Editing item: ${itemId}`);
        // TODO [2025-06-29] [Items]: Implement item editing
    }
}

console.log('TabContentRenderer', '[TabContentRenderer] Class defined successfully', );