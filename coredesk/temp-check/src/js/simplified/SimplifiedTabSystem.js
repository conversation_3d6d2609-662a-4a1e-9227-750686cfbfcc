/**
 * SimplifiedTabSystem.js (5/5)
 * Main integrating class for the SimplifiedTabSystem
 * Coordinates between TabManager, ContentRenderer, StateStore, and EventBus
 * Provides unified API for tab operations according to PRD specifications
 */

class SimplifiedTabSystem {
    constructor() {
        this.tabManager = null;
        this.contentRenderer = null;
        this.stateStore = null;
        this.eventBus = null;
        
        this.isInitialized = false;
        this.isAutoSaving = false;
        this.lastStateHash = null;
        
        this.config = {
            autoSave: true,
            autoSaveInterval: 30000, // 30 seconds
            restoreOnLoad: true,
            validateIntegrity: true
        };
        
        this.initialize();
    }

    /**
     * Initialize the SimplifiedTabSystem
     */
    async initialize() {
        console.log('TabSystem', '[SimplifiedTabSystem] Initializing unified tab system...', );
        
        try {
            // Initialize core components
            await this.initializeComponents();
            
            // Set up component integration
            this.setupComponentIntegration();
            
            // Set up event handling
            this.setupEventHandling();
            
            // Restore previous state if enabled
            if (this.config.restoreOnLoad) {
                await this.restoreState();
            }
            
            // Start auto-save if enabled
            if (this.config.autoSave) {
                this.startAutoSave();
            }
            
            // Set up global bindings
            this.setupGlobalBindings();
            
            this.isInitialized = true;
            
            console.log('TabSystem', '[SimplifiedTabSystem] Initialized successfully', );
            
            // Emit initialization complete event
            this.eventBus.emit('system-initialized', {
                timestamp: new Date().toISOString(),
                componentsReady: true
            });
            
            // Validate system integrity if enabled
            if (this.config.validateIntegrity) {
                this.validateSystemIntegrity();
            }
            
        } catch (error) {
            console.error('TabSystem', '[SimplifiedTabSystem] Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Initialize all core components
     * @private
     */
    async initializeComponents() {
        console.log('TabSystem', '[SimplifiedTabSystem] Initializing components...', );
        
        try {
            // Initialize in dependency order
            this.eventBus = new TabEventBus();
            this.stateStore = new TabStateStore();
            this.contentRenderer = new TabContentRenderer();
            this.tabManager = new SimplifiedTabManager();
            
            // Wait for all components to be ready
            await Promise.all([
                this.waitForComponent('eventBus'),
                this.waitForComponent('stateStore'),
                this.waitForComponent('contentRenderer'),
                this.waitForComponent('tabManager')
            ]);
            
            console.log('TabSystem', '[SimplifiedTabSystem] All components initialized', );
            
        } catch (error) {
            console.error('TabSystem', '[SimplifiedTabSystem] Component initialization failed:', error);
            throw error;
        }
    }

    /**
     * Wait for a component to be ready
     * @param {string} componentName - Name of component to wait for
     * @returns {Promise<void>}
     * @private
     */
    async waitForComponent(componentName) {
        const component = this[componentName];
        if (!component) {
            throw new Error(`Component ${componentName} not found`);
        }
        
        // Components are considered ready when they exist and have initialized
        // This is a simple check - could be enhanced with explicit readiness signals
        return Promise.resolve();
    }

    /**
     * Set up integration between components
     * @private
     */
    setupComponentIntegration() {
        console.log('TabSystem', '[SimplifiedTabSystem] Setting up component integration...', );
        
        // Integrate TabManager with ContentRenderer
        this.integrateTabManagerWithRenderer();
        
        // Integrate TabManager with StateStore
        this.integrateTabManagerWithStateStore();
        
        // Integrate EventBus with all components
        this.integrateEventBus();
        
        console.log('TabSystem', '[SimplifiedTabSystem] Component integration complete', );
    }

    /**
     * Integrate TabManager with ContentRenderer
     * @private
     */
    integrateTabManagerWithRenderer() {
        // Override tabManager's showTabContent to use contentRenderer
        const originalShowTabContent = this.tabManager.showTabContent.bind(this.tabManager);
        
        this.tabManager.showTabContent = async (tab) => {
            // Get or create tab pane
            let tabPane = document.getElementById(`tab-pane-${tab.id}`);
            
            if (!tabPane) {
                tabPane = document.createElement('div');
                tabPane.className = 'tab-pane';
                tabPane.id = `tab-pane-${tab.id}`;
                this.tabManager.contentContainer.appendChild(tabPane);
            }
            
            // Hide all other panes
            const allPanes = this.tabManager.contentContainer.querySelectorAll('.tab-pane');
            allPanes.forEach(pane => pane.classList.remove('active'));
            
            // Show current pane
            tabPane.classList.add('active');
            
            // Use ContentRenderer to render content
            try {
                await this.contentRenderer.render(tab, tabPane);
                
                // Emit content rendered event
                this.eventBus.emit('content-rendered', {
                    tabId: tab.id,
                    contentType: tab.content?.type,
                    timestamp: new Date().toISOString()
                });
                
            } catch (error) {
                console.error(`[SimplifiedTabSystem] Error rendering content for tab ${tab.id}:`, error);
                
                // Fallback to original method
                originalShowTabContent(tab);
            }
        };
    }

    /**
     * Integrate TabManager with StateStore
     * @private
     */
    integrateTabManagerWithStateStore() {
        // Auto-save on tab changes
        const originalCreateTab = this.tabManager.createTab.bind(this.tabManager);
        const originalCloseTab = this.tabManager.closeTab.bind(this.tabManager);
        const originalUpdateTab = this.tabManager.updateTab.bind(this.tabManager);
        const originalActivateTab = this.tabManager.activateTab.bind(this.tabManager);
        
        this.tabManager.createTab = (options) => {
            const result = originalCreateTab(options);
            this.scheduleStateSave();
            return result;
        };
        
        this.tabManager.closeTab = async (tabId, force) => {
            const result = await originalCloseTab(tabId, force);
            if (result) {
                this.scheduleStateSave();
            }
            return result;
        };
        
        this.tabManager.updateTab = (tabId, updates) => {
            const result = originalUpdateTab(tabId, updates);
            if (result) {
                this.scheduleStateSave();
            }
            return result;
        };
        
        this.tabManager.activateTab = (tabId) => {
            const result = originalActivateTab(tabId);
            if (result) {
                this.scheduleStateSave();
            }
            return result;
        };
    }

    /**
     * Integrate EventBus with all components
     * @private
     */
    integrateEventBus() {
        // Make eventBus available to all components
        if (this.tabManager) {
            this.tabManager.eventBus = this.eventBus;
        }
        
        if (this.contentRenderer) {
            this.contentRenderer.eventBus = this.eventBus;
        }
        
        if (this.stateStore) {
            this.stateStore.eventBus = this.eventBus;
        }
        
        // Set up cross-component event handling
        this.eventBus.on('tab-created', (eventData) => {
            console.log("Component", `[SimplifiedTabSystem] Tab created: ${eventData.data.tabId}`);
        });
        
        this.eventBus.on('tab-closed', (eventData) => {
            console.log("Component", `[SimplifiedTabSystem] Tab closed: ${eventData.data.tabId}`);
        });
        
        this.eventBus.on('content-rendered', (eventData) => {
            console.log("Component", `[SimplifiedTabSystem] Content rendered for tab: ${eventData.data.tabId}`);
        });
    }

    /**
     * Set up system-level event handling
     * @private
     */
    setupEventHandling() {
        // Handle global events
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.MODULE_SWITCHED, (event) => {
            this.handleModuleSwitch(event.detail);
        });
        
        // Handle page unload
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        // Handle visibility change for performance
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAutoSave();
            } else {
                this.resumeAutoSave();
            }
        });
    }

    /**
     * Set up global bindings
     * @private
     */
    setupGlobalBindings() {
        // Make system available globally
        window.simplifiedTabSystem = this;
        window.tabContentRenderer = this.contentRenderer;
        
        console.log('TabSystem', '[SimplifiedTabSystem] Global bindings established', );
    }

    /**
     * Create a new tab
     * @param {Object} options - Tab creation options
     * @returns {string|null} Tab ID or null if failed
     */
    createTab(options) {
        if (!this.isInitialized) {
            console.warn('TabSystem', '[SimplifiedTabSystem] System not initialized', );
            return null;
        }
        
        try {
            const tabId = this.tabManager.createTab(options);
            
            if (tabId) {
                this.eventBus.emit('tab-created', {
                    tabId,
                    options,
                    timestamp: new Date().toISOString()
                });
            }
            
            return tabId;
            
        } catch (error) {
            console.error('TabSystem', '[SimplifiedTabSystem] Error creating tab:', error);
            return null;
        }
    }

    /**
     * Close a tab
     * @param {string} tabId - Tab ID to close
     * @param {boolean} force - Force close without confirmation
     * @returns {Promise<boolean>} True if closed successfully
     */
    async closeTab(tabId, force = false) {
        if (!this.isInitialized) {
            console.warn('TabSystem', '[SimplifiedTabSystem] System not initialized', );
            return false;
        }
        
        try {
            const result = await this.tabManager.closeTab(tabId, force);
            
            if (result) {
                this.eventBus.emit('tab-closed', {
                    tabId,
                    timestamp: new Date().toISOString()
                });
            }
            
            return result;
            
        } catch (error) {
            console.error('TabSystem', '[SimplifiedTabSystem] Error closing tab:', error);
            return false;
        }
    }

    /**
     * Activate a tab
     * @param {string} tabId - Tab ID to activate
     * @returns {boolean} True if activated successfully
     */
    activateTab(tabId) {
        if (!this.isInitialized) {
            console.warn('TabSystem', '[SimplifiedTabSystem] System not initialized', );
            return false;
        }
        
        try {
            const result = this.tabManager.activateTab(tabId);
            
            if (result) {
                this.eventBus.emit('tab-activated', {
                    tabId,
                    timestamp: new Date().toISOString()
                });
            }
            
            return result;
            
        } catch (error) {
            console.error('TabSystem', '[SimplifiedTabSystem] Error activating tab:', error);
            return false;
        }
    }

    /**
     * Update a tab
     * @param {string} tabId - Tab ID to update
     * @param {Object} updates - Updates to apply
     * @returns {boolean} True if updated successfully
     */
    updateTab(tabId, updates) {
        if (!this.isInitialized) {
            console.warn('TabSystem', '[SimplifiedTabSystem] System not initialized', );
            return false;
        }
        
        try {
            const result = this.tabManager.updateTab(tabId, updates);
            
            if (result) {
                this.eventBus.emit('tab-updated', {
                    tabId,
                    updates,
                    timestamp: new Date().toISOString()
                });
            }
            
            return result;
            
        } catch (error) {
            console.error('TabSystem', '[SimplifiedTabSystem] Error updating tab:', error);
            return false;
        }
    }

    /**
     * Get tabs by module
     * @param {string} moduleCode - Module code
     * @returns {Array} Array of tabs
     */
    getTabsByModule(moduleCode) {
        if (!this.isInitialized) {
            return [];
        }
        
        return this.tabManager.getTabsByModule(moduleCode);
    }

    /**
     * Close all tabs
     * @param {string} exceptModule - Module to exclude
     * @returns {Promise<number>} Number of tabs closed
     */
    async closeAllTabs(exceptModule = null) {
        if (!this.isInitialized) {
            return 0;
        }
        
        return await this.tabManager.closeAllTabs(exceptModule);
    }

    /**
     * Filter tabs by module (hide/show based on current module)
     * @param {string} moduleCode - Module to filter by
     */
    filterTabsByModule(moduleCode) {
        if (!this.isInitialized) {
            return;
        }
        
        const allTabs = this.tabManager.getAllTabs();
        
        allTabs.forEach(tab => {
            const tabElement = document.getElementById(`tab-${tab.id}`);
            if (tabElement) {
                if (!moduleCode || tab.module === moduleCode) {
                    tabElement.style.display = '';
                } else {
                    tabElement.style.display = 'none';
                }
            }
        });
    }

    /**
     * Save current state
     * @returns {Promise<boolean>} True if saved successfully
     */
    async saveState() {
        if (!this.isInitialized) {
            return false;
        }
        
        try {
            const success = this.stateStore.saveState(
                this.tabManager.tabs,
                this.tabManager.activeTabId
            );
            
            if (success) {
                this.eventBus.emit('state-saved', {
                    timestamp: new Date().toISOString(),
                    tabCount: this.tabManager.tabs.size
                });
            }
            
            return success;
            
        } catch (error) {
            console.error('TabSystem', '[SimplifiedTabSystem] Error saving state:', error);
            return false;
        }
    }

    /**
     * Restore state from storage
     * @returns {Promise<boolean>} True if restored successfully
     */
    async restoreState() {
        if (!this.isInitialized) {
            return false;
        }
        
        try {
            const state = this.stateStore.loadState();
            
            if (!state || !state.tabs) {
                console.log('TabSystem', '[SimplifiedTabSystem] No state to restore', );
                return false;
            }
            
            console.log("Component", `[SimplifiedTabSystem] Restoring ${state.tabs.size} tabs...`);
            
            // Restore tabs
            let restoredCount = 0;
            for (const [tabId, tab] of state.tabs) {
                try {
                    this.tabManager.tabs.set(tabId, tab);
                    this.tabManager.renderTab(tab);
                    restoredCount++;
                } catch (error) {
                    console.warn(`[SimplifiedTabSystem] Failed to restore tab ${tabId}:`, error);
                }
            }
            
            // Restore active tab
            if (state.activeTabId && state.tabs.has(state.activeTabId)) {
                this.tabManager.activateTab(state.activeTabId);
            }
            
            this.eventBus.emit('state-restored', {
                timestamp: new Date().toISOString(),
                restoredCount,
                activeTabId: state.activeTabId
            });
            
            console.log("Component", `[SimplifiedTabSystem] Restored ${restoredCount} tabs successfully`);
            
            return true;
            
        } catch (error) {
            console.error('TabSystem', '[SimplifiedTabSystem] Error restoring state:', error);
            return false;
        }
    }

    /**
     * Handle module switch
     * @param {Object} switchData - Module switch data
     * @private
     */
    handleModuleSwitch(switchData) {
        if (!this.isInitialized) {
            return;
        }
        
        console.log("Component", `[SimplifiedTabSystem] Handling module switch: ${switchData.from} → ${switchData.to}`);
        
        // Filter tabs to show only the new module's tabs
        this.filterTabsByModule(switchData.to);
        
        // If no tabs for the new module, create a default one
        const moduleTabs = this.getTabsByModule(switchData.to);
        if (moduleTabs.length === 0) {
            // The ExclusiveModuleController should handle this
            console.log("Component", `[SimplifiedTabSystem] No tabs found for module ${switchData.to}`);
        }
    }

    /**
     * Schedule state save (debounced)
     * @private
     */
    scheduleStateSave() {
        if (!this.config.autoSave) {
            return;
        }
        
        clearTimeout(this._saveTimeout);
        this._saveTimeout = setTimeout(() => {
            this.saveState();
        }, 1000); // 1 second debounce
    }

    /**
     * Start auto-save timer
     * @private
     */
    startAutoSave() {
        if (this.isAutoSaving) {
            return;
        }
        
        this.isAutoSaving = true;
        this._autoSaveInterval = setInterval(() => {
            this.saveState();
        }, this.config.autoSaveInterval);
        
        console.log('TabSystem', '[SimplifiedTabSystem] Auto-save started', );
    }

    /**
     * Stop auto-save timer
     * @private
     */
    stopAutoSave() {
        if (!this.isAutoSaving) {
            return;
        }
        
        this.isAutoSaving = false;
        if (this._autoSaveInterval) {
            clearInterval(this._autoSaveInterval);
            this._autoSaveInterval = null;
        }
        
        console.log('TabSystem', '[SimplifiedTabSystem] Auto-save stopped', );
    }

    /**
     * Pause auto-save (for performance)
     * @private
     */
    pauseAutoSave() {
        this._autoSavePaused = true;
    }

    /**
     * Resume auto-save
     * @private
     */
    resumeAutoSave() {
        this._autoSavePaused = false;
    }

    /**
     * Validate system integrity
     * @private
     */
    validateSystemIntegrity() {
        const issues = [];
        
        // Check components
        if (!this.tabManager) issues.push('TabManager not initialized');
        if (!this.contentRenderer) issues.push('ContentRenderer not initialized');
        if (!this.stateStore) issues.push('StateStore not initialized');
        if (!this.eventBus) issues.push('EventBus not initialized');
        
        // Check DOM elements
        if (!document.getElementById('tab-list')) issues.push('Tab list element not found');
        if (!document.getElementById('tab-content')) issues.push('Tab content element not found');
        
        if (issues.length > 0) {
            console.warn('TabSystem', '[SimplifiedTabSystem] System integrity issues:', issues);
            return false;
        }
        
        console.log('TabSystem', '[SimplifiedTabSystem] System integrity validated successfully', );
        return true;
    }

    /**
     * Clean up resources
     * @private
     */
    cleanup() {
        console.log('TabSystem', '[SimplifiedTabSystem] Cleaning up...', );
        
        // Save state before cleanup
        if (this.isInitialized) {
            this.saveState();
        }
        
        // Stop auto-save
        this.stopAutoSave();
        
        // Clear timeouts
        if (this._saveTimeout) {
            clearTimeout(this._saveTimeout);
        }
        
        // Cleanup components
        if (this.eventBus) {
            this.eventBus.removeAllListeners();
        }
        
        console.log('TabSystem', '[SimplifiedTabSystem] Cleanup complete', );
    }

    /**
     * Get system statistics
     * @returns {Object} System statistics
     */
    getStats() {
        if (!this.isInitialized) {
            return { initialized: false };
        }
        
        return {
            initialized: true,
            autoSaving: this.isAutoSaving,
            components: {
                tabManager: this.tabManager?.getStats(),
                stateStore: this.stateStore?.getStats(),
                eventBus: this.eventBus?.getStats()
            },
            config: this.config
        };
    }

    /**
     * Update system configuration
     * @param {Object} newConfig - New configuration
     */
    updateConfig(newConfig) {
        Object.assign(this.config, newConfig);
        
        // Apply configuration changes
        if (newConfig.autoSave !== undefined) {
            if (newConfig.autoSave) {
                this.startAutoSave();
            } else {
                this.stopAutoSave();
            }
        }
        
        console.log('TabSystem', '[SimplifiedTabSystem] Configuration updated:', newConfig);
    }
}

console.log('TabSystem', '[SimplifiedTabSystem] Class defined successfully', );