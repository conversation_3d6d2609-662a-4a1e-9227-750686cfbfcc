/**
 * TabStateStore.js (3/5)
 * State persistence and management for the SimplifiedTabSystem
 * Handles saving/loading tab state to/from localStorage with serialization
 */

class TabStateStore {
    constructor() {
        this.storageKey = window.COREDESK_CONSTANTS?.STORAGE_KEYS?.TAB_STATE || 'coredesk_tab_state';
        this.maxStoredTabs = 50; // Maximum number of tabs to persist
        this.compressionEnabled = true;
        this.autoSaveInterval = 30000; // 30 seconds
        this.autoSaveTimer = null;
        
        this.initialize();
    }

    /**
     * Initialize the state store
     */
    initialize() {
        console.log('TabStateStore', '[TabStateStore] Initializing...', );
        
        // Set up auto-save
        this.startAutoSave();
        
        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            this.stopAutoSave();
        });
        
        console.log('TabStateStore', '[TabStateStore] Initialized successfully', );
    }

    /**
     * Save tabs state to localStorage
     * @param {Map} tabs - Tabs map
     * @param {string} activeTabId - Active tab ID
     * @returns {boolean} Success status
     */
    saveState(tabs, activeTabId) {
        try {
            console.log('TabStateStore', '[TabStateStore] Saving tab state...', );
            
            // Convert tabs map to serializable format
            const tabsArray = Array.from(tabs.entries()).map(([id, tab]) => ({
                id,
                ...this.serializeTab(tab)
            }));
            
            // Limit number of stored tabs (keep most recent)
            const sortedTabs = tabsArray
                .sort((a, b) => new Date(b.lastActivatedAt || b.createdAt) - new Date(a.lastActivatedAt || a.createdAt))
                .slice(0, this.maxStoredTabs);
            
            const state = {
                tabs: sortedTabs,
                activeTabId,
                savedAt: new Date().toISOString(),
                version: '2.0.0'
            };
            
            // Compress if enabled
            const stateString = this.compressionEnabled ? 
                this.compressState(JSON.stringify(state)) : 
                JSON.stringify(state);
            
            localStorage.setItem(this.storageKey, stateString);
            
            console.log("Component", `[TabStateStore] Saved ${sortedTabs.length} tabs to storage`);
            
            return true;
            
        } catch (error) {
            console.error('TabStateStore', '[TabStateStore] Error saving state:', error);
            
            // Try to save without compression as fallback
            if (this.compressionEnabled) {
                try {
                    this.compressionEnabled = false;
                    return this.saveState(tabs, activeTabId);
                } catch (fallbackError) {
                    console.error('TabStateStore', '[TabStateStore] Fallback save also failed:', fallbackError);
                }
            }
            
            return false;
        }
    }

    /**
     * Load tabs state from localStorage
     * @returns {Object|null} State object or null if not found/invalid
     */
    loadState() {
        try {
            console.log('TabStateStore', '[TabStateStore] Loading tab state...', );
            
            const stored = localStorage.getItem(this.storageKey);
            if (!stored) {
                console.log('TabStateStore', '[TabStateStore] No stored state found', );
                return null;
            }
            
            // Decompress if needed
            const stateString = this.isCompressed(stored) ? 
                this.decompressState(stored) : 
                stored;
            
            const state = JSON.parse(stateString);
            
            // Validate state structure
            if (!this.validateState(state)) {
                console.warn('TabStateStore', '[TabStateStore] Invalid state structure, ignoring', );
                return null;
            }
            
            // Deserialize tabs
            const tabs = new Map();
            state.tabs.forEach(tabData => {
                const tab = this.deserializeTab(tabData);
                if (tab) {
                    tabs.set(tab.id, tab);
                }
            });
            
            const result = {
                tabs,
                activeTabId: state.activeTabId,
                savedAt: state.savedAt
            };
            
            console.log("Component", `[TabStateStore] Loaded ${tabs.size} tabs from storage`);
            
            return result;
            
        } catch (error) {
            console.error('TabStateStore', '[TabStateStore] Error loading state:', error);
            
            // Clear corrupted state
            this.clearState();
            
            return null;
        }
    }

    /**
     * Clear stored state
     * @returns {boolean} Success status
     */
    clearState() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('TabStateStore', '[TabStateStore] State cleared', );
            return true;
        } catch (error) {
            console.error('TabStateStore', '[TabStateStore] Error clearing state:', error);
            return false;
        }
    }

    /**
     * Serialize a tab object for storage
     * @param {Object} tab - Tab object
     * @returns {Object} Serialized tab
     * @private
     */
    serializeTab(tab) {
        return {
            title: tab.title,
            module: tab.module,
            content: this.serializeContent(tab.content),
            closable: tab.closable,
            icon: tab.icon,
            data: this.serializeData(tab.data),
            hasUnsavedChanges: tab.hasUnsavedChanges,
            isActive: tab.isActive,
            createdAt: tab.createdAt,
            lastActivatedAt: tab.lastActivatedAt
        };
    }

    /**
     * Deserialize a tab object from storage
     * @param {Object} tabData - Serialized tab data
     * @returns {Object|null} Deserialized tab or null if invalid
     * @private
     */
    deserializeTab(tabData) {
        try {
            return {
                id: tabData.id,
                title: tabData.title || 'Untitled',
                module: tabData.module,
                content: this.deserializeContent(tabData.content),
                closable: tabData.closable !== false, // Default to true
                icon: tabData.icon,
                data: this.deserializeData(tabData.data),
                hasUnsavedChanges: tabData.hasUnsavedChanges || false,
                isActive: tabData.isActive || false,
                createdAt: tabData.createdAt || new Date().toISOString(),
                lastActivatedAt: tabData.lastActivatedAt
            };
        } catch (error) {
            console.error('TabStateStore', '[TabStateStore] Error deserializing tab:', error);
            return null;
        }
    }

    /**
     * Serialize tab content for storage
     * @param {*} content - Content to serialize
     * @returns {string} Serialized content
     * @private
     */
    serializeContent(content) {
        try {
            if (!content) return null;
            
            // Handle different content types
            if (typeof content === 'object') {
                // Remove non-serializable properties
                const serializable = { ...content };
                
                // Remove function properties
                Object.keys(serializable).forEach(key => {
                    if (typeof serializable[key] === 'function') {
                        delete serializable[key];
                    }
                });
                
                return JSON.stringify(serializable);
            }
            
            return String(content);
            
        } catch (error) {
            console.warn('TabStateStore', '[TabStateStore] Content serialization failed:', error);
            return null;
        }
    }

    /**
     * Deserialize tab content from storage
     * @param {string} content - Serialized content
     * @returns {*} Deserialized content
     * @private
     */
    deserializeContent(content) {
        try {
            if (!content) return null;
            
            // Try to parse as JSON first
            if (typeof content === 'string' && content.startsWith('{')) {
                return JSON.parse(content);
            }
            
            return content;
            
        } catch (error) {
            console.warn('TabStateStore', '[TabStateStore] Content deserialization failed:', error);
            return content; // Return as-is if parsing fails
        }
    }

    /**
     * Serialize tab data for storage
     * @param {*} data - Data to serialize
     * @returns {string} Serialized data
     * @private
     */
    serializeData(data) {
        try {
            if (!data) return null;
            return JSON.stringify(data);
        } catch (error) {
            console.warn('TabStateStore', '[TabStateStore] Data serialization failed:', error);
            return null;
        }
    }

    /**
     * Deserialize tab data from storage
     * @param {string} data - Serialized data
     * @returns {*} Deserialized data
     * @private
     */
    deserializeData(data) {
        try {
            if (!data) return {};
            return typeof data === 'string' ? JSON.parse(data) : data;
        } catch (error) {
            console.warn('TabStateStore', '[TabStateStore] Data deserialization failed:', error);
            return {};
        }
    }

    /**
     * Validate state structure
     * @param {Object} state - State to validate
     * @returns {boolean} True if valid
     * @private
     */
    validateState(state) {
        if (!state || typeof state !== 'object') {
            return false;
        }
        
        // Check required properties
        if (!Array.isArray(state.tabs)) {
            return false;
        }
        
        // Check version compatibility
        if (state.version && !this.isVersionCompatible(state.version)) {
            console.warn(`[TabStateStore] Incompatible state version: ${state.version}`);
            return false;
        }
        
        // Validate tabs structure
        for (const tab of state.tabs) {
            if (!tab.id || typeof tab.title !== 'string') {
                console.warn('TabStateStore', '[TabStateStore] Invalid tab structure found', );
                return false;
            }
        }
        
        return true;
    }

    /**
     * Check version compatibility
     * @param {string} version - Version to check
     * @returns {boolean} True if compatible
     * @private
     */
    isVersionCompatible(version) {
        // For now, accept any version
        // In the future, implement version migration logic
        return true;
    }

    /**
     * Compress state for storage
     * @param {string} stateString - State JSON string
     * @returns {string} Compressed state
     * @private
     */
    compressState(stateString) {
        try {
            // Simple compression using RLE for repeated patterns
            // In a real implementation, you might use a proper compression library
            return this.simpleCompress(stateString);
        } catch (error) {
            console.warn('TabStateStore', '[TabStateStore] Compression failed, using uncompressed:', error);
            return stateString;
        }
    }

    /**
     * Decompress state from storage
     * @param {string} compressedState - Compressed state
     * @returns {string} Decompressed state
     * @private
     */
    decompressState(compressedState) {
        try {
            return this.simpleDecompress(compressedState);
        } catch (error) {
            console.warn('TabStateStore', '[TabStateStore] Decompression failed:', error);
            throw error;
        }
    }

    /**
     * Check if stored data is compressed
     * @param {string} data - Data to check
     * @returns {boolean} True if compressed
     * @private
     */
    isCompressed(data) {
        // Simple heuristic: compressed data starts with special marker
        return data.startsWith('COMPRESSED:');
    }

    /**
     * Simple compression implementation
     * @param {string} str - String to compress
     * @returns {string} Compressed string
     * @private
     */
    simpleCompress(str) {
        // Very basic compression - just remove extra whitespace and add marker
        const compressed = str.replace(/\s+/g, ' ').trim();
        return `COMPRESSED:${compressed}`;
    }

    /**
     * Simple decompression implementation
     * @param {string} str - String to decompress
     * @returns {string} Decompressed string
     * @private
     */
    simpleDecompress(str) {
        if (!str.startsWith('COMPRESSED:')) {
            return str;
        }
        return str.substring(11); // Remove 'COMPRESSED:' prefix
    }

    /**
     * Start auto-save timer
     * @private
     */
    startAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }
        
        this.autoSaveTimer = setInterval(() => {
            // Get current state from tab system if available
            if (window.simplifiedTabSystem) {
                const tabManager = window.simplifiedTabSystem.tabManager;
                if (tabManager) {
                    this.saveState(tabManager.tabs, tabManager.activeTabId);
                }
            }
        }, this.autoSaveInterval);
        
        console.log('TabStateStore', '[TabStateStore] Auto-save started', );
    }

    /**
     * Stop auto-save timer
     * @private
     */
    stopAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
            console.log('TabStateStore', '[TabStateStore] Auto-save stopped', );
        }
    }

    /**
     * Get storage statistics
     * @returns {Object} Storage statistics
     */
    getStats() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (!stored) {
                return {
                    hasData: false,
                    size: 0,
                    compressed: false
                };
            }
            
            const isCompressed = this.isCompressed(stored);
            let tabCount = 0;
            
            try {
                const stateString = isCompressed ? this.decompressState(stored) : stored;
                const state = JSON.parse(stateString);
                tabCount = state.tabs ? state.tabs.length : 0;
            } catch (error) {
                // Ignore parsing errors for stats
            }
            
            return {
                hasData: true,
                size: stored.length,
                compressed: isCompressed,
                tabCount,
                storageKey: this.storageKey
            };
            
        } catch (error) {
            console.error('TabStateStore', '[TabStateStore] Error getting stats:', error);
            return {
                hasData: false,
                size: 0,
                compressed: false,
                error: error.message
            };
        }
    }

    /**
     * Export state as JSON
     * @returns {string|null} Exported state JSON
     */
    exportState() {
        try {
            const state = this.loadState();
            if (!state) {
                return null;
            }
            
            return JSON.stringify({
                ...state,
                exportedAt: new Date().toISOString(),
                version: '2.0.0'
            }, null, 2);
            
        } catch (error) {
            console.error('TabStateStore', '[TabStateStore] Error exporting state:', error);
            return null;
        }
    }

    /**
     * Import state from JSON
     * @param {string} stateJson - State JSON string
     * @returns {boolean} Success status
     */
    importState(stateJson) {
        try {
            const state = JSON.parse(stateJson);
            
            if (!this.validateState(state)) {
                throw new Error('Invalid state structure');
            }
            
            // Save imported state
            localStorage.setItem(this.storageKey, JSON.stringify(state));
            
            console.log('TabStateStore', '[TabStateStore] State imported successfully', );
            return true;
            
        } catch (error) {
            console.error('TabStateStore', '[TabStateStore] Error importing state:', error);
            return false;
        }
    }
}

console.log('TabStateStore', '[TabStateStore] Class defined successfully', );