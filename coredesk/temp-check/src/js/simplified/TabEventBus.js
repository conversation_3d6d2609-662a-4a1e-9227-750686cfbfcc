/**
 * TabEventBus.js (4/5)
 * Event management system for the SimplifiedTabSystem
 * Provides decoupled communication between tab system components
 */

class TabEventBus {
    constructor() {
        this.events = new Map();
        this.onceEvents = new Map();
        this.middleware = [];
        this.eventHistory = [];
        this.maxHistorySize = 100;
        this.debugMode = false;
        
        this.initialize();
    }

    /**
     * Initialize the event bus
     */
    initialize() {
        console.log('TabEventBus', '[TabEventBus] Initializing...', );
        
        // Enable debug mode in development
        if (typeof process !== 'undefined' && process.argv?.includes('--dev')) {
            this.debugMode = true;
        }
        
        // Set up global error handling
        this.setupErrorHandling();
        
        console.log('TabEventBus', '[TabEventBus] Initialized successfully', );
    }

    /**
     * Add event listener
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     * @param {Object} options - Event options
     * @returns {string} Listener ID for removal
     */
    on(event, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('Callback must be a function');
        }

        if (!this.events.has(event)) {
            this.events.set(event, []);
        }

        const listener = {
            id: this.generateListenerId(),
            callback,
            options,
            addedAt: new Date().toISOString()
        };

        this.events.get(event).push(listener);

        if (this.debugMode) {
            console.log("Component", `[TabEventBus] Listener added for event: ${event}`);
        }

        return listener.id;
    }

    /**
     * Add one-time event listener
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     * @param {Object} options - Event options
     * @returns {string} Listener ID
     */
    once(event, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('Callback must be a function');
        }

        if (!this.onceEvents.has(event)) {
            this.onceEvents.set(event, []);
        }

        const listener = {
            id: this.generateListenerId(),
            callback,
            options,
            addedAt: new Date().toISOString()
        };

        this.onceEvents.get(event).push(listener);

        if (this.debugMode) {
            console.log("Component", `[TabEventBus] One-time listener added for event: ${event}`);
        }

        return listener.id;
    }

    /**
     * Remove event listener
     * @param {string} event - Event name
     * @param {string|Function} listenerIdOrCallback - Listener ID or callback function
     * @returns {boolean} True if listener was removed
     */
    off(event, listenerIdOrCallback) {
        let removed = false;

        // Remove from regular events
        const listeners = this.events.get(event);
        if (listeners) {
            const index = listeners.findIndex(listener => 
                listener.id === listenerIdOrCallback || listener.callback === listenerIdOrCallback
            );
            
            if (index !== -1) {
                listeners.splice(index, 1);
                removed = true;
            }
        }

        // Remove from once events
        const onceListeners = this.onceEvents.get(event);
        if (onceListeners) {
            const index = onceListeners.findIndex(listener => 
                listener.id === listenerIdOrCallback || listener.callback === listenerIdOrCallback
            );
            
            if (index !== -1) {
                onceListeners.splice(index, 1);
                removed = true;
            }
        }

        if (removed && this.debugMode) {
            console.log("Component", `[TabEventBus] Listener removed for event: ${event}`);
        }

        return removed;
    }

    /**
     * Emit an event
     * @param {string} event - Event name
     * @param {*} data - Event data
     * @param {Object} options - Emit options
     * @returns {Promise<Array>} Array of callback results
     */
    async emit(event, data = null, options = {}) {
        const eventData = {
            event,
            data,
            timestamp: new Date().toISOString(),
            id: this.generateEventId()
        };

        // Add to history
        this.addToHistory(eventData);

        if (this.debugMode) {
            console.log(`[TabEventBus] Emitting event: ${event}`, data);
        }

        const results = [];

        try {
            // Apply middleware
            const processedData = await this.applyMiddleware(eventData);
            
            // Execute regular listeners
            await this.executeListeners(event, processedData, results);
            
            // Execute and remove once listeners
            await this.executeOnceListeners(event, processedData, results);

            // Emit to global event system if available
            if (window.CoreDeskEvents && options.global !== false) {
                window.CoreDeskEvents.emit(`tab:${event}`, processedData);
            }

        } catch (error) {
            console.error(`[TabEventBus] Error emitting event ${event}:`, error);
            
            // Emit error event
            this.emit('error', {
                originalEvent: event,
                error: error.message,
                data
            }, { global: false });
        }

        return results;
    }

    /**
     * Execute regular event listeners
     * @param {string} event - Event name
     * @param {Object} eventData - Event data
     * @param {Array} results - Results array to push to
     * @private
     */
    async executeListeners(event, eventData, results) {
        const listeners = this.events.get(event) || [];
        
        for (const listener of listeners) {
            try {
                const result = await this.executeListener(listener, eventData);
                results.push(result);
            } catch (error) {
                console.error(`[TabEventBus] Error in listener for ${event}:`, error);
                results.push({ error: error.message });
            }
        }
    }

    /**
     * Execute and remove once listeners
     * @param {string} event - Event name
     * @param {Object} eventData - Event data
     * @param {Array} results - Results array to push to
     * @private
     */
    async executeOnceListeners(event, eventData, results) {
        const onceListeners = this.onceEvents.get(event) || [];
        
        // Clear once listeners before executing
        this.onceEvents.set(event, []);
        
        for (const listener of onceListeners) {
            try {
                const result = await this.executeListener(listener, eventData);
                results.push(result);
            } catch (error) {
                console.error(`[TabEventBus] Error in once listener for ${event}:`, error);
                results.push({ error: error.message });
            }
        }
    }

    /**
     * Execute a single listener
     * @param {Object} listener - Listener object
     * @param {Object} eventData - Event data
     * @returns {Promise<*>} Listener result
     * @private
     */
    async executeListener(listener, eventData) {
        const { callback, options } = listener;
        
        // Check if listener should be throttled
        if (options.throttle && this.isThrottled(listener, options.throttle)) {
            return { throttled: true };
        }
        
        // Execute callback
        const result = callback(eventData);
        
        // Handle async callbacks
        if (result instanceof Promise) {
            return await result;
        }
        
        return result;
    }

    /**
     * Check if listener is throttled
     * @param {Object} listener - Listener object
     * @param {number} throttleMs - Throttle time in milliseconds
     * @returns {boolean} True if throttled
     * @private
     */
    isThrottled(listener, throttleMs) {
        const now = Date.now();
        const lastExecution = listener.lastExecution || 0;
        
        if (now - lastExecution < throttleMs) {
            return true;
        }
        
        listener.lastExecution = now;
        return false;
    }

    /**
     * Apply middleware to event data
     * @param {Object} eventData - Event data
     * @returns {Promise<Object>} Processed event data
     * @private
     */
    async applyMiddleware(eventData) {
        let processedData = eventData;
        
        for (const middleware of this.middleware) {
            try {
                const result = await middleware(processedData);
                if (result !== undefined) {
                    processedData = result;
                }
            } catch (error) {
                console.error('TabEventBus', '[TabEventBus] Middleware error:', error);
            }
        }
        
        return processedData;
    }

    /**
     * Add middleware function
     * @param {Function} middleware - Middleware function
     */
    addMiddleware(middleware) {
        if (typeof middleware !== 'function') {
            throw new Error('Middleware must be a function');
        }
        
        this.middleware.push(middleware);
        
        if (this.debugMode) {
            console.log('TabEventBus', '[TabEventBus] Middleware added', );
        }
    }

    /**
     * Remove middleware function
     * @param {Function} middleware - Middleware function to remove
     * @returns {boolean} True if middleware was removed
     */
    removeMiddleware(middleware) {
        const index = this.middleware.indexOf(middleware);
        if (index !== -1) {
            this.middleware.splice(index, 1);
            
            if (this.debugMode) {
                console.log('TabEventBus', '[TabEventBus] Middleware removed', );
            }
            
            return true;
        }
        
        return false;
    }

    /**
     * Wait for a specific event
     * @param {string} event - Event name
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise} Promise that resolves with event data
     */
    waitFor(event, timeout = 5000) {
        return new Promise((resolve, reject) => {
            let timeoutId;
            
            const cleanup = () => {
                if (timeoutId) clearTimeout(timeoutId);
            };
            
            const listener = (eventData) => {
                cleanup();
                resolve(eventData);
            };
            
            this.once(event, listener);
            
            if (timeout > 0) {
                timeoutId = setTimeout(() => {
                    this.off(event, listener);
                    reject(new Error(`Event ${event} timeout after ${timeout}ms`));
                }, timeout);
            }
        });
    }

    /**
     * Get all listeners for an event
     * @param {string} event - Event name
     * @returns {Array} Array of listeners
     */
    getListeners(event) {
        const regular = this.events.get(event) || [];
        const once = this.onceEvents.get(event) || [];
        
        return [...regular, ...once];
    }

    /**
     * Remove all listeners for an event
     * @param {string} event - Event name (optional)
     */
    removeAllListeners(event) {
        if (event) {
            this.events.delete(event);
            this.onceEvents.delete(event);
            
            if (this.debugMode) {
                console.log("Component", `[TabEventBus] All listeners removed for event: ${event}`);
            }
        } else {
            this.events.clear();
            this.onceEvents.clear();
            
            if (this.debugMode) {
                console.log('TabEventBus', '[TabEventBus] All listeners removed', );
            }
        }
    }

    /**
     * Get event history
     * @param {number} limit - Maximum number of events to return
     * @returns {Array} Array of event history
     */
    getHistory(limit = 50) {
        return this.eventHistory.slice(-limit);
    }

    /**
     * Clear event history
     */
    clearHistory() {
        this.eventHistory = [];
        
        if (this.debugMode) {
            console.log('TabEventBus', '[TabEventBus] Event history cleared', );
        }
    }

    /**
     * Add event to history
     * @param {Object} eventData - Event data
     * @private
     */
    addToHistory(eventData) {
        this.eventHistory.push(eventData);
        
        // Maintain history size limit
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }

    /**
     * Set up global error handling
     * @private
     */
    setupErrorHandling() {
        // Handle uncaught errors in listeners
        this.on('error', (eventData) => {
            console.error('TabEventBus', '[TabEventBus] Event system error:', eventData);
            
            // Log error for debugging
            window.Logger?.ui.error('Tab event system error', eventData);
        });
    }

    /**
     * Generate unique listener ID
     * @returns {string} Unique ID
     * @private
     */
    generateListenerId() {
        return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Generate unique event ID
     * @returns {string} Unique ID
     * @private
     */
    generateEventId() {
        return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Get event bus statistics
     * @returns {Object} Statistics
     */
    getStats() {
        const regularListeners = Array.from(this.events.values())
            .reduce((total, listeners) => total + listeners.length, 0);
        
        const onceListeners = Array.from(this.onceEvents.values())
            .reduce((total, listeners) => total + listeners.length, 0);
        
        return {
            totalEvents: this.events.size + this.onceEvents.size,
            regularListeners,
            onceListeners,
            totalListeners: regularListeners + onceListeners,
            middleware: this.middleware.length,
            historySize: this.eventHistory.length,
            debugMode: this.debugMode
        };
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode is enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log("Component", `[TabEventBus] Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }
}

console.log('TabEventBus', '[TabEventBus] Class defined successfully', );