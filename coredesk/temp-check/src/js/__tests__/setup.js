/**
 * Jest Setup File
 * Global test configuration and mocks for CoreDesk test suite
 */

// Mock localStorage for Node.js environment
global.localStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};

// Mock navigator for Node.js environment
global.navigator = {
    userAgent: 'Jest Test Environment'
};

// Mock window for Node.js environment
global.window = {
    location: {
        href: 'http://localhost:3000/test'
    },
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
};

// Mock document for Node.js environment
global.document = {
    querySelector: jest.fn(),
    querySelectorAll: jest.fn(() => []),
    createElement: jest.fn(() => ({
        setAttribute: jest.fn(),
        getAttribute: jest.fn(),
        removeAttribute: jest.fn(),
        hasAttribute: jest.fn(() => false),
        appendChild: jest.fn(),
        remove: jest.fn()
    })),
    head: {
        appendChild: jest.fn()
    },
    body: {
        appendChild: jest.fn()
    },
    addEventListener: jest.fn()
};

// Mock process.env
process.env.NODE_ENV = 'test';
process.env.API_BASE_URL = 'http://localhost:3010/v1';

// Global test utilities
global.testUtils = {
    createMockError: (message, type = 'Error') => {
        const error = new Error(message);
        error.name = type;
        return error;
    },
    
    createMockFile: (name, size = 1024, type = 'text/plain') => ({
        name,
        size,
        type,
        lastModified: Date.now()
    }),
    
    sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms))
};

// Console suppression for cleaner test output
const originalConsole = global.console;
global.console = {
    ...originalConsole,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
};

// Restore console for debugging when needed
global.console.debug = originalConsole.log;