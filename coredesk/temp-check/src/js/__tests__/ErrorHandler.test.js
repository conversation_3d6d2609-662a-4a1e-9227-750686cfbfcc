/**
 * ErrorHandler.test.js
 * Comprehensive unit tests for the ErrorHandler class
 */

const { <PERSON>rror<PERSON>and<PERSON>, errorHandler } = require('../utils/ErrorHandler');

describe('ErrorHandler', () => {
    let handler;
    
    beforeEach(() => {
        handler = new ErrorHandler();
        // Clear localStorage mock calls
        jest.clearAllMocks();
    });

    describe('Constructor and Initialization', () => {
        test('should create ErrorHandler instance with correct properties', () => {
            expect(handler).toBeInstanceOf(ErrorHandler);
            expect(handler.errorTypes).toBeDefined();
            expect(handler.errorCodes).toBeDefined();
            expect(typeof handler.handleError).toBe('function');
        });

        test('should have all required error types', () => {
            const expectedTypes = ['NETWORK', 'AUTH', 'VALIDATION', 'FIREBASE', 'LICENSE', 'SYNC', 'STORAGE', 'UNKNOWN'];
            expectedTypes.forEach(type => {
                expect(handler.errorTypes[type]).toBeDefined();
            });
        });

        test('should have comprehensive error codes', () => {
            expect(handler.errorCodes.NETWORK_TIMEOUT).toBe('E001');
            expect(handler.errorCodes.AUTH_INVALID_CREDENTIALS).toBe('E101');
            expect(handler.errorCodes.LICENSE_INVALID).toBe('E401');
            expect(handler.errorCodes.STORAGE_FULL).toBe('E601');
        });
    });

    describe('Error Processing', () => {
        test('should process Error objects correctly', () => {
            const error = new Error('Test error message');
            error.name = 'TestError';
            
            const processed = handler.processError(error, 'test-context', { userId: 123 });
            
            expect(processed.id).toMatch(/^ERR_/);
            expect(processed.name).toBe('TestError');
            expect(processed.message).toBe('Test error message');
            expect(processed.context).toBe('test-context');
            expect(processed.metadata.userId).toBe(123);
            expect(processed.stack).toBeDefined();
            expect(processed.timestamp).toBeDefined();
        });

        test('should process plain objects as errors', () => {
            const errorObj = {
                name: 'CustomError',
                message: 'Custom error message',
                code: 'CUSTOM_001'
            };
            
            const processed = handler.processError(errorObj, 'test-context');
            
            expect(processed.name).toBe('CustomError');
            expect(processed.message).toBe('Custom error message');
            expect(processed.code).toBe('CUSTOM_001');
        });

        test('should handle string errors', () => {
            const processed = handler.processError('Simple error string', 'test-context');
            
            expect(processed.name).toBe('UnknownError');
            expect(processed.message).toBe('Simple error string');
            expect(processed.code).toBe('E999');
        });

        test('should handle null/undefined errors', () => {
            const processed = handler.processError(null, 'test-context');
            
            expect(processed.name).toBe('UnknownError');
            expect(processed.message).toBe('null');
        });
    });

    describe('Error Type Determination', () => {
        test('should detect network errors', () => {
            const networkError = new Error('Network timeout occurred');
            const type = handler.determineErrorType(networkError);
            expect(type).toBe(handler.errorTypes.NETWORK);
        });

        test('should detect authentication errors', () => {
            const authError = new Error('Authentication failed');
            const type = handler.determineErrorType(authError);
            expect(type).toBe(handler.errorTypes.AUTH);
        });

        test('should detect Firebase errors', () => {
            const firebaseError = new Error('Firebase connection failed');
            firebaseError.name = 'FirebaseError';
            const type = handler.determineErrorType(firebaseError);
            expect(type).toBe(handler.errorTypes.FIREBASE);
        });

        test('should detect license errors', () => {
            const licenseError = new Error('License validation failed');
            const type = handler.determineErrorType(licenseError);
            expect(type).toBe(handler.errorTypes.LICENSE);
        });

        test('should default to unknown for unrecognized errors', () => {
            const unknownError = new Error('Some random error');
            const type = handler.determineErrorType(unknownError);
            expect(type).toBe(handler.errorTypes.UNKNOWN);
        });
    });

    describe('Error Code Determination', () => {
        test('should determine specific error codes', () => {
            const timeoutError = new Error('Network timeout');
            expect(handler.determineErrorCode(timeoutError)).toBe(handler.errorCodes.NETWORK_TIMEOUT);

            const credentialsError = new Error('Invalid credentials provided');
            expect(handler.determineErrorCode(credentialsError)).toBe(handler.errorCodes.AUTH_INVALID_CREDENTIALS);

            const licenseError = new Error('Invalid license key');
            expect(handler.determineErrorCode(licenseError)).toBe(handler.errorCodes.LICENSE_INVALID);
        });

        test('should default to E999 for unknown error types', () => {
            const unknownError = new Error('Unknown error type');
            expect(handler.determineErrorCode(unknownError)).toBe('E999');
        });
    });

    describe('User-Friendly Messages', () => {
        test('should provide Spanish error messages', () => {
            const errorInfo = { code: handler.errorCodes.NETWORK_TIMEOUT };
            const message = handler.getUserFriendlyMessage(errorInfo);
            
            expect(message).toContain('conexión');
            expect(message).toContain('internet');
        });

        test('should provide default message for unknown codes', () => {
            const errorInfo = { code: 'UNKNOWN_CODE' };
            const message = handler.getUserFriendlyMessage(errorInfo);
            
            expect(message).toBe('Ha ocurrido un error inesperado. Por favor, inténtalo de nuevo.');
        });
    });

    describe('Severity Determination', () => {
        test('should assign critical severity to auth and license errors', () => {
            const authError = { type: handler.errorTypes.AUTH };
            expect(handler.determineSeverity(authError)).toBe('critical');

            const licenseError = { type: handler.errorTypes.LICENSE };
            expect(handler.determineSeverity(licenseError)).toBe('critical');
        });

        test('should assign warning severity to network and sync errors', () => {
            const networkError = { type: handler.errorTypes.NETWORK };
            expect(handler.determineSeverity(networkError)).toBe('warning');

            const syncError = { type: handler.errorTypes.SYNC };
            expect(handler.determineSeverity(syncError)).toBe('warning');
        });

        test('should assign info severity to other error types', () => {
            const validationError = { type: handler.errorTypes.VALIDATION };
            expect(handler.determineSeverity(validationError)).toBe('info');
        });
    });

    describe('Recovery Suggestions', () => {
        test('should provide specific suggestions for known error codes', () => {
            const timeoutError = { code: handler.errorCodes.NETWORK_TIMEOUT };
            const suggestions = handler.getRecoverySuggestions(timeoutError);
            
            expect(Array.isArray(suggestions)).toBe(true);
            expect(suggestions.length).toBeGreaterThan(0);
            expect(suggestions[0]).toContain('conexión');
        });

        test('should provide default suggestions for unknown codes', () => {
            const unknownError = { code: 'UNKNOWN_CODE' };
            const suggestions = handler.getRecoverySuggestions(unknownError);
            
            expect(suggestions).toContain('Inténtalo de nuevo');
            expect(suggestions).toContain('Reinicia la aplicación si el problema persiste');
        });
    });

    describe('Local Storage Integration', () => {
        test('should store errors in localStorage', () => {
            localStorage.getItem.mockReturnValue('[]');
            
            const errorInfo = {
                id: 'TEST_001',
                timestamp: '2024-01-01T00:00:00.000Z',
                type: 'test',
                code: 'T001',
                message: 'Test error',
                context: 'test',
                severity: 'info'
            };
            
            handler.storeErrorLocally(errorInfo);
            
            expect(localStorage.getItem).toHaveBeenCalledWith('coredesk_error_log');
            expect(localStorage.setItem).toHaveBeenCalledWith('coredesk_error_log', expect.any(String));
        });

        test('should limit error log to 50 entries', () => {
            // Create array with 50 existing errors
            const existingErrors = Array(50).fill(null).map((_, i) => ({ id: `ERR_${i}` }));
            localStorage.getItem.mockReturnValue(JSON.stringify(existingErrors));
            
            const newError = {
                id: 'NEW_ERROR',
                timestamp: '2024-01-01T00:00:00.000Z',
                type: 'test',
                code: 'T001',
                message: 'New error',
                context: 'test',
                severity: 'info'
            };
            
            handler.storeErrorLocally(newError);
            
            const savedData = JSON.parse(localStorage.setItem.mock.calls[0][1]);
            expect(savedData.length).toBe(50); // Should still be 50
            expect(savedData[savedData.length - 1].id).toBe('NEW_ERROR'); // New error at end
        });
    });

    describe('Error Statistics', () => {
        test('should calculate error statistics correctly', () => {
            const mockErrors = [
                { type: 'network', severity: 'warning' },
                { type: 'network', severity: 'warning' },
                { type: 'auth', severity: 'critical' }
            ];
            localStorage.getItem.mockReturnValue(JSON.stringify(mockErrors));
            
            const stats = handler.getErrorStats();
            
            expect(stats.total).toBe(3);
            expect(stats.byType.network).toBe(2);
            expect(stats.byType.auth).toBe(1);
            expect(stats.bySeverity.warning).toBe(2);
            expect(stats.bySeverity.critical).toBe(1);
            expect(stats.recent.length).toBe(3);
        });

        test('should handle localStorage errors gracefully', () => {
            localStorage.getItem.mockImplementation(() => {
                throw new Error('localStorage error');
            });
            
            const stats = handler.getErrorStats();
            
            expect(stats).toEqual({ total: 0, byType: {}, bySeverity: {}, recent: [] });
        });
    });

    describe('Retry Wrapper', () => {
        test('should create retry wrapper that succeeds on first attempt', async () => {
            const mockFn = jest.fn().mockResolvedValue('success');
            const wrappedFn = handler.createRetryWrapper(mockFn);
            
            const result = await wrappedFn('arg1', 'arg2');
            
            expect(result).toBe('success');
            expect(mockFn).toHaveBeenCalledTimes(1);
            expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
        });

        test('should retry on failure and eventually succeed', async () => {
            const mockFn = jest.fn()
                .mockRejectedValueOnce(new Error('First failure'))
                .mockRejectedValueOnce(new Error('Second failure'))
                .mockResolvedValue('success');
                
            const wrappedFn = handler.createRetryWrapper(mockFn, { maxRetries: 3 });
            
            const result = await wrappedFn();
            
            expect(result).toBe('success');
            expect(mockFn).toHaveBeenCalledTimes(3);
        });

        test('should fail after max retries', async () => {
            const mockFn = jest.fn().mockRejectedValue(new Error('Always fails'));
            const wrappedFn = handler.createRetryWrapper(mockFn, { maxRetries: 2 });
            
            await expect(wrappedFn()).rejects.toEqual(expect.objectContaining({
                message: 'Always fails'
            }));
            expect(mockFn).toHaveBeenCalledTimes(2);
        });

        test('should respect retry condition', async () => {
            const mockFn = jest.fn().mockRejectedValue(new Error('Non-retryable error'));
            const retryCondition = (error) => !error.message.includes('Non-retryable');
            const wrappedFn = handler.createRetryWrapper(mockFn, { retryCondition });
            
            await expect(wrappedFn()).rejects.toEqual(expect.objectContaining({
                message: 'Non-retryable error'
            }));
            expect(mockFn).toHaveBeenCalledTimes(1); // Should not retry
        });
    });

    describe('Main handleError Method', () => {
        test('should process, log, and report errors', () => {
            const spyProcess = jest.spyOn(handler, 'processError');
            const spyLog = jest.spyOn(handler, 'logError');
            const spyReport = jest.spyOn(handler, 'reportError');
            
            const error = new Error('Test error');
            const result = handler.handleError(error, 'test-context', { meta: 'data' });
            
            expect(spyProcess).toHaveBeenCalledWith(error, 'test-context', { meta: 'data' });
            expect(spyLog).toHaveBeenCalledWith(result);
            expect(spyReport).toHaveBeenCalledWith(result);
            expect(result).toHaveProperty('id');
            expect(result).toHaveProperty('userMessage');
            expect(result).toHaveProperty('recovery');
        });
    });

    describe('Singleton Instance', () => {
        test('should export singleton errorHandler instance', () => {
            expect(errorHandler).toBeInstanceOf(ErrorHandler);
        });
    });

    describe('Error ID Generation', () => {
        test('should generate unique error IDs', () => {
            const id1 = handler.generateErrorId();
            const id2 = handler.generateErrorId();
            
            expect(id1).toMatch(/^ERR_[A-Z0-9_]+$/);
            expect(id2).toMatch(/^ERR_[A-Z0-9_]+$/);
            expect(id1).not.toBe(id2);
        });
    });

    describe('Clear Error Log', () => {
        test('should clear error log from localStorage', () => {
            handler.clearErrorLog();
            expect(localStorage.removeItem).toHaveBeenCalledWith('coredesk_error_log');
        });
    });

    describe('Log Level Mapping', () => {
        test('should map severity to correct console methods', () => {
            expect(handler.getLogLevel('critical')).toBe('error');
            expect(handler.getLogLevel('warning')).toBe('warn');
            expect(handler.getLogLevel('info')).toBe('info');
            expect(handler.getLogLevel('unknown')).toBe('error');
        });
    });
});