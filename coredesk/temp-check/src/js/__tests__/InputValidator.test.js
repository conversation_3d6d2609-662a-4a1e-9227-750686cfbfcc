/**
 * InputValidator.test.js
 * Comprehensive unit tests for the InputValidator class
 */

const { InputValidator, inputValidator } = require('../utils/InputValidator');

// Mock the ErrorHandler
jest.mock('../utils/ErrorHandler', () => ({
    errorHandler: {
        handleError: jest.fn()
    }
}));

describe('InputValidator', () => {
    let validator;
    
    beforeEach(() => {
        validator = new InputValidator();
        jest.clearAllMocks();
    });

    describe('Constructor and Initialization', () => {
        test('should create InputValidator instance with correct properties', () => {
            expect(validator).toBeInstanceOf(InputValidator);
            expect(validator.patterns).toBeDefined();
            expect(validator.maxLengths).toBeDefined();
            expect(validator.dangerousPatterns).toBeDefined();
            expect(validator.securityLimits).toBeDefined();
        });

        test('should initialize rate limiting stores', () => {
            expect(validator.failedAttempts).toBeInstanceOf(Map);
            expect(validator.rateLimitStore).toBeInstanceOf(Map);
        });

        test('should have comprehensive validation patterns', () => {
            expect(validator.patterns.email).toBeInstanceOf(RegExp);
            expect(validator.patterns.licenseKey).toBeInstanceOf(RegExp);
            expect(validator.patterns.url).toBeInstanceOf(RegExp);
            expect(validator.patterns.fileName).toBeInstanceOf(RegExp);
        });
    });

    describe('Email Validation', () => {
        test('should validate correct email addresses', () => {
            const validEmails = [
                '<EMAIL>',
                '<EMAIL>',
                'x@y.z',
                '<EMAIL>'
            ];

            validEmails.forEach(email => {
                const result = validator.validateEmail(email);
                expect(result.valid).toBe(true);
                expect(result.sanitized).toBe(email.toLowerCase());
            });
        });

        test('should reject invalid email addresses', () => {
            const invalidEmails = [
                '',
                'invalid',
                '@domain.com',
                'user@',
                '<EMAIL>',
                'user@domain',
                'a'.repeat(255) + '@domain.com' // too long
            ];

            invalidEmails.forEach(email => {
                const result = validator.validateEmail(email);
                expect(result.valid).toBe(false);
                expect(result.error).toBeDefined();
            });
        });

        test('should detect dangerous content in emails', () => {
            const dangerousEmails = [
                '<EMAIL><script>alert(1)</script>',
                '<EMAIL> javascript:void(0)',
                '<EMAIL>\'; DROP TABLE users; --'
            ];

            dangerousEmails.forEach(email => {
                const result = validator.validateEmail(email);
                expect(result.valid).toBe(false);
                // Most will fail on format validation first
                expect(result.error).toBeDefined();
            });
        });

        test('should handle null/undefined email inputs', () => {
            expect(validator.validateEmail(null).valid).toBe(false);
            expect(validator.validateEmail(undefined).valid).toBe(false);
            expect(validator.validateEmail('').valid).toBe(false);
        });

        test('should detect suspicious patterns', () => {
            const suspiciousEmail = 'a'.repeat(20) + '@domain.com';
            const result = validator.validateEmail(suspiciousEmail);
            expect(result.valid).toBe(false);
            expect(result.error).toContain('suspicious');
        });
    });

    describe('Password Validation', () => {
        test('should validate strong passwords', () => {
            const strongPasswords = [
                'Password123!',
                'MyStr0ng#Pass',
                'C0mplex$Password',
                'Test@Pass123'
            ];

            strongPasswords.forEach(password => {
                const result = validator.validatePassword(password);
                expect(result.valid).toBe(true);
            });
        });

        test('should reject weak passwords', () => {
            const weakPasswords = [
                '',
                'short',
                'password', // no uppercase, numbers, special
                'PASSWORD123', // no lowercase
                'password123', // no uppercase, special
                'Password!', // no numbers
                'a'.repeat(129) // too long
            ];

            weakPasswords.forEach(password => {
                const result = validator.validatePassword(password);
                expect(result.valid).toBe(false);
                expect(result.error).toBeDefined();
            });
        });

        test('should require minimum length', () => {
            const shortPassword = '1234567'; // 7 chars
            const result = validator.validatePassword(shortPassword);
            expect(result.valid).toBe(false);
            expect(result.error).toContain('8 characters');
        });

        test('should require character complexity', () => {
            const simplePassword = 'password';
            const result = validator.validatePassword(simplePassword);
            expect(result.valid).toBe(false);
            expect(result.error).toContain('uppercase, lowercase, number, and special character');
        });
    });

    describe('License Key Validation', () => {
        test('should validate correct license key format', () => {
            const validKeys = [
                'ABCD-1234-EFGH-5678',
                'TEST-TEST-TEST-TEST',
                'A1B2-C3D4-E5F6-G7H8'
            ];

            validKeys.forEach(key => {
                const result = validator.validateLicenseKey(key);
                expect(result.valid).toBe(true);
                expect(result.sanitized).toBe(key);
            });
        });

        test('should reject invalid license key formats', () => {
            const invalidKeys = [
                '',
                'ABCD-1234-EFGH', // too short
                'ABCD-1234-EFGH-5678-9ABC', // too long
                'ABCD_1234_EFGH_5678', // wrong separator
                'ABCD-12345-EFGH-5678' // wrong segment length
            ];

            invalidKeys.forEach(key => {
                const result = validator.validateLicenseKey(key);
                expect(result.valid).toBe(false);
                expect(result.error).toBeDefined();
            });
        });

        test('should normalize license keys to uppercase', () => {
            const result = validator.validateLicenseKey('abcd-1234-efgh-5678');
            expect(result.valid).toBe(true); // Should pass after normalization
            expect(result.sanitized).toBe('ABCD-1234-EFGH-5678');
        });
    });

    describe('File Name Validation', () => {
        test('should validate safe file names', () => {
            const validNames = [
                'document.pdf',
                'image.jpg',
                'file_name.txt',
                'data-file.xlsx'
            ];

            validNames.forEach(name => {
                const result = validator.validateFileName(name);
                expect(result.valid).toBe(true);
                expect(result.sanitized).toBe(name);
            });
        });

        test('should reject dangerous file extensions', () => {
            const dangerousFiles = [
                'virus.exe',
                'script.bat',
                'malware.cmd',
                'trojan.scr',
                'payload.vbs'
            ];

            dangerousFiles.forEach(name => {
                const result = validator.validateFileName(name);
                expect(result.valid).toBe(false);
                expect(result.error).toContain('not allowed');
            });
        });

        test('should reject invalid file name patterns', () => {
            const invalidNames = [
                'file<name>.txt',
                'file|name.txt',
                'file?name.txt',
                'file*name.txt',
                'a'.repeat(256) + '.txt' // too long
            ];

            invalidNames.forEach(name => {
                const result = validator.validateFileName(name);
                expect(result.valid).toBe(false);
            });
        });
    });

    describe('URL Validation', () => {
        test('should validate correct URLs', () => {
            const validUrls = [
                'https://example.com',
                'http://test.org/path',
                'https://subdomain.example.com/path?query=value',
                'http://localhost:3000'
            ];

            validUrls.forEach(url => {
                const result = validator.validateUrl(url);
                expect(result.valid).toBe(true);
                expect(result.sanitized).toBe(url);
            });
        });

        test('should reject invalid URLs', () => {
            const invalidUrls = [
                '',
                'invalid-url',
                'ftp://example.com', // wrong protocol
                'javascript:alert(1)', // dangerous protocol
                'data:text/html,<script>alert(1)</script>'
            ];

            invalidUrls.forEach(url => {
                const result = validator.validateUrl(url);
                expect(result.valid).toBe(false);
                expect(result.error).toBeDefined();
            });
        });

        test('should only allow HTTP/HTTPS protocols', () => {
            const result = validator.validateUrl('ftp://example.com');
            expect(result.valid).toBe(false);
            expect(result.error).toContain('HTTP and HTTPS');
        });
    });

    describe('Dangerous Content Detection', () => {
        test('should detect script injection attempts', () => {
            const dangerousInputs = [
                '<script>alert(1)</script>',
                'javascript:void(0)',
                'on' + 'load=alert(1)',
                'eval(' + 'malicious_code)',
                'document.cookie'
            ];

            dangerousInputs.forEach(input => {
                expect(validator.containsDangerousContent(input)).toBe(true);
            });
        });

        test('should detect SQL injection patterns', () => {
            const sqlInjections = [
                "'; DROP TABLE users; --",
                "' UNION SELECT * FROM passwords",
                "' OR '1'='1",
                'admin\' --'
            ];

            sqlInjections.forEach(input => {
                expect(validator.containsDangerousContent(input)).toBe(true);
            });
        });

        test('should detect command injection patterns', () => {
            const commandInjections = [
                'test | rm -rf /',
                'input && malicious_command',
                'data; cat /etc/passwd',
                '$(malicious_command)' // This should be detected
            ];

            commandInjections.forEach(input => {
                expect(validator.containsDangerousContent(input)).toBe(true);
            });
        });

        test('should allow safe content', () => {
            const safeInputs = [
                'Normal text content',
                '<EMAIL>',
                'A safe filename.txt',
                'Regular password123!',
                'https://safe-website.com'
            ];

            safeInputs.forEach(input => {
                expect(validator.containsDangerousContent(input)).toBe(false);
            });
        });
    });

    describe('Text Sanitization', () => {
        test('should sanitize HTML content', () => {
            const htmlInput = '<script>alert(1)</script><p>Safe content</p>';
            const sanitized = validator.sanitizeHtml(htmlInput);
            
            expect(sanitized).not.toContain('<script>');
            expect(sanitized).not.toContain('alert(1)');
            expect(sanitized).toContain('Safe content');
        });

        test('should remove dangerous attributes', () => {
            const htmlInput = '<div onclick="malicious()">Content</div>';
            const sanitized = validator.sanitizeHtml(htmlInput);
            
            expect(sanitized).not.toContain('onclick');
            expect(sanitized).not.toContain('malicious');
        });

        test('should sanitize text input', () => {
            const textInput = '<script>alert(1)</script>"dangerous"\'quotes\'\0null';
            const sanitized = validator.sanitizeText(textInput);
            
            expect(sanitized).not.toContain('<script>');
            expect(sanitized).not.toContain('"');
            expect(sanitized).not.toContain("'");
            expect(sanitized).not.toContain('\0');
        });
    });

    describe('Form Data Validation', () => {
        test('should validate complete form with schema', () => {
            const formData = {
                email: '<EMAIL>',
                password: 'StrongPass123!',
                age: '25',
                agreed: 'true'
            };

            const schema = {
                email: { type: 'email', required: true },
                password: { type: 'password', required: true },
                age: { type: 'number', required: true, min: 18, max: 100 },
                agreed: { type: 'boolean', required: true }
            };

            const result = validator.validateFormData(formData, schema);
            
            expect(result.valid).toBe(true);
            expect(result.errors).toEqual({});
            expect(result.sanitized.email).toBe('<EMAIL>');
            expect(result.sanitized.age).toBe(25);
            expect(result.sanitized.agreed).toBe(true);
        });

        test('should handle validation errors in form', () => {
            const formData = {
                email: 'invalid-email',
                password: 'weak',
                age: '17' // too young
            };

            const schema = {
                email: { type: 'email', required: true },
                password: { type: 'password', required: true },
                age: { type: 'number', required: true, min: 18, max: 100 }
            };

            const result = validator.validateFormData(formData, schema);
            
            expect(result.valid).toBe(false);
            expect(Object.keys(result.errors).length).toBeGreaterThan(0);
            expect(result.errors.email).toBeDefined();
            expect(result.errors.password).toBeDefined();
            expect(result.errors.age).toBeDefined();
        });

        test('should handle missing required fields', () => {
            const formData = {
                email: '<EMAIL>'
                // missing required password
            };

            const schema = {
                email: { type: 'email', required: true },
                password: { type: 'password', required: true }
            };

            const result = validator.validateFormData(formData, schema);
            
            expect(result.valid).toBe(false);
            expect(result.errors.password).toContain('required');
        });
    });

    describe('Rate Limiting', () => {
        test('should allow requests within rate limit', () => {
            const result = validator.validateRateLimit('test-key', 5, 60000);
            expect(result.valid).toBe(true);
        });

        test('should block requests exceeding rate limit', () => {
            const key = 'test-key-limit';
            
            // Make requests up to the limit
            for (let i = 0; i < 5; i++) {
                validator.validateRateLimit(key, 5, 60000);
            }
            
            // This should be blocked
            const result = validator.validateRateLimit(key, 5, 60000);
            expect(result.valid).toBe(false);
            expect(result.error).toContain('Rate limit exceeded');
            expect(result.retryAfter).toBeDefined();
        });

        test('should reset rate limit after time window', () => {
            // Mock Date.now to control time
            const originalNow = Date.now;
            let mockTime = 1000000;
            Date.now = jest.fn(() => mockTime);
            
            const key = 'test-key-reset';
            
            // Fill up the rate limit
            for (let i = 0; i < 5; i++) {
                validator.validateRateLimit(key, 5, 1000); // 1 second window
            }
            
            // Advance time beyond window
            mockTime += 2000;
            
            // Should be allowed again
            const result = validator.validateRateLimit(key, 5, 1000);
            expect(result.valid).toBe(true);
            
            // Restore original Date.now
            Date.now = originalNow;
        });
    });

    describe('File Upload Validation', () => {
        test('should validate acceptable file uploads', () => {
            const validFile = testUtils.createMockFile('document.pdf', 1024 * 1024, 'application/pdf');
            
            // Mock InputValidator methods to return safe results
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.containsDangerousContent.mockReturnValue(false);
            inputValidator.containsMalwareSignatures = jest.fn().mockReturnValue(false);
            
            const result = validator.validateFileUpload(validFile, {
                allowedTypes: ['application/pdf'],
                allowedExtensions: ['.pdf']
            });
            
            expect(result.valid).toBe(true);
            expect(result.sanitized.name).toBeDefined();
        });

        test('should reject files that are too large', () => {
            const largeFile = testUtils.createMockFile('large.pdf', 50 * 1024 * 1024, 'application/pdf');
            
            const result = validator.validateFileUpload(largeFile, {
                maxSize: 10 * 1024 * 1024 // 10MB limit
            });
            
            expect(result.valid).toBe(false);
            expect(result.error).toContain('exceeds limit');
        });

        test('should reject unauthorized file types', () => {
            const executableFile = testUtils.createMockFile('virus.exe', 1024, 'application/octet-stream');
            
            const result = validator.validateFileUpload(executableFile);
            
            expect(result.valid).toBe(false);
            expect(result.error).toContain('not allowed');
        });

        test('should detect potential malware signatures', () => {
            const suspiciousFile = testUtils.createMockFile('autorun.inf', 1024, 'text/plain');
            
            const result = validator.validateFileUpload(suspiciousFile, {
                scanForMalware: true,
                allowedTypes: ['text/plain'],
                allowedExtensions: ['.inf']  // Allow .inf for this test
            });
            
            expect(result.valid).toBe(false);
            expect(result.error).toContain('malicious content');
        });
    });

    describe('Login Attempt Tracking', () => {
        test('should track and allow normal login attempts', () => {
            const result = validator.validateLoginAttempt('<EMAIL>');
            expect(result.valid).toBe(true);
        });

        test('should lock account after multiple failed attempts', () => {
            const email = '<EMAIL>';
            
            // Record multiple failed attempts
            for (let i = 0; i < 5; i++) {
                validator.recordFailedLogin(email);
            }
            
            const result = validator.validateLoginAttempt(email);
            expect(result.valid).toBe(false);
            expect(result.locked).toBe(true);
            expect(result.error).toContain('locked');
        });

        test('should clear failed attempts after successful login', () => {
            const email = '<EMAIL>';
            
            // Record some failed attempts
            validator.recordFailedLogin(email);
            validator.recordFailedLogin(email);
            
            // Clear attempts
            validator.clearFailedAttempts(email);
            
            // Should be allowed
            const result = validator.validateLoginAttempt(email);
            expect(result.valid).toBe(true);
        });
    });

    describe('Special Pattern Detection', () => {
        test('should detect repeated patterns', () => {
            const repeatedPattern = 'abcabcabcabcabcabc'; // 'abc' repeated 6 times
            expect(validator.hasRepeatedPatterns(repeatedPattern, 5)).toBe(true);
            
            const normalText = 'This is normal text';
            expect(validator.hasRepeatedPatterns(normalText, 5)).toBe(false);
        });

        test('should detect homograph attacks', () => {
            // Using Cyrillic characters that look like Latin
            const homographAttack = 'gооgle.com'; // Contains Cyrillic 'о' instead of Latin 'o'
            expect(validator.containsHomographs(homographAttack)).toBe(true);
            
            const normalDomain = 'google.com';
            expect(validator.containsHomographs(normalDomain)).toBe(false);
        });
    });

    describe('API Key Validation', () => {
        test('should validate proper API keys', () => {
            const validKey = 'sk_test_' + 'a'.repeat(40);
            const result = validator.validateApiKey(validKey, { minLength: 32 });
            expect(result.valid).toBe(true);
        });

        test('should reject short API keys', () => {
            const shortKey = 'short_key';
            const result = validator.validateApiKey(shortKey, { minLength: 32 });
            expect(result.valid).toBe(false);
            expect(result.error).toContain('between');
        });

        test('should reject API keys with invalid characters', () => {
            const invalidKey = 'key_with_invalid_chars!@#' + 'a'.repeat(20); // Make it long enough
            const result = validator.validateApiKey(invalidKey);
            expect(result.valid).toBe(false);
            expect(result.error).toContain('invalid characters');
        });
    });

    describe('Security Statistics', () => {
        test('should provide security statistics', () => {
            const stats = validator.getSecurityStats();
            
            expect(stats).toHaveProperty('rateLimitEntries');
            expect(stats).toHaveProperty('failedLoginAttempts');
            expect(stats).toHaveProperty('dangerousPatterns');
            expect(stats).toHaveProperty('securityLimits');
            expect(typeof stats.dangerousPatterns).toBe('number');
        });
    });

    describe('Singleton Instance', () => {
        test('should export singleton inputValidator instance', () => {
            expect(inputValidator).toBeInstanceOf(InputValidator);
        });
    });

    describe('Number and Boolean Validation', () => {
        test('should validate numbers with range constraints', () => {
            expect(validator.validateNumber('25', 18, 65).valid).toBe(true);
            expect(validator.validateNumber('10', 18, 65).valid).toBe(false);
            expect(validator.validateNumber('100', 18, 65).valid).toBe(false);
            expect(validator.validateNumber('not-a-number').valid).toBe(false);
        });

        test('should validate boolean values', () => {
            expect(validator.validateBoolean(true).valid).toBe(true);
            expect(validator.validateBoolean('true').sanitized).toBe(true);
            expect(validator.validateBoolean('false').sanitized).toBe(false);
            expect(validator.validateBoolean('1').sanitized).toBe(true);
            expect(validator.validateBoolean('0').sanitized).toBe(false);
            expect(validator.validateBoolean('invalid').valid).toBe(false);
        });
    });

    describe('JSON Validation', () => {
        test('should validate and parse JSON', () => {
            const validJson = '{"name": "test", "value": 123}';
            const result = validator.validateJson(validJson);
            
            expect(result.valid).toBe(true);
            expect(result.parsed).toEqual({ name: 'test', value: 123 });
        });

        test('should reject invalid JSON', () => {
            const invalidJson = '{"name": "test", "value": }';
            const result = validator.validateJson(invalidJson);
            
            expect(result.valid).toBe(false);
            expect(result.error).toContain('Invalid JSON');
        });

        test('should detect dangerous content in JSON', () => {
            const dangerousJson = '{"script": "<script>alert(1)</script>"}';
            const result = validator.validateJson(dangerousJson);
            
            expect(result.valid).toBe(false);
            expect(result.error).toContain('invalid content');
        });
    });
});