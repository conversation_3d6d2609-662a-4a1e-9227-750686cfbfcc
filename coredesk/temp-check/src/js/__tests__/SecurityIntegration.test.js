/**
 * SecurityIntegration.test.js
 * Integration tests for the complete security system
 * Tests <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InputValidator, and SecurityManager working together
 */

// Import the real implementations (not mocked)
const { <PERSON>rror<PERSON>and<PERSON> } = require('../utils/ErrorHandler');
const { InputValidator } = require('../utils/InputValidator');
const { SecurityManager } = require('../security/SecurityManager');

describe('Security System Integration', () => {
    let errorHandler;
    let inputValidator;
    let securityManager;
    
    beforeEach(() => {
        // Create fresh instances for each test
        errorHandler = new ErrorHandler();
        inputValidator = new InputValidator();
        securityManager = new SecurityManager();
        
        // Clear any previous data
        jest.clearAllMocks();
        localStorage.clear();
    });

    describe('Complete Workflow Integration', () => {
        test('should handle complete email validation with error logging', () => {
            const invalidEmail = 'invalid<script>email';
            
            // Test the complete flow
            const validationResult = inputValidator.validateEmail(invalidEmail);
            expect(validationResult.valid).toBe(false);
            
            // The SecurityManager should also reject this
            const securityResult = securityManager.validateInput(invalidEmail, 'email');
            expect(securityResult.valid).toBe(false);
        });

        test('should handle rate limiting with error reporting', () => {
            const key = 'test-rate-limit';
            
            // Fill up rate limit
            for (let i = 0; i < 10; i++) {
                inputValidator.validateRateLimit(key, 5, 60000);
            }
            
            // Next attempt should be blocked
            const result = inputValidator.validateRateLimit(key, 5, 60000);
            expect(result.valid).toBe(false);
            expect(result.error).toContain('Rate limit exceeded');
        });

        test('should track login attempts across validators', () => {
            const email = '<EMAIL>';
            
            // Record failed attempts via InputValidator
            for (let i = 0; i < 5; i++) {
                inputValidator.recordFailedLogin(email);
            }
            
            // SecurityManager should also show locked status
            const locked = securityManager.isAccountLocked(email);
            expect(locked).toBe(true);
        });

        test('should provide comprehensive security statistics', () => {
            // Add some test data
            inputValidator.recordFailedLogin('<EMAIL>');
            inputValidator.validateRateLimit('test-key', 5, 60000);
            securityManager.logSecurityEvent('test_event', { test: true });
            
            // Get statistics
            const inputStats = inputValidator.getSecurityStats();
            const securityStatus = securityManager.getSecurityStatus();
            
            expect(inputStats.failedLoginAttempts).toBeGreaterThan(0);
            expect(securityStatus.totalEvents).toBeGreaterThan(0);
        });
    });

    describe('Error Handling Integration', () => {
        test('should handle validation errors through ErrorHandler', () => {
            // Force an error in validation
            const originalValidateEmail = inputValidator.validateEmail;
            inputValidator.validateEmail = () => {
                throw new Error('Validation service failed');
            };
            
            // This should be caught and handled gracefully
            const result = securityManager.validateInput('<EMAIL>', 'email');
            expect(result.valid).toBe(false);
            expect(result.error).toBe('Validation failed');
            
            // Restore original method
            inputValidator.validateEmail = originalValidateEmail;
        });

        test('should create retry wrapper for failing operations', () => {
            let attempts = 0;
            const unreliableFunction = async () => {
                attempts++;
                if (attempts < 3) {
                    throw new Error(`Attempt ${attempts} failed`);
                }
                return 'success';
            };
            
            const wrappedFunction = errorHandler.createRetryWrapper(unreliableFunction);
            
            return wrappedFunction().then(result => {
                expect(result).toBe('success');
                expect(attempts).toBe(3);
            });
        });

        test('should log errors to localStorage and maintain size limits', () => {
            // Create many errors to test size limiting
            for (let i = 0; i < 52; i++) {
                errorHandler.handleError(new Error(`Test error ${i}`), 'test-context');
            }
            
            const stats = errorHandler.getErrorStats();
            expect(stats.total).toBe(50); // Should be limited to 50
        });
    });

    describe('Security Event Flow', () => {
        test('should detect and respond to security threats', () => {
            const threatPattern = 'login_failed';
            const identifier = '<EMAIL>';
            
            // Generate threat pattern (6 attempts to trigger anomaly)
            for (let i = 0; i < 6; i++) {
                securityManager.logSecurityEvent(threatPattern, { identifier });
            }
            
            // Check that pattern was detected
            const patterns = securityManager.threatDetection.suspiciousPatterns;
            const key = `${threatPattern}_${identifier}`;
            expect(patterns.has(key)).toBe(true);
        });

        test('should integrate with ErrorHandler for high-severity events', () => {
            const originalHandleError = errorHandler.handleError;
            const handleErrorSpy = jest.fn(originalHandleError.bind(errorHandler));
            errorHandler.handleError = handleErrorSpy;
            
            // Log a high-severity security event
            securityManager.logSecurityEvent('xss_attempt', { script: '<script>alert(1)</script>' });
            
            // Should have called ErrorHandler
            expect(handleErrorSpy).toHaveBeenCalledWith(
                expect.any(Error),
                'security-event',
                expect.objectContaining({ eventType: 'xss_attempt' })
            );
            
            // Restore original method
            errorHandler.handleError = originalHandleError;
        });
    });

    describe('File Upload Security Chain', () => {
        test('should validate files through complete security chain', () => {
            const testFile = {
                name: 'test-document.pdf',
                size: 1024 * 1024, // 1MB
                type: 'application/pdf'
            };
            
            // Test through InputValidator
            const inputResult = inputValidator.validateFileUpload(testFile, {
                allowedTypes: ['application/pdf'],
                allowedExtensions: ['.pdf']
            });
            
            // Test through SecurityManager
            const securityResult = securityManager.validateFileUpload(testFile);
            
            // Both should validate successfully
            expect(inputResult.valid).toBe(true);
            expect(securityResult.valid).toBe(true);
        });

        test('should reject malicious files consistently', () => {
            const maliciousFile = {
                name: 'virus.exe',
                size: 1024,
                type: 'application/octet-stream'
            };
            
            // Both validators should reject
            const inputResult = inputValidator.validateFileUpload(maliciousFile);
            const securityResult = securityManager.validateFileUpload(maliciousFile);
            
            expect(inputResult.valid).toBe(false);
            expect(securityResult.valid).toBe(false);
        });
    });

    describe('Performance and Resource Management', () => {
        test('should cleanup old security events automatically', () => {
            // Mock Date.now to control time
            const originalNow = Date.now;
            let mockTime = 1000000;
            Date.now = jest.fn(() => mockTime);
            
            // Add events
            securityManager.logSecurityEvent('old_event', { timestamp: mockTime });
            
            // Advance time by 2 hours
            mockTime += 2 * 60 * 60 * 1000;
            
            // Trigger cleanup
            securityManager.cleanupSecurityEvents();
            
            // Old events should be removed
            expect(securityManager.securityEvents.length).toBe(0);
            
            // Restore original Date.now
            Date.now = originalNow;
        });

        test('should handle high-volume security events efficiently', () => {
            const startTime = Date.now();
            
            // Log many events
            for (let i = 0; i < 100; i++) {
                securityManager.logSecurityEvent('performance_test', { index: i });
            }
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            // Should complete within reasonable time (less than 1 second)
            expect(duration).toBeLessThan(1000);
            expect(securityManager.securityEvents.length).toBe(100);
        });
    });

    describe('Data Sanitization and Security', () => {
        test('should sanitize data consistently across components', () => {
            const dangerousInput = '<script>alert("xss")</script>Hello World';
            
            // Test InputValidator sanitization
            const sanitizedText = inputValidator.sanitizeText(dangerousInput);
            const sanitizedHtml = inputValidator.sanitizeHtml(dangerousInput);
            
            expect(sanitizedText).not.toContain('<script>');
            expect(sanitizedHtml).not.toContain('<script>');
            expect(sanitizedHtml).toContain('Hello World');
        });

        test('should mask sensitive data for logging', () => {
            const sensitiveData = {
                email: '<EMAIL>',
                password: 'secretPassword123',
                licenseKey: 'ABCD-1234-EFGH-5678'
            };
            
            // Test SecurityManager masking
            expect(securityManager.maskSensitiveData(sensitiveData.email, 'email')).toBe('u***@example.com');
            expect(securityManager.maskSensitiveData(sensitiveData.password, 'password')).toBe('********');
            expect(securityManager.maskSensitiveData(sensitiveData.licenseKey, 'licenseKey')).toBe('ABCD****');
        });
    });

    describe('Recovery and Resilience', () => {
        test('should provide recovery suggestions for common errors', () => {
            const networkError = errorHandler.handleError(new Error('Network timeout'), 'api-call');
            
            expect(networkError.recovery).toBeInstanceOf(Array);
            expect(networkError.recovery.length).toBeGreaterThan(0);
            expect(networkError.recovery[0]).toContain('conexión');
        });

        test('should handle security system failures gracefully', () => {
            // Simulate SecurityManager failure
            const originalValidateInput = securityManager.validateInput;
            securityManager.validateInput = () => {
                throw new Error('Security system failure');
            };
            
            // Should not crash the application
            let result;
            expect(() => {
                result = securityManager.validateInput('test', 'email');
            }).not.toThrow();
            
            // Restore original method
            securityManager.validateInput = originalValidateInput;
        });
    });

    describe('Configuration and Policies', () => {
        test('should allow security policy updates', () => {
            const originalMaxAttempts = securityManager.securityPolicies.maxLoginAttempts;
            
            securityManager.updateSecurityPolicy('maxLoginAttempts', 3);
            expect(securityManager.securityPolicies.maxLoginAttempts).toBe(3);
            
            // Should log the policy change
            const recentEvents = securityManager.securityEvents.slice(-1);
            expect(recentEvents[0].type).toBe('policy_updated');
            
            // Restore original value
            securityManager.updateSecurityPolicy('maxLoginAttempts', originalMaxAttempts);
        });

        test('should maintain security limits consistently', () => {
            expect(inputValidator.securityLimits.maxFileSize).toBeDefined();
            expect(securityManager.securityPolicies.maxFileSize).toBeDefined();
            
            // Both should have reasonable limits
            expect(inputValidator.securityLimits.maxFileSize).toBeGreaterThan(0);
            expect(securityManager.securityPolicies.maxFileSize).toBeGreaterThan(0);
        });
    });
});