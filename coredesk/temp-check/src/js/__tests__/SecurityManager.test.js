/**
 * SecurityManager.test.js
 * Comprehensive unit tests for the SecurityManager class
 */

const { SecurityManager, securityManager } = require('../security/SecurityManager');

// Mock dependencies
jest.mock('../utils/ErrorHandler', () => ({
    errorHandler: {
        handleError: jest.fn()
    }
}));

jest.mock('../utils/InputValidator', () => ({
    inputValidator: {
        validateEmail: jest.fn(),
        validatePassword: jest.fn(),
        validateLicenseKey: jest.fn(),
        validateFileName: jest.fn(),
        validateUrl: jest.fn(),
        validateNumber: jest.fn(),
        validateBoolean: jest.fn(),
        validateJson: jest.fn(),
        validateFileUpload: jest.fn(),
        validateApiKey: jest.fn(),
        validateText: jest.fn(),
        validateLoginAttempt: jest.fn(),
        recordFailedLogin: jest.fn(),
        clearFailedAttempts: jest.fn(),
        containsDangerousContent: jest.fn(),
        sanitizeText: jest.fn(),
        sanitizeHtml: jest.fn(),
        sanitizeFileName: jest.fn()
    }
}));

// Mock DOM APIs
global.MutationObserver = jest.fn().mockImplementation((callback) => ({
    observe: jest.fn(),
    disconnect: jest.fn()
}));

global.Node = {
    ELEMENT_NODE: 1
};

describe('SecurityManager', () => {
    let manager;
    
    beforeEach(() => {
        manager = new SecurityManager();
        jest.clearAllMocks();
        
        // Setup document mock
        document.querySelector = jest.fn();
        document.querySelectorAll = jest.fn(() => []);
        document.createElement = jest.fn(() => ({
            setAttribute: jest.fn(),
            getAttribute: jest.fn(),
            removeAttribute: jest.fn(),
            hasAttribute: jest.fn(() => false)
        }));
        document.head = { appendChild: jest.fn() };
    });

    describe('Constructor and Initialization', () => {
        test('should create SecurityManager instance with correct properties', () => {
            expect(manager).toBeInstanceOf(SecurityManager);
            expect(manager.securityPolicies).toBeDefined();
            expect(manager.securityEvents).toBeInstanceOf(Array);
            expect(manager.loginAttempts).toBeInstanceOf(Map);
            expect(manager.threatDetection).toBeDefined();
        });

        test('should initialize security policies', () => {
            expect(manager.securityPolicies.csp).toBeDefined();
            expect(manager.securityPolicies.trustedDomains).toBeInstanceOf(Array);
            expect(manager.securityPolicies.maxFileSize).toBe(10 * 1024 * 1024);
            expect(manager.securityPolicies.maxLoginAttempts).toBe(5);
        });

        test('should initialize threat detection system', () => {
            expect(manager.threatDetection.suspiciousPatterns).toBeInstanceOf(Map);
            expect(manager.threatDetection.behaviorAnalysis).toBeInstanceOf(Map);
            expect(manager.threatDetection.anomalyThreshold).toBe(5);
        });
    });

    describe('Content Security Policy (CSP)', () => {
        test('should setup CSP meta tag', () => {
            document.querySelector.mockReturnValue(null); // No existing CSP
            const mockMeta = {
                setAttribute: jest.fn()
            };
            document.createElement.mockReturnValue(mockMeta);
            
            manager.setupCSP();
            
            expect(document.createElement).toHaveBeenCalledWith('meta');
            expect(mockMeta.setAttribute).toHaveBeenCalledWith('http-equiv', 'Content-Security-Policy');
            expect(mockMeta.setAttribute).toHaveBeenCalledWith('content', expect.stringContaining("default-src 'self'"));
        });

        test('should not duplicate CSP if already exists', () => {
            document.querySelector.mockReturnValue({ content: 'existing csp' });
            
            manager.setupCSP();
            
            expect(document.createElement).not.toHaveBeenCalled();
        });
    });

    describe('Input Validation Integration', () => {
        test('should validate email input using InputValidator', () => {
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.validateEmail.mockReturnValue({ valid: true, sanitized: '<EMAIL>' });
            
            const result = manager.validateInput('<EMAIL>', 'email');
            
            expect(inputValidator.validateEmail).toHaveBeenCalledWith('<EMAIL>');
            expect(result.valid).toBe(true);
        });

        test('should handle validation failures', () => {
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.validateEmail.mockReturnValue({ valid: false, error: 'Invalid email' });
            
            const result = manager.validateInput('invalid-email', 'email');
            
            expect(result.valid).toBe(false);
            expect(result.error).toBe('Invalid email');
        });

        test('should log security events for validation failures', () => {
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.validateEmail.mockReturnValue({ valid: false, error: 'Invalid email' });
            
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            manager.validateInput('invalid-email', 'email', { identifier: 'test-user' });
            
            expect(spyLogEvent).toHaveBeenCalledWith('validation_failed', expect.objectContaining({
                type: 'email',
                error: 'Invalid email'
            }));
        });

        test('should handle missing InputValidator gracefully', () => {
            // Temporarily remove the validator
            const originalValidator = manager.inputValidator;
            manager.inputValidator = null;
            
            const result = manager.validateInput('test', 'email');
            
            expect(result.valid).toBe(false);
            expect(result.error).toBe('Validation service unavailable');
            
            // Restore validator
            manager.inputValidator = originalValidator;
        });
    });

    describe('Request URL Validation', () => {
        test('should allow trusted domains', () => {
            expect(manager.isAllowedRequest('https://coredesk.io/api')).toBe(true);
            expect(manager.isAllowedRequest('https://api.coredesk.io')).toBe(true);
            expect(manager.isAllowedRequest('https://cdn.coredesk.io')).toBe(true);
        });

        test('should allow localhost for development', () => {
            expect(manager.isAllowedRequest('http://localhost:3000')).toBe(true);
            expect(manager.isAllowedRequest('http://127.0.0.1:8080')).toBe(true);
        });

        test('should allow relative URLs for local resources', () => {
            expect(manager.isAllowedRequest('/css/styles.css')).toBe(true);
            expect(manager.isAllowedRequest('/js/app.js')).toBe(true);
            expect(manager.isAllowedRequest('/images/logo.png')).toBe(true);
        });

        test('should block untrusted domains', () => {
            expect(manager.isAllowedRequest('https://malicious-site.com')).toBe(false);
            expect(manager.isAllowedRequest('http://evil.example.com')).toBe(false);
        });

        test('should handle malformed URLs gracefully', () => {
            expect(manager.isAllowedRequest('not-a-url')).toBe(false);
            expect(manager.isAllowedRequest('')).toBe(false);
        });
    });

    describe('File Upload Validation', () => {
        test('should validate acceptable file uploads', () => {
            const mockFile = {
                name: 'document.pdf',
                size: 1024 * 1024, // 1MB
                type: 'application/pdf'
            };
            
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.containsDangerousContent.mockReturnValue(false);
            inputValidator.sanitizeText.mockReturnValue('document.pdf');
            
            const result = manager.validateFileUpload(mockFile);
            
            expect(result.valid).toBe(true);
            expect(result.sanitizedName).toBe('document.pdf');
        });

        test('should reject files that are too large', () => {
            const largeFile = {
                name: 'large.pdf',
                size: 20 * 1024 * 1024, // 20MB (exceeds 10MB limit)
                type: 'application/pdf'
            };
            
            const result = manager.validateFileUpload(largeFile);
            
            expect(result.valid).toBe(false);
            expect(result.errors[0]).toContain('exceeds limit');
        });

        test('should reject unauthorized file types', () => {
            const executableFile = {
                name: 'virus.exe',
                size: 1024,
                type: 'application/octet-stream'
            };
            
            const result = manager.validateFileUpload(executableFile);
            
            expect(result.valid).toBe(false);
            expect(result.errors[0]).toContain('not allowed');
        });

        test('should detect dangerous file names', () => {
            const dangerousFile = {
                name: 'script<attack>.pdf',
                size: 1024,
                type: 'application/pdf'
            };
            
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.containsDangerousContent.mockReturnValue(true);
            
            const result = manager.validateFileUpload(dangerousFile);
            
            expect(result.valid).toBe(false);
            expect(result.errors).toContain('File name contains dangerous characters');
        });
    });

    describe('Login Attempt Tracking', () => {
        test('should track successful login attempts', () => {
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.validateLoginAttempt.mockReturnValue({ valid: true });
            inputValidator.clearFailedAttempts = jest.fn();
            
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            const result = manager.trackLoginAttempt('<EMAIL>', true);
            
            expect(result.locked).toBe(false);
            expect(inputValidator.clearFailedAttempts).toHaveBeenCalledWith('<EMAIL>');
            expect(spyLogEvent).toHaveBeenCalledWith('login_success', expect.any(Object));
        });

        test('should track failed login attempts', () => {
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.validateLoginAttempt.mockReturnValue({ valid: true });
            inputValidator.recordFailedLogin = jest.fn();
            
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            const result = manager.trackLoginAttempt('<EMAIL>', false);
            
            expect(result.locked).toBe(false);
            expect(inputValidator.recordFailedLogin).toHaveBeenCalledWith('<EMAIL>');
            expect(spyLogEvent).toHaveBeenCalledWith('login_failed', expect.any(Object));
        });

        test('should lock account after maximum failed attempts', () => {
            const email = '<EMAIL>';
            
            // Simulate 5 failed attempts
            for (let i = 0; i < 5; i++) {
                manager.trackLoginAttempt(email, false);
            }
            
            const result = manager.trackLoginAttempt(email, false);
            
            expect(result.locked).toBe(true);
            expect(result.retryAfter).toBeDefined();
        });

        test('should handle InputValidator lockout response', () => {
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.validateLoginAttempt.mockReturnValue({ 
                valid: false, 
                locked: true, 
                remainingTime: 10 
            });
            
            const result = manager.trackLoginAttempt('<EMAIL>', false);
            
            expect(result.locked).toBe(true);
            expect(result.remainingTime).toBe(10);
        });
    });

    describe('XSS Detection and Prevention', () => {
        test('should scan DOM nodes for XSS attempts', () => {
            const mockNode = {
                nodeType: 1, // ELEMENT_NODE
                tagName: 'DIV',
                hasAttribute: jest.fn(() => true),
                getAttribute: jest.fn(() => 'malicious()'),
                removeAttribute: jest.fn(),
                innerHTML: '<script>alert(1)</script>'
            };
            
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.containsDangerousContent.mockReturnValue(true);
            inputValidator.sanitizeHtml.mockReturnValue('safe content');
            
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            manager.scanForXSS(mockNode);
            
            expect(mockNode.removeAttribute).toHaveBeenCalledWith('onclick');
            expect(spyLogEvent).toHaveBeenCalledWith('xss_attempt', expect.any(Object));
        });

        test('should ignore non-element nodes', () => {
            const textNode = { nodeType: 3 }; // TEXT_NODE
            
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            manager.scanForXSS(textNode);
            
            expect(spyLogEvent).not.toHaveBeenCalled();
        });
    });

    describe('Security Event Logging', () => {
        test('should log security events with proper structure', () => {
            const spyAnalyze = jest.spyOn(manager, 'analyzeSecurityPattern');
            
            manager.logSecurityEvent('test_event', { detail: 'test' });
            
            expect(manager.securityEvents.length).toBe(1);
            
            const event = manager.securityEvents[0];
            expect(event.type).toBe('test_event');
            expect(event.details.detail).toBe('test');
            expect(event.timestamp).toBeDefined();
            expect(event.severity).toBeDefined();
            
            expect(spyAnalyze).toHaveBeenCalledWith('test_event', { detail: 'test' });
        });

        test('should classify event severity correctly', () => {
            expect(manager.getSecurityEventSeverity('xss_attempt')).toBe('high');
            expect(manager.getSecurityEventSeverity('login_failed')).toBe('medium');
            expect(manager.getSecurityEventSeverity('unknown_event')).toBe('low');
        });

        test('should integrate with ErrorHandler for high-severity events', () => {
            const { errorHandler } = require('../utils/ErrorHandler');
            
            manager.logSecurityEvent('xss_attempt', { attack: 'script injection' });
            
            expect(errorHandler.handleError).toHaveBeenCalledWith(
                expect.any(Error),
                'security-event',
                expect.objectContaining({ eventType: 'xss_attempt' })
            );
        });

        test('should maintain audit log with size limits', () => {
            // Fill audit log beyond limit
            for (let i = 0; i < 1005; i++) {
                manager.logSecurityEvent('test_event', { index: i });
            }
            
            expect(manager.auditLog.length).toBe(1000);
            expect(manager.auditLog[0].details.index).toBe(5); // First 5 should be removed
        });
    });

    describe('Threat Pattern Analysis', () => {
        test('should analyze and track suspicious patterns', () => {
            const spyAnomaly = jest.spyOn(manager, 'handleSecurityAnomaly');
            
            // Generate multiple events to trigger anomaly detection
            for (let i = 0; i < 7; i++) {
                manager.analyzeSecurityPattern('login_failed', { identifier: '<EMAIL>' });
            }
            
            expect(spyAnomaly).toHaveBeenCalled();
        });

        test('should clean up old patterns', () => {
            // Mock Date.now to control time
            const originalNow = Date.now;
            let mockTime = 1000000;
            Date.now = jest.fn(() => mockTime);
            
            // Add pattern
            manager.analyzeSecurityPattern('test_event', { identifier: 'test' });
            
            // Advance time by 15 minutes
            mockTime += 15 * 60 * 1000;
            
            // Add another pattern (should clean up old one)
            manager.analyzeSecurityPattern('test_event', { identifier: 'test' });
            
            const patterns = manager.threatDetection.suspiciousPatterns.get('test_event_test');
            expect(patterns.length).toBe(1); // Old pattern should be removed
            
            // Restore original Date.now
            Date.now = originalNow;
        });
    });

    describe('Security Anomaly Handling', () => {
        test('should handle security anomalies appropriately', () => {
            const spyLockdown = jest.spyOn(manager, 'triggerSecurityLockdown');
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            manager.handleSecurityAnomaly('login_failed', { identifier: 'attacker' }, 15);
            
            expect(spyLogEvent).toHaveBeenCalledWith('security_anomaly', expect.any(Object));
            expect(spyLockdown).toHaveBeenCalledWith('login_failed', { identifier: 'attacker' });
        });

        test('should trigger security lockdown for severe anomalies', () => {
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            manager.triggerSecurityLockdown('xss_attempt', { source: 'malicious script' });
            
            expect(spyLogEvent).toHaveBeenCalledWith('security_lockdown', expect.objectContaining({
                trigger: 'xss_attempt'
            }));
        });
    });

    describe('Periodic Security Scans', () => {
        test('should perform security scan on scripts', () => {
            const maliciousScript = {
                src: 'https://evil.com/malware.js',
                remove: jest.fn()
            };
            
            document.querySelectorAll.mockReturnValue([maliciousScript]);
            
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            manager.performSecurityScan();
            
            expect(spyLogEvent).toHaveBeenCalledWith('suspicious_script', { src: 'https://evil.com/malware.js' });
            expect(maliciousScript.remove).toHaveBeenCalled();
        });

        test('should scan inline styles for dangerous content', () => {
            const elementWithDangerousStyle = {
                getAttribute: jest.fn(() => 'expression(malicious)'),
                removeAttribute: jest.fn()
            };
            
            document.querySelectorAll.mockReturnValue([elementWithDangerousStyle]);
            
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.containsDangerousContent.mockReturnValue(true);
            
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            manager.performSecurityScan();
            
            expect(spyLogEvent).toHaveBeenCalledWith('suspicious_style', expect.any(Object));
            expect(elementWithDangerousStyle.removeAttribute).toHaveBeenCalledWith('style');
        });
    });

    describe('Security Status and Statistics', () => {
        test('should provide comprehensive security status', () => {
            document.querySelector.mockReturnValue({ content: 'csp policy' });
            
            const status = manager.getSecurityStatus();
            
            expect(status.cspEnabled).toBe(true);
            expect(status.recentEvents).toBeInstanceOf(Array);
            expect(status.lockedAccounts).toBeInstanceOf(Array);
            expect(status.totalEvents).toBeDefined();
            expect(status.policies).toBeDefined();
        });

        test('should identify locked accounts', () => {
            const email = '<EMAIL>';
            
            // Lock the account by failing attempts
            for (let i = 0; i < 5; i++) {
                manager.trackLoginAttempt(email, false);
            }
            
            const status = manager.getSecurityStatus();
            expect(status.lockedAccounts).toContain(email);
        });
    });

    describe('Security Policy Updates', () => {
        test('should update security policies', () => {
            const spyLogEvent = jest.spyOn(manager, 'logSecurityEvent');
            
            manager.updateSecurityPolicy('maxLoginAttempts', 3);
            
            expect(manager.securityPolicies.maxLoginAttempts).toBe(3);
            expect(spyLogEvent).toHaveBeenCalledWith('policy_updated', {
                policy: 'maxLoginAttempts',
                value: 3
            });
        });

        test('should ignore invalid policy updates', () => {
            const originalValue = manager.securityPolicies.maxLoginAttempts;
            
            manager.updateSecurityPolicy('nonExistentPolicy', 'value');
            
            expect(manager.securityPolicies.maxLoginAttempts).toBe(originalValue);
        });
    });

    describe('Cleanup and Resource Management', () => {
        test('should cleanup security events older than one hour', () => {
            // Mock Date.now
            const originalNow = Date.now;
            let mockTime = 1000000;
            Date.now = jest.fn(() => mockTime);
            
            // Add old event
            manager.securityEvents.push({
                timestamp: new Date(mockTime - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
            });
            
            // Add recent event
            manager.securityEvents.push({
                timestamp: new Date(mockTime - 30 * 60 * 1000).toISOString() // 30 minutes ago
            });
            
            manager.cleanupSecurityEvents();
            
            expect(manager.securityEvents.length).toBe(1); // Only recent event should remain
            
            // Restore original Date.now
            Date.now = originalNow;
        });

        test('should cleanup DOM observer on cleanup', () => {
            const mockObserver = {
                disconnect: jest.fn()
            };
            manager.domObserver = mockObserver;
            
            manager.cleanup();
            
            expect(mockObserver.disconnect).toHaveBeenCalled();
            expect(manager.domObserver).toBeNull();
        });
    });

    describe('Data Masking', () => {
        test('should mask sensitive data appropriately', () => {
            expect(manager.maskSensitiveData('password123', 'password')).toBe('********');
            expect(manager.maskSensitiveData('<EMAIL>', 'email')).toBe('u***@example.com');
            expect(manager.maskSensitiveData('ABCD-1234-EFGH-5678', 'licenseKey')).toBe('ABCD****');
            expect(manager.maskSensitiveData('some long text', 'default')).toBe('some l...');
        });

        test('should handle edge cases in masking', () => {
            expect(manager.maskSensitiveData('', 'email')).toBe('');
            expect(manager.maskSensitiveData(null, 'password')).toBeNull();
            expect(manager.maskSensitiveData(123, 'password')).toBe(123);
        });
    });

    describe('Singleton Instance', () => {
        test('should export singleton securityManager instance', () => {
            expect(securityManager).toBeInstanceOf(SecurityManager);
        });
    });

    describe('Error Handling', () => {
        test('should handle errors in validation gracefully', () => {
            const { inputValidator } = require('../utils/InputValidator');
            inputValidator.validateEmail.mockImplementation(() => {
                throw new Error('Validation service error');
            });
            
            const { errorHandler } = require('../utils/ErrorHandler');
            
            const result = manager.validateInput('<EMAIL>', 'email');
            
            expect(result.valid).toBe(false);
            expect(result.error).toBe('Validation failed');
            expect(errorHandler.handleError).toHaveBeenCalled();
        });

        test('should handle errors in pattern analysis gracefully', () => {
            const { errorHandler } = require('../utils/ErrorHandler');
            
            // Force an error in pattern analysis
            const originalMethod = manager.threatDetection.suspiciousPatterns.set;
            manager.threatDetection.suspiciousPatterns.set = jest.fn(() => {
                throw new Error('Pattern analysis error');
            });
            
            manager.analyzeSecurityPattern('test_event', { identifier: 'test' });
            
            expect(errorHandler.handleError).toHaveBeenCalledWith(
                expect.any(Error),
                'security-pattern-analysis'
            );
            
            // Restore original method
            manager.threatDetection.suspiciousPatterns.set = originalMethod;
        });
    });
});