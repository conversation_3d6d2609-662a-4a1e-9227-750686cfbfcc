/**
 * SecureTokenManager.js
 * Enhanced token manager with proper encryption using Web Crypto API
 * Replaces weak base64 encoding with AES-GCM encryption
 */

class SecureTokenManager {
    constructor() {
        this.storageKeys = {
            token: 'coredesk_token',
            refreshToken: 'coredesk_refresh_token',
            tokenExpiry: 'coredesk_token_expiry',
            encryptionSalt: 'coredesk_salt'
        };
        
        this.refreshThreshold = 5 * 60 * 1000; // 5 minutes before expiry
        this.cryptoKey = null;
        this.isInitialized = false;
        
        // Initialize crypto key asynchronously
        this.initializeKey();
    }

    /**
     * Initialize or retrieve the encryption key
     */
    async initializeKey() {
        try {
            // Check if Web Crypto API is available
            if (!window.crypto || !window.crypto.subtle) {
                console.error('[SecureTokenManager] Web Crypto API not available');
                this.isInitialized = false;
                return;
            }

            // Generate or retrieve the salt
            let salt = localStorage.getItem(this.storageKeys.encryptionSalt);
            if (!salt) {
                // Generate new salt
                const saltArray = new Uint8Array(16);
                window.crypto.getRandomValues(saltArray);
                salt = this.arrayToBase64(saltArray);
                localStorage.setItem(this.storageKeys.encryptionSalt, salt);
            }

            // Derive key from a combination of factors
            const keyMaterial = await this.getKeyMaterial();
            this.cryptoKey = await window.crypto.subtle.deriveKey(
                {
                    name: "PBKDF2",
                    salt: this.base64ToArray(salt),
                    iterations: 100000,
                    hash: "SHA-256"
                },
                keyMaterial,
                { name: "AES-GCM", length: 256 },
                false,
                ["encrypt", "decrypt"]
            );

            this.isInitialized = true;
            console.log('[SecureTokenManager] Encryption key initialized');
        } catch (error) {
            console.error('[SecureTokenManager] Failed to initialize encryption:', error);
            this.isInitialized = false;
        }
    }

    /**
     * Get key material for deriving the encryption key
     */
    async getKeyMaterial() {
        // Use a combination of device-specific factors
        const encoder = new TextEncoder();
        const keyData = encoder.encode(
            navigator.userAgent + 
            navigator.language + 
            screen.width + 
            screen.height +
            (new Date().getTimezoneOffset())
        );
        
        return window.crypto.subtle.importKey(
            "raw",
            keyData,
            "PBKDF2",
            false,
            ["deriveKey"]
        );
    }

    /**
     * Convert ArrayBuffer to base64 string
     */
    arrayToBase64(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }

    /**
     * Convert base64 string to ArrayBuffer
     */
    base64ToArray(base64) {
        const binary = atob(base64);
        const bytes = new Uint8Array(binary.length);
        for (let i = 0; i < binary.length; i++) {
            bytes[i] = binary.charCodeAt(i);
        }
        return bytes.buffer;
    }

    /**
     * Encrypt sensitive data using AES-GCM
     */
    async encrypt(text) {
        if (!this.isInitialized || !this.cryptoKey) {
            console.warn('[SecureTokenManager] Encryption not available, falling back to sessionStorage');
            return null;
        }

        try {
            const encoder = new TextEncoder();
            const data = encoder.encode(text);
            
            // Generate random IV for each encryption
            const iv = window.crypto.getRandomValues(new Uint8Array(12));
            
            // Encrypt the data
            const encryptedData = await window.crypto.subtle.encrypt(
                {
                    name: "AES-GCM",
                    iv: iv
                },
                this.cryptoKey,
                data
            );
            
            // Combine IV and encrypted data
            const combined = new Uint8Array(iv.length + encryptedData.byteLength);
            combined.set(iv, 0);
            combined.set(new Uint8Array(encryptedData), iv.length);
            
            // Return as base64
            return this.arrayToBase64(combined);
        } catch (error) {
            console.error('[SecureTokenManager] Encryption failed:', error);
            return null;
        }
    }

    /**
     * Decrypt data using AES-GCM
     */
    async decrypt(encryptedText) {
        if (!this.isInitialized || !this.cryptoKey) {
            console.warn('[SecureTokenManager] Decryption not available');
            return null;
        }

        try {
            // Convert from base64
            const combined = new Uint8Array(this.base64ToArray(encryptedText));
            
            // Extract IV and encrypted data
            const iv = combined.slice(0, 12);
            const encryptedData = combined.slice(12);
            
            // Decrypt the data
            const decryptedData = await window.crypto.subtle.decrypt(
                {
                    name: "AES-GCM",
                    iv: iv
                },
                this.cryptoKey,
                encryptedData
            );
            
            // Decode the result
            const decoder = new TextDecoder();
            return decoder.decode(decryptedData);
        } catch (error) {
            console.error('[SecureTokenManager] Decryption failed:', error);
            return null;
        }
    }

    /**
     * Store tokens securely
     */
    async storeTokens(tokenData) {
        try {
            // Wait for initialization if needed
            if (!this.isInitialized) {
                await this.initializeKey();
            }

            const { accessToken, refreshToken, expiresIn } = tokenData;
            
            // Calculate expiry time
            const expiryTime = Date.now() + (expiresIn * 1000);
            
            // Try to encrypt tokens
            const encryptedAccess = await this.encrypt(accessToken);
            const encryptedRefresh = refreshToken ? await this.encrypt(refreshToken) : null;
            
            if (encryptedAccess) {
                // Store encrypted tokens
                localStorage.setItem(this.storageKeys.token, encryptedAccess);
                if (encryptedRefresh) {
                    localStorage.setItem(this.storageKeys.refreshToken, encryptedRefresh);
                }
            } else {
                // Fallback to sessionStorage (more secure than localStorage)
                console.warn('[SecureTokenManager] Using sessionStorage fallback');
                sessionStorage.setItem(this.storageKeys.token, accessToken);
                if (refreshToken) {
                    sessionStorage.setItem(this.storageKeys.refreshToken, refreshToken);
                }
            }
            
            // Store expiry time (not sensitive)
            localStorage.setItem(this.storageKeys.tokenExpiry, expiryTime.toString());
            
            console.log('[SecureTokenManager] Tokens stored securely');
            return true;
        } catch (error) {
            console.error('[SecureTokenManager] Failed to store tokens:', error);
            return false;
        }
    }

    /**
     * Retrieve and decrypt access token
     */
    async getToken() {
        try {
            // Check localStorage first
            const encryptedToken = localStorage.getItem(this.storageKeys.token);
            if (encryptedToken && this.isInitialized) {
                const decrypted = await this.decrypt(encryptedToken);
                if (decrypted) {
                    return decrypted;
                }
            }
            
            // Check sessionStorage fallback
            const sessionToken = sessionStorage.getItem(this.storageKeys.token);
            if (sessionToken) {
                return sessionToken;
            }
            
            return null;
        } catch (error) {
            console.error('[SecureTokenManager] Failed to retrieve token:', error);
            return null;
        }
    }

    /**
     * Clear all tokens
     */
    clearTokens() {
        // Clear from both storages
        Object.values(this.storageKeys).forEach(key => {
            localStorage.removeItem(key);
            sessionStorage.removeItem(key);
        });
        
        console.log('[SecureTokenManager] All tokens cleared');
    }

    /**
     * Check if token is valid
     */
    async isTokenValid() {
        const token = await this.getToken();
        if (!token) return false;
        
        const expiry = localStorage.getItem(this.storageKeys.tokenExpiry);
        if (!expiry) return false;
        
        const expiryTime = parseInt(expiry);
        const now = Date.now();
        
        // Add session timeout check (30 minutes of inactivity)
        const lastActivity = sessionStorage.getItem('coredesk_last_activity');
        if (lastActivity) {
            const timeSinceActivity = now - parseInt(lastActivity);
            if (timeSinceActivity > 30 * 60 * 1000) {
                console.log('[SecureTokenManager] Session timeout - clearing tokens');
                this.clearTokens();
                return false;
            }
        }
        
        // Update last activity
        sessionStorage.setItem('coredesk_last_activity', now.toString());
        
        return now < expiryTime;
    }

    /**
     * Decode JWT token payload
     */
    decodeToken(token) {
        try {
            const parts = token.split('.');
            if (parts.length !== 3) {
                throw new Error('Invalid JWT format');
            }
            
            const payload = parts[1];
            const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
            const decoded = atob(paddedPayload);
            return JSON.parse(decoded);
        } catch (error) {
            console.error('[SecureTokenManager] Failed to decode token:', error);
            return null;
        }
    }
}

// Create and export singleton instance
const secureTokenManager = new SecureTokenManager();

// Make available globally
if (typeof window !== 'undefined') {
    window.secureTokenManager = secureTokenManager;
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecureTokenManager;
}