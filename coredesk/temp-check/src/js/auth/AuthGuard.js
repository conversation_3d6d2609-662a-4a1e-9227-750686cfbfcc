/**
 * Authentication Guard
 * Protects the main application by checking authentication status
 * Redirects to login page if user is not authenticated
 */

class AuthGuard {
    constructor() {
        this.loginUrl = 'login.html';
        this.publicPaths = ['/login.html', '/register.html', '/forgot-password.html'];
        this.checkInterval = null;
        this.isCheckingAuth = false; // Prevent simultaneous auth checks
    }
    
    /**
     * Initialize authentication guard
     */
    init() {
        console.log('Auth', '[AuthGuard] Initializing authentication guard...');
        
        // Skip initial check if immediate auth check already verified authentication
        if (window._dashboardAuthVerified) {
            console.log('Auth', '[AuthGuard] Skipping initial check - immediate auth check already verified');
        } else {
            console.log('Auth', '[AuthGuard] Performing initial authentication check');
            this.checkAuthentication();
        }
        
        // Set up periodic checks
        this.startPeriodicCheck();
        
        // Listen for authentication events
        this.setupEventListeners();
        
        console.log('Auth', '[AuthGuard] Authentication guard initialized successfully');
    }
    
    /**
     * Check if user is authenticated - CENTRAL AUTHENTICATION AUTHORITY
     * This is the single source of truth for authentication checking
     */
    checkAuthentication() {
        // Prevent simultaneous authentication checks
        if (this.isCheckingAuth) {
            console.log('Auth', '[AuthGuard] Authentication check already in progress, skipping');
            return;
        }
        
        this.isCheckingAuth = true;
        
        try {
            const currentPath = window.location.pathname;
            
            console.log('Auth', '[AuthGuard] Starting authentication check for path:', currentPath);
        
            // Skip check for public pages
            if (this.isPublicPath(currentPath)) {
                console.log('Auth', '[AuthGuard] Skipping auth check - public path');
                return;
            }
            
            // CRITICAL: Check for persistent logout protection first
            const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
            const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
            const now = Date.now();
            
            // If logout was forced within the last 10 seconds, don't perform auth checks
            if (persistentLogoutFlag === 'true' || (logoutTimestamp && (now - parseInt(logoutTimestamp)) < 10000)) {
                console.log('Auth', '[AuthGuard] BLOCKED authentication check - Persistent logout protection active');
                return; // Exit early - no authentication check allowed
            }
            
            // CRITICAL: Don't check auth if logout is in progress
            if (window._isLoggingOut) {
                console.log('Auth', '[AuthGuard] Authentication check skipped - logout in progress');
                return;
            }
            
            // Also check if we just logged out to prevent immediate redirect back
            const justLoggedOut = sessionStorage.getItem('_justLoggedOut');
            if (justLoggedOut) {
                console.log('Auth', '[AuthGuard] Skipping auth check - just logged out flag set');
                // Clear the flag after checking to allow future authentications
                sessionStorage.removeItem('_justLoggedOut');
                // Clear any stale auth data
                this.clearAuthData();
                return;
            }
            
            // Check if we have valid authentication
            if (!this.isAuthenticated()) {
                console.log('Auth', '[AuthGuard] User not authenticated, redirecting to login');
                this.redirectToLogin();
                return;
            }
            
            // Check if session is expired
            if (this.isSessionExpired()) {
                console.log('Auth', '[AuthGuard] Session expired, redirecting to login');
                this.handleSessionExpired();
                return;
            }
            
            console.log('Auth', '[AuthGuard] User authenticated, access granted');
            
            // Emit authentication success event for other components
            window.dispatchEvent(new CustomEvent('auth:verified', {
                detail: { source: 'AuthGuard', authenticated: true }
            }));
            
        } catch (error) {
            console.error('Auth', '[AuthGuard] Error during authentication check:', error);
        } finally {
            // Always reset the checking flag
            this.isCheckingAuth = false;
        }
    }
    
    /**
     * Check if current path is public (doesn't require auth)
     */
    isPublicPath(path) {
        return this.publicPaths.some(publicPath => path.includes(publicPath));
    }
    
    /**
     * Check if user has valid authentication
     */
    isAuthenticated() {
        // First check if we have auth utilities available
        if (!window.CoreDeskAuth || !window.CoreDeskAuth.utils) {
            return false;
        }
        
        // Use the auth utility function
        return window.CoreDeskAuth.utils.isAuthenticated();
    }
    
    /**
     * Check if session has expired
     */
    isSessionExpired() {
        if (!window.tokenManager) {
            return false;
        }
        
        // Check if token is expired
        return !window.tokenManager.isTokenValid();
    }
    
    /**
     * Redirect to login page
     */
    redirectToLogin() {
        // Save current URL for redirect after login
        const returnUrl = window.location.href;
        sessionStorage.setItem('returnUrl', returnUrl);
        
        // Clear any auth data
        this.clearAuthData();
        
        // Redirect to login
        window.location.href = this.loginUrl;
    }
    
    /**
     * Handle expired session
     */
    handleSessionExpired() {
        // Show notification if available
        if (window.showNotification) {
            window.showNotification('Tu sesión ha expirado. Por favor inicia sesión nuevamente.', 'warning');
        }
        
        // Clear auth data and redirect
        this.clearAuthData();
        this.redirectToLogin();
    }
    
    /**
     * Clear authentication data
     */
    clearAuthData() {
        // Clear tokens
        if (window.tokenManager) {
            window.tokenManager.clearTokens();
        }
        
        // Clear user data
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        
        // Clear session storage
        sessionStorage.removeItem('auth_token');
    }
    
    /**
     * Start periodic authentication checks
     */
    startPeriodicCheck() {
        // Clear any existing interval
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
        }
        
        // Check every 10 minutes (reduced frequency to prevent interference)
        this.checkInterval = setInterval(() => {
            console.log('Auth', '[AuthGuard] Periodic authentication check');
            this.checkAuthentication();
        }, 10 * 60 * 1000);
    }
    
    /**
     * Stop periodic checks
     */
    stopPeriodicCheck() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for logout events
        window.addEventListener('auth:logout', () => {
            this.redirectToLogin();
        });
        
        // Listen for session expired events
        window.addEventListener('auth:session-expired', () => {
            this.handleSessionExpired();
        });
        
        // Listen for unauthorized API responses
        window.addEventListener('api:unauthorized', () => {
            this.handleSessionExpired();
        });
        
        // Stop checks when page is hidden
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopPeriodicCheck();
            } else {
                this.startPeriodicCheck();
                this.checkAuthentication();
            }
        });
    }
    
    /**
     * Get return URL after login
     */
    getReturnUrl() {
        const returnUrl = sessionStorage.getItem('returnUrl');
        sessionStorage.removeItem('returnUrl');
        return returnUrl || 'index.html';
    }
    
    /**
     * Update user session
     */
    updateSession(user, token) {
        // CRITICAL: Check for persistent logout protection before updating session
        const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
        const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
        const now = Date.now();
        
        // If logout was forced within the last 10 seconds, refuse to update session
        if (persistentLogoutFlag === 'true' || (logoutTimestamp && (now - parseInt(logoutTimestamp)) < 10000)) {
            console.log('AuthGuard.updateSession: BLOCKED - Persistent logout protection active, refusing to update session');
            return;
        }
        
        // CRITICAL: Don't update session if logout is in progress
        if (window._isLoggingOut) {
            console.log('AuthGuard.updateSession: BLOCKED - logout in progress, refusing to update session');
            return;
        }
        
        if (user) {
            localStorage.setItem('auth_user', JSON.stringify(user));
        }
        
        if (token) {
            localStorage.setItem('auth_token', token);
        }
        
        // Restart periodic checks
        this.startPeriodicCheck();
    }
    
    /**
     * Get current user
     */
    getCurrentUser() {
        const userStr = localStorage.getItem('auth_user');
        if (!userStr) return null;
        
        try {
            return JSON.parse(userStr);
        } catch (error) {
            console.error('Failed to parse user data:', error);
            return null;
        }
    }
    
    /**
     * Check if user has specific permission
     */
    hasPermission(permission) {
        const user = this.getCurrentUser();
        if (!user || !user.permissions) return false;
        
        return user.permissions.includes(permission);
    }
    
    /**
     * Check if user has specific role
     */
    hasRole(role) {
        const user = this.getCurrentUser();
        if (!user || !user.roles) return false;
        
        return user.roles.includes(role);
    }
}

// Create global instance
const authGuard = new AuthGuard();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = authGuard;
}

// Make available globally
if (typeof window !== 'undefined') {
    window.authGuard = authGuard;
}