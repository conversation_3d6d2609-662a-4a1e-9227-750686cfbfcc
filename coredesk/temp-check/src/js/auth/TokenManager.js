/**
 * TokenManager.js
 * Manages JWT tokens with secure storage and automatic refresh
 */

// JWT and crypto APIs will be available globally or through polyfills
// const jwt = require('jsonwebtoken');
// const crypto = require('crypto');

class TokenManager {
    constructor() {
        // Simple JWT decode for browser environment
        this.jwtDecode = this.createJwtDecoder();
        this.storageKeys = {
            token: 'coredesk_token',
            refreshToken: 'coredesk_refresh_token',
            tokenExpiry: 'coredesk_token_expiry',
            encryptionKey: 'coredesk_encryption_key'
        };
        
        this.refreshThreshold = 5 * 60 * 1000; // 5 minutes before expiry
        this.encryptionKey = this.getOrCreateEncryptionKey();
    }

    /**
     * Create simple JWT decoder for browser environment
     */
    createJwtDecoder() {
        return {
            decode: (token) => {
                try {
                    const parts = token.split('.');
                    if (parts.length !== 3) {
                        throw new Error('Invalid JWT format');
                    }
                    
                    // Decode payload (second part)
                    const payload = parts[1];
                    // Add padding if needed
                    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
                    const decoded = atob(paddedPayload);
                    return JSON.parse(decoded);
                } catch (error) {
                    throw new Error('Failed to decode JWT: ' + error.message);
                }
            }
        };
    }

    /**
     * Generate random key for browser environment
     */
    generateRandomKey() {
        if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
            // Use crypto.getRandomValues for secure random bytes
            const array = new Uint8Array(32);
            window.crypto.getRandomValues(array);
            return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
        } else {
            // Fallback to Math.random (less secure but functional)
            let key = '';
            for (let i = 0; i < 64; i++) {
                key += Math.floor(Math.random() * 16).toString(16);
            }
            return key;
        }
    }

    /**
     * Get or create encryption key for secure storage
     */
    getOrCreateEncryptionKey() {
        let key = localStorage.getItem(this.storageKeys.encryptionKey);
        
        if (!key) {
            // Generate a new encryption key using browser-compatible method
            key = this.generateRandomKey();
            localStorage.setItem(this.storageKeys.encryptionKey, key);
        }
        
        return key;
    }

    /**
     * Encrypt sensitive data
     */
    encrypt(text) {
        // Check if we're in a browser environment
        if (typeof window !== 'undefined') {
            // Browser environment - use base64 encoding for basic obfuscation
            try {
                const encoded = 'browser:' + btoa(text);
                return encoded;
            } catch (error) {
                console.warn('TokenManager', 'Base64 encoding failed, storing unencrypted:', error);
                return text;
            }
        }
        
        // Node.js environment - use proper encryption
        try {
            const algorithm = 'aes-256-gcm';
            const iv = crypto.randomBytes(16);
            const cipher = crypto.createCipheriv(algorithm, Buffer.from(this.encryptionKey, 'hex'), iv);
            
            let encrypted = cipher.update(text, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const authTag = cipher.getAuthTag();
            
            return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
        } catch (error) {
            console.error('Encryption error:', error);
            return text; // Fallback to unencrypted
        }
    }

    /**
     * Decrypt sensitive data
     */
    decrypt(encryptedData) {
        try {
            // Check if this is browser-encoded data
            if (encryptedData.startsWith('browser:')) {
                try {
                    return atob(encryptedData.substring(8)); // Remove 'browser:' prefix and decode
                } catch (error) {
                    console.warn('Base64 decoding failed:', error);
                    return encryptedData;
                }
            }
            
            // Handle Node.js encrypted data
            const parts = encryptedData.split(':');
            if (parts.length !== 3) return encryptedData; // Not encrypted
            
            const algorithm = 'aes-256-gcm';
            const iv = Buffer.from(parts[0], 'hex');
            const authTag = Buffer.from(parts[1], 'hex');
            const encrypted = parts[2];
            
            const decipher = crypto.createDecipheriv(algorithm, Buffer.from(this.encryptionKey, 'hex'), iv);
            decipher.setAuthTag(authTag);
            
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
        } catch (error) {
            console.error('Decryption error:', error);
            return encryptedData; // Return as-is if decryption fails
        }
    }

    /**
     * Store tokens securely
     */
    storeTokens(tokenData) {
        // CRITICAL: Check for persistent logout protection first
        const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
        const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
        const now = Date.now();
        
        // If logout was forced within the last 10 seconds, refuse to store tokens
        if (persistentLogoutFlag === 'true' || (logoutTimestamp && (now - parseInt(logoutTimestamp)) < 10000)) {
            console.log('TokenManager.storeTokens: BLOCKED - Persistent logout protection active, refusing to store tokens');
            return;
        }
        
        // CRITICAL: Prevent token storage during logout
        if (window._isLoggingOut) {
            console.log('TokenManager.storeTokens: BLOCKED - logout in progress, refusing to store tokens');
            return;
        }
        
        console.log('TokenManager.storeTokens: tokenData =', tokenData);
        const { token, refreshToken, expiresIn, expiryTime } = tokenData;
        
        if (token) {
            // Double-check logout flag before each storage operation
            if (window._isLoggingOut) {
                console.log('TokenManager.storeTokens: BLOCKED during token storage - logout detected');
                return;
            }
            
            // Store encrypted token
            const encryptedToken = this.encrypt(token);
            localStorage.setItem(this.storageKeys.token, encryptedToken);
            console.log('TokenManager.storeTokens: token stored');
            
            // Calculate and store expiry time
            if (expiryTime) {
                // Use provided expiry time if available
                const expiry = expiryTime instanceof Date ? expiryTime : new Date(expiryTime);
                localStorage.setItem(this.storageKeys.tokenExpiry, expiry.toISOString());
                console.log('TokenManager.storeTokens: using provided expiryTime:', expiry.toISOString());
            } else if (expiresIn) {
                // Calculate from expiresIn (seconds)
                const expiryTime = new Date(Date.now() + (expiresIn * 1000));
                localStorage.setItem(this.storageKeys.tokenExpiry, expiryTime.toISOString());
                console.log('TokenManager.storeTokens: calculated expiryTime:', expiryTime.toISOString());
            } else {
                // Default to 24 hours if no expiry info provided
                const defaultExpiry = new Date(Date.now() + (24 * 60 * 60 * 1000));
                localStorage.setItem(this.storageKeys.tokenExpiry, defaultExpiry.toISOString());
                console.log('TokenManager.storeTokens: using default 24hr expiry:', defaultExpiry.toISOString());
            }
        }
        
        if (refreshToken) {
            // Final check before storing refresh token
            if (window._isLoggingOut) {
                console.log('TokenManager.storeTokens: BLOCKED during refresh token storage - logout detected');
                return;
            }
            
            // Store encrypted refresh token
            const encryptedRefreshToken = this.encrypt(refreshToken);
            localStorage.setItem(this.storageKeys.refreshToken, encryptedRefreshToken);
            console.log('TokenManager.storeTokens: refresh token stored');
        }
        
        console.log('TokenManager', 'Tokens stored securely');
    }

    /**
     * Get access token
     */
    getToken() {
        const encryptedToken = localStorage.getItem(this.storageKeys.token);
        console.log('TokenManager.getToken: encryptedToken exists?', !!encryptedToken);
        if (!encryptedToken) return null;
        
        const decrypted = this.decrypt(encryptedToken);
        console.log('TokenManager.getToken: decrypted token exists?', !!decrypted);
        return decrypted;
    }

    /**
     * Get refresh token
     */
    getRefreshToken() {
        const encryptedToken = localStorage.getItem(this.storageKeys.refreshToken);
        if (!encryptedToken) return null;
        
        return this.decrypt(encryptedToken);
    }

    /**
     * Get token expiry time
     */
    getTokenExpiry() {
        const expiry = localStorage.getItem(this.storageKeys.tokenExpiry);
        return expiry ? new Date(expiry) : null;
    }

    /**
     * Check if token is valid
     */
    isTokenValid() {
        const token = this.getToken();
        const expiry = this.getTokenExpiry();
        
        console.log('TokenManager.isTokenValid: token exists?', !!token);
        console.log('TokenManager.isTokenValid: expiry exists?', !!expiry);
        
        if (!token || !expiry) return false;
        
        const isValid = new Date() < expiry;
        console.log('TokenManager.isTokenValid: token is valid?', isValid, 'expiry:', expiry);
        
        return isValid;
    }

    /**
     * Check if token needs refresh
     */
    needsRefresh() {
        const expiry = this.getTokenExpiry();
        if (!expiry) return false;
        
        const timeUntilExpiry = expiry.getTime() - Date.now();
        return timeUntilExpiry < this.refreshThreshold;
    }

    /**
     * Decode token without verification (client-side only)
     */
    decodeToken(token = null) {
        try {
            const tokenToDecode = token || this.getToken();
            if (!tokenToDecode) return null;
            
            // Decode without verification (client-side)
            const decoded = this.jwtDecode.decode(tokenToDecode);
            return decoded;
        } catch (error) {
            console.error('Token decode error:', error);
            return null;
        }
    }

    /**
     * Get user info from token
     */
    getUserFromToken() {
        const decoded = this.decodeToken();
        if (!decoded) return null;
        
        return {
            id: decoded.userId || decoded.sub,
            email: decoded.email,
            role: decoded.role,
            permissions: decoded.permissions || [],
            licenseType: decoded.licenseType
        };
    }

    /**
     * Clear all tokens
     */
    clearTokens() {
        console.log('TokenManager.clearTokens: Starting token removal...');
        console.log('TokenManager.clearTokens: Storage keys:', this.storageKeys);
        
        // Check what exists before removal
        const beforeRemoval = {
            token: !!localStorage.getItem(this.storageKeys.token),
            refreshToken: !!localStorage.getItem(this.storageKeys.refreshToken),
            expiry: !!localStorage.getItem(this.storageKeys.tokenExpiry)
        };
        console.log('TokenManager.clearTokens: Tokens before removal:', beforeRemoval);
        
        // Remove each token with logging
        console.log('TokenManager.clearTokens: Removing token from key:', this.storageKeys.token);
        localStorage.removeItem(this.storageKeys.token);
        
        console.log('TokenManager.clearTokens: Removing refresh token from key:', this.storageKeys.refreshToken);
        localStorage.removeItem(this.storageKeys.refreshToken);
        
        console.log('TokenManager.clearTokens: Removing expiry from key:', this.storageKeys.tokenExpiry);
        localStorage.removeItem(this.storageKeys.tokenExpiry);
        
        // Verify removal
        const afterRemoval = {
            token: !!localStorage.getItem(this.storageKeys.token),
            refreshToken: !!localStorage.getItem(this.storageKeys.refreshToken),
            expiry: !!localStorage.getItem(this.storageKeys.tokenExpiry)
        };
        console.log('TokenManager.clearTokens: Tokens after removal:', afterRemoval);
        
        const success = !afterRemoval.token && !afterRemoval.refreshToken && !afterRemoval.expiry;
        console.log('TokenManager.clearTokens: Token clearing successful:', success);
        
        console.log('TokenManager', 'All tokens cleared');
    }

    /**
     * Set custom refresh threshold
     */
    setRefreshThreshold(minutes) {
        this.refreshThreshold = minutes * 60 * 1000;
    }

    /**
     * Get time until token expiry
     */
    getTimeUntilExpiry() {
        const expiry = this.getTokenExpiry();
        if (!expiry) return 0;
        
        const timeRemaining = expiry.getTime() - Date.now();
        return Math.max(0, timeRemaining);
    }

    /**
     * Format time remaining
     */
    formatTimeRemaining() {
        const ms = this.getTimeUntilExpiry();
        
        if (ms <= 0) return 'Expired';
        
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) return `${days}d ${hours % 24}h`;
        if (hours > 0) return `${hours}h ${minutes % 60}m`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }

    /**
     * Check if user has specific permission
     */
    hasPermission(permission) {
        const user = this.getUserFromToken();
        if (!user) return false;
        
        return user.permissions.includes(permission);
    }

    /**
     * Check if user has specific role
     */
    hasRole(role) {
        const user = this.getUserFromToken();
        if (!user) return false;
        
        return user.role === role;
    }

    /**
     * Get token claims
     */
    getTokenClaims() {
        const decoded = this.decodeToken();
        if (!decoded) return {};
        
        return {
            issuedAt: decoded.iat ? new Date(decoded.iat * 1000) : null,
            expiresAt: decoded.exp ? new Date(decoded.exp * 1000) : null,
            notBefore: decoded.nbf ? new Date(decoded.nbf * 1000) : null,
            issuer: decoded.iss,
            audience: decoded.aud,
            subject: decoded.sub,
            jwtId: decoded.jti
        };
    }
}

// Create singleton instance
const tokenManager = new TokenManager();

// Make available globally for browser environment
if (typeof window !== 'undefined') {
    window.TokenManager = TokenManager;
    window.tokenManager = tokenManager;
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = tokenManager;
}