/**
 * Unified Authentication Manager
 * Handles all authentication operations including login, logout, token refresh
 * and session management as specified in PRD section 3.2
 */

// API services and TokenManager will be available globally
// const { authApiService, licenseApiService } = require('../services/api');
// const tokenManager = require('./TokenManager');

class UnifiedAuthManager {
    constructor() {
        // Initialize dependencies lazily to avoid circular dependencies
        this.authApiService = null;
        this.licenseApiService = null;
        this.tokenManager = null;
        
        this.authenticationHandler = null;
        this.sessionManager = null;
        this.eventHandlers = null;
        
        // Defer initialization to ensure all classes are defined
        setTimeout(() => {
            this.initialize();
        }, 0);
    }

    /**
     * Initialize dependencies after all components are loaded
     */
    initializeDependencies() {
        if (!this.authApiService && window.authApiService) {
            this.authApiService = window.authApiService;
        }
        if (!this.licenseApiService && window.licenseApiService) {
            this.licenseApiService = window.licenseApiService;
        }
        if (!this.tokenManager && window.tokenManager) {
            this.tokenManager = window.tokenManager;
        }
        
        // Force initialization if still missing
        if (!this.authApiService) {
            console.warn('AuthApiService not found, creating new instance');
            this.authApiService = new window.AuthApiService();
        }
        if (!this.tokenManager) {
            console.warn('TokenManager not found, creating new instance');
            this.tokenManager = new window.TokenManager();
        }
    }

    /**
     * Initialize the authentication manager
     */
    async initialize() {
        console.log('Auth', '[UnifiedAuthManager] Initializing...', );
        
        // Initialize handlers
        this.authenticationHandler = new AuthenticationHandler();
        this.sessionManager = new SessionManager();
        this.eventHandlers = new AuthEventHandlers();
        
        // Load current user if authenticated
        if (window.CoreDeskAuth && window.CoreDeskAuth.utils && window.CoreDeskAuth.utils.isAuthenticated()) {
            await this.authenticationHandler.restoreSession();
            this.sessionManager.startMonitoring();
        }
        
        // Set up event listeners
        this.eventHandlers.setup();
        
        console.log('Auth', '[UnifiedAuthManager] Initialized successfully', );
    }

    /**
     * Authenticate user with email and password
     */
    async login(email, password, rememberMe = false) {
        return await this.authenticationHandler.login(email, password, rememberMe);
    }

    /**
     * Authenticate user with license key
     */
    async loginWithLicense(licenseKey, deviceFingerprint) {
        return await this.authenticationHandler.loginWithLicense(licenseKey, deviceFingerprint);
    }

    /**
     * Logout current user
     */
    async logout() {
        return await this.authenticationHandler.logout();
    }

    /**
     * Refresh authentication token
     */
    async refreshToken() {
        return await this.sessionManager.refreshToken();
    }

    /**
     * Get current user profile from API
     */
    async getCurrentProfile() {
        return await this.authenticationHandler.getCurrentProfile();
    }

    /**
     * Get current authentication status
     */
    getStatus() {
        return this.authenticationHandler.getStatus();
    }
}

/**
 * AuthenticationHandler class
 * Handles login/logout operations and user authentication
 */
class AuthenticationHandler {
    constructor() {
        this.currentUser = null;
    }

    async login(email, password, rememberMe = false) {
        // CRITICAL: Prevent login during logout process
        if (window._isLoggingOut) {
            console.log('Auth', '[AuthenticationHandler] LOGIN BLOCKED - logout in progress');
            return { success: false, error: 'Authentication temporarily unavailable' };
        }
        
        // Create memory-efficient async wrapper
        const memoryEfficientLogin = window.memoryManager?.createAsyncWrapper(async () => {
            // Double-check logout flag inside async wrapper
            if (window._isLoggingOut) {
                throw new Error('Authentication blocked during logout');
            }
            
            // Validate inputs using security manager
            const emailValidation = window.securityManager?.validateInput(email, 'email');
            if (!emailValidation?.valid) {
                return { success: false, error: emailValidation?.error || 'Invalid email format' };
            }

            const passwordValidation = window.securityManager?.validateInput(password, 'password');
            if (!passwordValidation?.valid) {
                return { success: false, error: passwordValidation?.error || 'Invalid password format' };
            }

            // Check rate limiting
            const rateLimitCheck = window.securityManager?.trackLoginAttempt(emailValidation.sanitized, false);
            if (rateLimitCheck?.locked) {
                return { 
                    success: false, 
                    error: 'Too many failed attempts. Account temporarily locked.',
                    retryAfter: rateLimitCheck.retryAfter 
                };
            }

            console.log('Auth', '[AuthenticationHandler] Attempting login for:', emailValidation.sanitized);
            
            const result = await this.makeLoginRequest(emailValidation.sanitized, password, rememberMe);
            
            // Final check before processing login
            if (window._isLoggingOut) {
                throw new Error('Login cancelled due to logout in progress');
            }
            
            await this.processSuccessfulLogin(result);
            
            // Mark login as successful for rate limiting
            window.securityManager?.trackLoginAttempt(emailValidation.sanitized, true);
            
            console.log('Auth', '[AuthenticationHandler] Login successful for:', emailValidation.sanitized);
            return { success: true, user: this.currentUser };
        }, 15000); // 15 second timeout for login

        try {
            return await (memoryEfficientLogin || this.fallbackLogin.bind(this))(email, password, rememberMe);
        } catch (error) {
            // Mark login as failed for rate limiting
            if (email) {
                const emailValidation = window.securityManager?.validateInput(email, 'email');
                if (emailValidation?.valid) {
                    window.securityManager?.trackLoginAttempt(emailValidation.sanitized, false);
                }
            }
            
            console.error('Auth', '[AuthenticationHandler] Login error:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Fallback login method when memory manager is not available
     */
    async fallbackLogin(email, password, rememberMe) {
        const emailValidation = window.securityManager?.validateInput(email, 'email');
        const sanitizedEmail = emailValidation?.sanitized || email;
        
        const result = await this.makeLoginRequest(sanitizedEmail, password, rememberMe);
        await this.processSuccessfulLogin(result);
        return { success: true, user: this.currentUser };
    }

    async makeLoginRequest(email, password, rememberMe) {
        window.unifiedAuthManager.initializeDependencies();
        if (!window.unifiedAuthManager.authApiService) {
            throw new Error('AuthApiService not available');
        }
        
        const result = await window.unifiedAuthManager.authApiService.login(email, password, rememberMe);
        
        if (!result.success) {
            throw new Error(result.error || 'Login failed');
        }
        
        return result;
    }

    async processSuccessfulLogin(result) {
        window.unifiedAuthManager.initializeDependencies();
        if (!window.unifiedAuthManager.authApiService || !window.unifiedAuthManager.tokenManager) {
            throw new Error('Authentication services not available');
        }
        
        const authData = window.unifiedAuthManager.authApiService.extractAuthData(result);
        
        // Store tokens using TokenManager
        window.unifiedAuthManager.tokenManager.storeTokens(authData);
        
        // Store user data
        localStorage.setItem(window.CoreDeskAuth.storage.keys.user, JSON.stringify(authData.user));
        this.currentUser = authData.user;
        
        // Start session monitoring
        window.unifiedAuthManager.sessionManager.startMonitoring();
        
        // Emit login event
        window.dispatchEvent(new CustomEvent(window.CoreDeskAuth.events.onLogin, {
            detail: { user: this.currentUser, fromLogin: true }
        }));
    }

    async loginWithLicense(licenseKey, deviceFingerprint) {
        try {
            // Validate license key format
            const licenseValidation = window.securityManager?.validateInput(licenseKey, 'licenseKey');
            if (!licenseValidation?.valid) {
                return { success: false, error: licenseValidation?.error || 'Invalid license key format' };
            }

            // Validate device fingerprint
            if (!deviceFingerprint || typeof deviceFingerprint !== 'string') {
                return { success: false, error: 'Invalid device fingerprint' };
            }

            // Check rate limiting using license key as identifier
            const rateLimitCheck = window.securityManager?.trackLoginAttempt(licenseValidation.sanitized, false);
            if (rateLimitCheck?.locked) {
                return { 
                    success: false, 
                    error: 'Too many failed attempts. License temporarily blocked.',
                    retryAfter: rateLimitCheck.retryAfter 
                };
            }

            console.log('Auth', '[AuthenticationHandler] Attempting login with license:', licenseValidation.sanitized);
            
            const result = await this.makeLicenseLoginRequest(licenseValidation.sanitized, deviceFingerprint);
            
            await this.processSuccessfulLicenseLogin(result);
            
            // Mark login as successful for rate limiting
            window.securityManager?.trackLoginAttempt(licenseValidation.sanitized, true);
            
            console.log('Auth', '[AuthenticationHandler] License login successful');
            return { success: true, user: this.currentUser, license: authData.license };
            
        } catch (error) {
            // Mark login as failed for rate limiting
            if (licenseKey) {
                const licenseValidation = window.securityManager?.validateInput(licenseKey, 'licenseKey');
                if (licenseValidation?.valid) {
                    window.securityManager?.trackLoginAttempt(licenseValidation.sanitized, false);
                }
            }
            
            console.error('Auth', '[AuthenticationHandler] License login error:', error);
            return { success: false, error: error.message };
        }
    }

    async makeLicenseLoginRequest(licenseKey, deviceFingerprint) {
        const result = await this.authApiService.loginWithLicense(licenseKey, deviceFingerprint);
        
        if (!result.success) {
            throw new Error(result.error || 'License authentication failed');
        }
        
        return result;
    }

    async processSuccessfulLicenseLogin(result) {
        const authData = this.authApiService.extractAuthData(result);
        
        // Store tokens using TokenManager
        this.tokenManager.storeTokens(authData);
        
        // Store user data
        localStorage.setItem(window.CoreDeskAuth.storage.keys.user, JSON.stringify(authData.user));
        this.currentUser = authData.user;
        
        // Start session monitoring
        window.unifiedAuthManager.sessionManager.startMonitoring();
        
        // Emit login event
        window.dispatchEvent(new CustomEvent(window.CoreDeskAuth.events.onLogin, {
            detail: { user: this.currentUser, fromLicense: true, license: authData.license }
        }));
    }

    async logout() {
        try {
            console.log('Auth', '[AuthenticationHandler] Logging out user');
            
            // Initialize dependencies first
            window.unifiedAuthManager.initializeDependencies();
            
            // Call logout endpoint if we have a token
            const token = window.unifiedAuthManager.tokenManager?.getToken();
            if (token && window.unifiedAuthManager.authApiService) {
                await window.unifiedAuthManager.authApiService.logout();
            }
            
            // Perform cleanup with explicit context
            await this.performLogoutCleanup();
            this.emitLogoutEvent();
            
            console.log('Auth', '[AuthenticationHandler] Logout successful');
            return true;
            
        } catch (error) {
            console.error('Auth', '[AuthenticationHandler] Logout error:', error);
            
            // Still clean up local data even if API call fails
            await this.performLogoutCleanup();
            this.emitLogoutEvent();
            
            return false;
        }
    }

    // makeLogoutRequest method removed - now using this.authApiService.logout()

    async performLogoutCleanup() {
        console.log('Auth', '[AuthenticationHandler] Starting COMPREHENSIVE logout cleanup process...');
        
        try {
            // STEP 1: Record initial state
            const tokensBefore = {
                token: !!localStorage.getItem('coredesk_token'),
                refreshToken: !!localStorage.getItem('coredesk_refresh_token'),
                expiry: !!localStorage.getItem('coredesk_token_expiry'),
                user: !!localStorage.getItem('coredesk_user')
            };
            console.log('Auth', '[AuthenticationHandler] Tokens before cleanup:', tokensBefore);
            
            // STEP 2: Stop all authentication-related timers and monitoring
            console.log('Auth', '[AuthenticationHandler] Stopping all authentication timers...');
            if (window.unifiedAuthManager?.sessionManager?.cleanup) {
                window.unifiedAuthManager.sessionManager.cleanup();
            }
            
            // Stop any AuthGuard periodic checks
            if (window.authGuard?.stopPeriodicCheck) {
                window.authGuard.stopPeriodicCheck();
            }
            
            // STEP 3: Clear in-memory state first
            console.log('Auth', '[AuthenticationHandler] Clearing in-memory authentication state...');
            this.currentUser = null;
            if (window.unifiedAuthManager?.authenticationHandler) {
                window.unifiedAuthManager.authenticationHandler.currentUser = null;
            }
            
            // STEP 4: Prevent any new authentication attempts during cleanup
            console.log('Auth', '[AuthenticationHandler] Setting logout flag to prevent re-authentication...');
            window._isLoggingOut = true;
            
            // STEP 4b: Set persistent logout flags that survive page transitions
            console.log('Auth', '[AuthenticationHandler] Setting PERSISTENT logout protection flags...');
            localStorage.setItem('_forceLogoutActive', 'true');
            localStorage.setItem('_logoutTimestamp', Date.now().toString());
            sessionStorage.setItem('_justLoggedOut', 'true');
            
            // STEP 5: Clear tokens using TokenManager 
            if (window.unifiedAuthManager?.tokenManager && typeof window.unifiedAuthManager.tokenManager.clearTokens === 'function') {
                console.log('Auth', '[AuthenticationHandler] Clearing tokens using TokenManager...');
                window.unifiedAuthManager.tokenManager.clearTokens();
            } else {
                console.warn('Auth', '[AuthenticationHandler] TokenManager clearTokens not available');
            }
            
            // STEP 6: AGGRESSIVE localStorage cleanup
            console.log('Auth', '[AuthenticationHandler] AGGRESSIVE localStorage cleanup...');
            const authKeys = [
                'coredesk_token',
                'coredesk_refresh_token',
                'coredesk_token_expiry',
                'coredesk_user',
                'coredesk_encryption_key',
                'auth_token',
                'auth_user'
                // NOTE: _forceLogoutActive and _logoutTimestamp are NOT cleared here
                // because they need to persist for 10 seconds to prevent re-authentication
            ];
            
            // Try multiple removal methods for each key
            authKeys.forEach(key => {
                const existed = !!localStorage.getItem(key);
                if (existed) {
                    try {
                        localStorage.removeItem(key);
                        localStorage.setItem(key, '');
                        localStorage.removeItem(key);
                        delete localStorage[key];
                        console.log('Auth', `[AuthenticationHandler] AGGRESSIVELY removed ${key}`);
                        
                        // Verify removal
                        const stillExists = !!localStorage.getItem(key);
                        if (stillExists) {
                            console.error('Auth', `[AuthenticationHandler] CRITICAL: ${key} still exists after aggressive removal!`);
                        }
                    } catch (e) {
                        console.error('Auth', `[AuthenticationHandler] Error removing ${key}:`, e);
                    }
                }
            });
            
            // STEP 7: Clear sessionStorage too
            console.log('Auth', '[AuthenticationHandler] Clearing sessionStorage...');
            authKeys.forEach(key => {
                try {
                    sessionStorage.removeItem(key);
                } catch (e) {
                    console.error('Auth', `[AuthenticationHandler] Error clearing sessionStorage ${key}:`, e);
                }
            });
            
            // STEP 8: Use CoreDeskAuth cleanup as backup
            if (window.CoreDeskAuth?.utils?.clearAuthData) {
                console.log('Auth', '[AuthenticationHandler] Using CoreDeskAuth.utils.clearAuthData...');
                window.CoreDeskAuth.utils.clearAuthData();
            }
            
            // STEP 9: Wait a moment to ensure all operations complete
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // STEP 10: Final verification
            const tokensAfter = {
                token: !!localStorage.getItem('coredesk_token'),
                refreshToken: !!localStorage.getItem('coredesk_refresh_token'),
                expiry: !!localStorage.getItem('coredesk_token_expiry'),
                user: !!localStorage.getItem('coredesk_user')
            };
            console.log('Auth', '[AuthenticationHandler] Tokens after cleanup:', tokensAfter);
            
            // Check if cleanup was successful
            const cleanupSuccess = !tokensAfter.token && !tokensAfter.refreshToken && !tokensAfter.expiry && !tokensAfter.user;
            console.log('Auth', '[AuthenticationHandler] Cleanup successful:', cleanupSuccess);
            
            if (!cleanupSuccess) {
                console.error('Auth', '[AuthenticationHandler] CRITICAL: Cleanup failed - tokens still present after aggressive cleanup!');
                // Last resort: clear entire localStorage
                console.log('Auth', '[AuthenticationHandler] LAST RESORT: Clearing entire localStorage...');
                try {
                    const keysToPreserve = ['theme', 'language', 'settings']; // Preserve non-auth data
                    const preservedData = {};
                    keysToPreserve.forEach(key => {
                        const value = localStorage.getItem(key);
                        if (value) preservedData[key] = value;
                    });
                    
                    localStorage.clear();
                    
                    // Restore preserved data
                    Object.entries(preservedData).forEach(([key, value]) => {
                        localStorage.setItem(key, value);
                    });
                    
                    console.log('Auth', '[AuthenticationHandler] localStorage cleared and non-auth data restored');
                } catch (e) {
                    console.error('Auth', '[AuthenticationHandler] Failed to clear localStorage:', e);
                }
            }
            
            console.log('Auth', '[AuthenticationHandler] COMPREHENSIVE logout cleanup completed');
            
        } catch (error) {
            console.error('Auth', '[AuthenticationHandler] Critical error during cleanup:', error);
            // Emergency fallback
            try {
                localStorage.clear();
                console.log('Auth', '[AuthenticationHandler] Emergency localStorage clear performed');
            } catch (e) {
                console.error('Auth', '[AuthenticationHandler] Emergency cleanup failed:', e);
            }
        } finally {
            // Set a longer delay before allowing new authentication to prevent browser auto-submit
            setTimeout(() => {
                window._isLoggingOut = false;
                console.log('Auth', '[AuthenticationHandler] Logout protection period ended - authentication now allowed');
                
                // Note: Persistent flags (_forceLogoutActive, _logoutTimestamp) remain active for 10 seconds
                // and are cleared by authConfig.js when safe
            }, 2000); // 2 second delay to prevent immediate re-authentication
        }
    }

    emitLogoutEvent() {
        window.dispatchEvent(new CustomEvent(window.CoreDeskAuth.events.onLogout));
    }

    async getCurrentProfile() {
        try {
            const result = await this.authApiService.getProfile();
            
            if (!result.success) {
                throw new Error(result.error || 'Failed to fetch user profile');
            }
            
            const profile = result.data;
            
            // Update stored user data
            localStorage.setItem(window.CoreDeskAuth.storage.keys.user, JSON.stringify(profile));
            this.currentUser = profile;
            
            return profile;
            
        } catch (error) {
            console.error('Auth', '[AuthenticationHandler] Error fetching user profile:', error);
            return null;
        }
    }

    async restoreSession() {
        this.currentUser = window.CoreDeskAuth.utils.getCurrentUser();
    }

    getStatus() {
        // Ensure dependencies are initialized
        window.unifiedAuthManager.initializeDependencies();
        
        return {
            isAuthenticated: window.unifiedAuthManager.tokenManager?.isTokenValid() || false,
            currentUser: this.currentUser,
            needsRefresh: window.unifiedAuthManager.tokenManager?.needsRefresh() || false,
            tokenInfo: window.unifiedAuthManager.tokenManager?.getTokenClaims() || {}
        };
    }
}

/**
 * SessionManager class
 * Handles token refresh and session monitoring
 */
class SessionManager {
    constructor() {
        this.tokenRefreshTimer = null;
        this.inactivityTimer = null;
        this.isRefreshing = false;
        this.lastActivity = Date.now();
    }

    startMonitoring() {
        this.startTokenRefreshTimer();
        this.startInactivityMonitor();
    }

    async refreshToken() {
        if (this.isRefreshing) {
            console.log('Auth', '[SessionManager] Token refresh already in progress');
            return false;
        }
        
        // CRITICAL: Check for persistent logout protection first
        const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
        const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
        const now = Date.now();
        
        // If logout was forced within the last 10 seconds, don't refresh tokens
        if (persistentLogoutFlag === 'true' || (logoutTimestamp && (now - parseInt(logoutTimestamp)) < 10000)) {
            console.log('Auth', '[SessionManager] BLOCKED token refresh - Persistent logout protection active');
            return false;
        }
        
        // CRITICAL: Don't refresh tokens if logout is in progress
        if (window._isLoggingOut) {
            console.log('Auth', '[SessionManager] Token refresh skipped - logout in progress');
            return false;
        }
        
        this.isRefreshing = true;
        
        try {
            console.log('Auth', '[SessionManager] Refreshing authentication token');
            
            // Ensure dependencies are initialized
            window.unifiedAuthManager.initializeDependencies();
            
            const refreshToken = window.unifiedAuthManager.tokenManager?.getRefreshToken();
            if (!refreshToken) {
                throw new Error('No refresh token available');
            }
            
            const result = await window.unifiedAuthManager.authApiService?.refreshToken(refreshToken);
            
            if (!result.success) {
                throw new Error(result.error || 'Token refresh failed');
            }
            
            this.processRefreshSuccess(result);
            
            console.log('Auth', '[SessionManager] Token refreshed successfully');
            return true;
            
        } catch (error) {
            console.error('Auth', '[SessionManager] Token refresh error:', error);
            await window.unifiedAuthManager.logout();
            return false;
        } finally {
            this.isRefreshing = false;
        }
    }

    // makeRefreshRequest method removed - now using this.authApiService.refreshToken()

    processRefreshSuccess(result) {
        const authData = window.unifiedAuthManager.authApiService.extractAuthData(result);
        
        // Store new tokens using TokenManager
        window.unifiedAuthManager.tokenManager.storeTokens(authData);
        
        // Emit token refresh event
        window.dispatchEvent(new CustomEvent(window.CoreDeskAuth.events.onTokenRefresh, {
            detail: { newToken: authData.token }
        }));
    }

    startTokenRefreshTimer() {
        if (this.tokenRefreshTimer) {
            clearInterval(this.tokenRefreshTimer);
        }
        
        this.tokenRefreshTimer = setInterval(() => {
            // CRITICAL: Check for persistent logout protection before token refresh
            const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
            const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
            const now = Date.now();
            
            // If logout protection is active, skip token refresh
            if (persistentLogoutFlag === 'true' || (logoutTimestamp && (now - parseInt(logoutTimestamp)) < 10000)) {
                console.log('Auth', '[SessionManager] Timer BLOCKED token refresh - Persistent logout protection active');
                return;
            }
            
            // Don't refresh if logout is in progress
            if (window._isLoggingOut) {
                console.log('Auth', '[SessionManager] Timer skipped token refresh - logout in progress');
                return;
            }
            
            if (window.unifiedAuthManager.tokenManager?.needsRefresh()) {
                this.refreshToken();
            }
        }, window.CoreDeskAuth.session.tokenRefreshInterval);
        
        // Track timer with memory manager
        window.memoryManager?.trackTimer('sessionTokenRefresh', this.tokenRefreshTimer);
        
        console.log('Auth', '[SessionManager] Token refresh timer started');
    }

    startInactivityMonitor() {
        if (this.inactivityTimer) {
            clearInterval(this.inactivityTimer);
        }
        
        // Create throttled inactivity check to reduce memory usage
        const throttledInactivityCheck = window.memoryManager?.createThrottledFunction(() => {
            const timeSinceLastActivity = Date.now() - this.lastActivity;
            
            if (timeSinceLastActivity >= window.CoreDeskAuth.session.maxInactivityTime) {
                console.log('Auth', '[SessionManager] Session expired due to inactivity');
                this.emitSessionExpiredEvent();
                window.unifiedAuthManager.logout();
            }
        }, 5000); // Throttle to every 5 seconds max
        
        this.inactivityTimer = setInterval(throttledInactivityCheck || (() => {
            const timeSinceLastActivity = Date.now() - this.lastActivity;
            if (timeSinceLastActivity >= window.CoreDeskAuth.session.maxInactivityTime) {
                this.emitSessionExpiredEvent();
                window.unifiedAuthManager.logout();
            }
        }), 60 * 1000); // Check every minute
        
        // Track timer with memory manager
        window.memoryManager?.trackTimer('sessionInactivityMonitor', this.inactivityTimer);
        
        console.log('Auth', '[SessionManager] Inactivity monitor started');
    }

    emitSessionExpiredEvent() {
        window.dispatchEvent(new CustomEvent(window.CoreDeskAuth.events.onSessionExpired, {
            detail: { reason: 'inactivity' }
        }));
    }

    updateLastActivity() {
        this.lastActivity = Date.now();
    }

    cleanup() {
        // Clear authentication data is now handled by performCleanup in AuthenticationHandler
        
        // Clear timers using memory manager
        window.memoryManager?.untrackResource('sessionTokenRefresh');
        window.memoryManager?.untrackResource('sessionInactivityMonitor');
        
        // Clear timers manually as fallback
        if (this.tokenRefreshTimer) {
            clearInterval(this.tokenRefreshTimer);
            this.tokenRefreshTimer = null;
        }
        
        if (this.inactivityTimer) {
            clearInterval(this.inactivityTimer);
            this.inactivityTimer = null;
        }
        
        // Reset state
        this.isRefreshing = false;
        
        console.log('Auth', '[SessionManager] Cleanup completed');
    }
}

/**
 * AuthEventHandlers class
 * Handles authentication-related events and user activity monitoring
 */
class AuthEventHandlers {
    constructor() {
        this.activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    }

    setup() {
        this.setupAuthEvents();
        this.setupActivityMonitoring();
        this.setupVisibilityHandling();
    }

    setupAuthEvents() {
        // Listen for authentication events
        window.addEventListener(window.CoreDeskAuth.events.onLogin, (event) => {
            this.handleLoginEvent(event.detail);
        });
        
        window.addEventListener(window.CoreDeskAuth.events.onLogout, () => {
            this.handleLogoutEvent();
        });
    }

    setupActivityMonitoring() {
        // Create debounced activity handler to reduce memory usage
        const debouncedActivityHandler = window.memoryManager?.createDebouncedFunction(() => {
            window.unifiedAuthManager.sessionManager.updateLastActivity();
        }, 1000); // Debounce to once per second
        
        // Monitor user activity for inactivity timeout
        this.activityEvents.forEach(event => {
            const handler = debouncedActivityHandler || (() => {
                window.unifiedAuthManager.sessionManager.updateLastActivity();
            });
            
            document.addEventListener(event, handler, { passive: true });
            
            // Track event listener with memory manager
            window.memoryManager?.trackEventListener(
                `activity_${event}`, 
                document, 
                event, 
                handler, 
                { passive: true }
            );
        });
    }

    setupVisibilityHandling() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                window.unifiedAuthManager.sessionManager.updateLastActivity();
                
                // Check if token needs refresh when page becomes visible
                if (window.unifiedAuthManager.tokenManager?.isTokenValid() && 
                    window.unifiedAuthManager.tokenManager?.needsRefresh()) {
                    window.unifiedAuthManager.refreshToken();
                }
            }
        });
    }

    handleLoginEvent(detail) {
        console.log('Auth', '[AuthEventHandlers] Handling login event:', detail);
        // Additional login event handling can be added here
    }

    handleLogoutEvent() {
        console.log('Auth', '[AuthEventHandlers] Handling logout event');
        // Additional logout event handling can be added here
    }
}

// Create global instance
const unifiedAuthManager = new UnifiedAuthManager();

// Make available globally for browser environment
if (typeof window !== 'undefined') {
    window.UnifiedAuthManager = UnifiedAuthManager;
    window.unifiedAuthManager = unifiedAuthManager;
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = unifiedAuthManager;
}

console.log('Auth', '[UnifiedAuthManager] Global instance created successfully', );