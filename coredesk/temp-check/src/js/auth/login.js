/**
 * Login Page Controller
 * Handles authentication forms, validation, and navigation
 */

class LoginController {
    constructor() {
        this.currentTab = 'login';
        this.currentRegisterStep = 1;
        this.isSubmitting = false;
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }
        
        // Check if already authenticated
        this.checkAuthStatus();
    }
    
    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.auth-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });
        
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }
        
        // Login with license button
        const licenseBtn = document.getElementById('login-with-license');
        if (licenseBtn) {
            licenseBtn.addEventListener('click', () => this.switchTab('activate'));
        }
        
        // Activation form
        const activateForm = document.getElementById('activate-form');
        if (activateForm) {
            activateForm.addEventListener('submit', (e) => this.handleActivation(e));
        }
        
        // License key formatting
        const licenseInput = document.getElementById('license-key');
        if (licenseInput) {
            licenseInput.addEventListener('input', (e) => this.formatLicenseKey(e));
        }
        
        // Paste license button
        const pasteBtn = document.getElementById('paste-license');
        if (pasteBtn) {
            pasteBtn.addEventListener('click', () => this.pasteLicenseKey());
        }
        
        // Request trial link
        const trialLink = document.getElementById('request-trial-link');
        if (trialLink) {
            trialLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTab('register');
            });
        }
        
        // Registration form
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegistration(e));
        }
        
        // Registration step navigation
        document.getElementById('register-next-1')?.addEventListener('click', () => this.validateAndNextStep(1));
        document.getElementById('register-next-2')?.addEventListener('click', () => this.validateAndNextStep(2));
        document.getElementById('register-back-2')?.addEventListener('click', () => this.goToRegisterStep(1));
        document.getElementById('register-back-3')?.addEventListener('click', () => this.goToRegisterStep(2));
        
        // Password strength indicator
        const passwordInput = document.getElementById('register-password');
        if (passwordInput) {
            passwordInput.addEventListener('input', (e) => this.updatePasswordStrength(e.target.value));
        }
        
        // Modal close buttons
        document.getElementById('success-ok')?.addEventListener('click', () => this.closeModal('success-modal'));
        document.getElementById('error-ok')?.addEventListener('click', () => this.closeModal('error-modal'));
        
        // Cancel button (quit app)
        const cancelBtn = document.getElementById('cancel-login');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.handleCancel());
        }
        
        // Enter key navigation
        this.setupEnterKeyNavigation();
    }
    
    setupEnterKeyNavigation() {
        // Allow Enter key to submit forms
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !this.isSubmitting) {
                    const form = input.closest('form');
                    if (form) {
                        const submitBtn = form.querySelector('[type="submit"]');
                        if (submitBtn) {
                            submitBtn.click();
                        }
                    }
                }
            });
        });
    }
    
    checkAuthStatus() {
        // IMPORTANT: Skip authentication check on login page to prevent redirect loops
        // AuthGuard will handle authentication checking for the main app
        console.log('LoginController: Skipping auth check on login page - prevents redirect loops');
        
        // Clear any logout flags that might prevent future authentication
        const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
        const now = Date.now();
        
        // Clear logout protection flags if enough time has passed (10 seconds)
        if (logoutTimestamp && (now - parseInt(logoutTimestamp)) >= 10000) {
            localStorage.removeItem('_forceLogoutActive');
            localStorage.removeItem('_logoutTimestamp');
            console.log('LoginController: Cleared expired logout protection flags');
        }
        
        // Remove any "just logged out" session flag since we're on login page
        sessionStorage.removeItem('_justLoggedOut');
    }
    
    async waitForAuthSystem() {
        // Wait for all authentication dependencies to be available
        let attempts = 0;
        const maxAttempts = 50; // 5 seconds max wait
        
        while (attempts < maxAttempts) {
            if (window.unifiedAuthManager && 
                window.authApiService && 
                window.tokenManager && 
                window.CoreDeskAuth) {
                
                // Initialize dependencies
                if (window.unifiedAuthManager.initializeDependencies) {
                    window.unifiedAuthManager.initializeDependencies();
                }
                
                return; // All dependencies loaded
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
        
        throw new Error('Sistema de autenticación no disponible después de 5 segundos de espera');
    }
    
    switchTab(tab) {
        if (!tab || this.currentTab === tab) return;
        
        // Update tab buttons
        document.querySelectorAll('.auth-tab').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tab);
        });
        
        // Update tab content
        document.querySelectorAll('.auth-tab-content').forEach(content => {
            content.classList.toggle('active', content.id === `${tab}-tab`);
        });
        
        this.currentTab = tab;
        
        // Reset forms when switching tabs
        this.resetForms();
        
        // Focus first input in new tab
        setTimeout(() => {
            const firstInput = document.querySelector(`#${tab}-tab .form-input`);
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }
    
    resetForms() {
        // Clear all form errors
        document.querySelectorAll('.form-error').forEach(error => {
            error.textContent = '';
        });
        
        // Reset register form to step 1
        if (this.currentTab !== 'register') {
            this.goToRegisterStep(1);
        }
    }
    
    async handleLogin(e) {
        e.preventDefault();
        
        // CRITICAL: Block login attempts during logout
        if (window._isLoggingOut) {
            console.log('LoginController: Login blocked - logout in progress');
            this.showError('login-password', 'Sistema de autenticación temporalmente no disponible');
            return;
        }
        
        if (this.isSubmitting) return;
        
        const email = document.getElementById('login-email').value.trim();
        const password = document.getElementById('login-password').value;
        const rememberMe = document.getElementById('remember-me').checked;
        
        // Clear previous errors
        this.clearErrors(['login-email', 'login-password']);
        
        // Validate inputs
        const emailError = this.validateEmail(email);
        if (emailError) {
            this.showError('login-email', emailError);
            return;
        }
        
        if (!password) {
            this.showError('login-password', 'La contraseña es requerida');
            return;
        }
        
        // Show loading state
        this.setSubmitting(true, 'login-submit');
        
        try {
            // Double-check logout flag before proceeding
            if (window._isLoggingOut) {
                throw new Error('Login cancelled due to logout in progress');
            }
            
            // Wait for dependencies to load and initialize
            await this.waitForAuthSystem();
            
            // Attempt login
            const result = await window.unifiedAuthManager.login(email, password, rememberMe);
            
            if (result.success) {
                this.showSuccess('Inicio de sesión exitoso');
                
                // Redirect to main app after short delay
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            } else {
                this.showError('login-password', result.error || 'Credenciales inválidas');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showError('login-password', 'Error al iniciar sesión. Por favor intenta nuevamente.');
        } finally {
            this.setSubmitting(false, 'login-submit');
        }
    }
    
    async handleActivation(e) {
        e.preventDefault();
        
        if (this.isSubmitting) return;
        
        const licenseKey = document.getElementById('license-key').value.trim();
        
        // Clear previous errors
        this.clearErrors(['license-key']);
        
        // Validate license key format
        const licenseError = this.validateLicenseKey(licenseKey);
        if (licenseError) {
            this.showError('license-key', licenseError);
            return;
        }
        
        // Show loading state
        this.setSubmitting(true, 'activate-submit');
        
        try {
            // Wait for dependencies to load and initialize
            await this.waitForAuthSystem();
            
            // Get device fingerprint
            const deviceFingerprint = await this.getDeviceFingerprint();
            
            // Attempt license activation
            const result = await window.unifiedAuthManager.loginWithLicense(licenseKey, deviceFingerprint);
            
            if (result.success) {
                this.showSuccess('Licencia activada exitosamente');
                
                // Redirect to main app
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            } else {
                this.showError('license-key', result.error || 'Licencia inválida o ya activada');
            }
        } catch (error) {
            console.error('Activation error:', error);
            this.showError('license-key', 'Error al activar la licencia. Por favor intenta nuevamente.');
        } finally {
            this.setSubmitting(false, 'activate-submit');
        }
    }
    
    async handleRegistration(e) {
        e.preventDefault();
        
        if (this.isSubmitting) return;
        
        // Validate all steps before submission
        if (!this.validateRegistrationForm()) {
            return;
        }
        
        // Get form data
        const name = document.getElementById('register-name').value.trim();
        const email = document.getElementById('register-email').value.trim();
        const company = document.getElementById('register-company').value.trim();
        const password = document.getElementById('register-password').value;
        const acceptTerms = document.getElementById('accept-terms').checked;
        
        // Basic client validation
        if (!name || !email || !password || !acceptTerms) {
            this.showErrorModal('Por favor completa todos los campos requeridos y acepta los términos.');
            return;
        }
        
        // Split name into firstName and lastName as required by API
        const nameParts = name.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';
        
        if (!firstName) {
            this.showErrorModal('Por favor ingresa tu nombre completo.');
            return;
        }
        
        // Ensure we have at least a lastName (use firstName if only one name provided)
        const finalLastName = lastName || firstName;
        
        // Prepare data according to API schema - matches backend requirements exactly
        const userData = {
            email: email.toLowerCase().trim(),
            password,
            firstName: firstName.trim(),
            lastName: finalLastName.trim(),
            ...(company && { company: company.trim() })
        };
        
        // Show loading state
        this.setSubmitting(true, 'register-submit');
        
        try {
            // Use AuthApiService for registration
            const result = await window.authApiService.register(userData);
            
            if (result.success) {
                this.showSuccess('Cuenta creada exitosamente. Revisa tu correo para activarla.');
                
                // Switch to login tab after delay
                setTimeout(() => {
                    this.switchTab('login');
                    document.getElementById('login-email').value = email;
                }, 2000);
            } else {
                this.showErrorModal(result.error || 'Error al crear la cuenta');
            }
        } catch (error) {
            console.error('Registration error:', error);
            this.showErrorModal('Error al crear la cuenta. Por favor intenta nuevamente.');
        } finally {
            this.setSubmitting(false, 'register-submit');
        }
    }
    
    validateAndNextStep(currentStep) {
        let isValid = true;
        
        if (currentStep === 1) {
            // Validate step 1
            const name = document.getElementById('register-name').value.trim();
            const email = document.getElementById('register-email').value.trim();
            
            this.clearErrors(['register-name', 'register-email']);
            
            if (!name) {
                this.showError('register-name', 'El nombre es requerido');
                isValid = false;
            }
            
            const emailError = this.validateEmail(email);
            if (emailError) {
                this.showError('register-email', emailError);
                isValid = false;
            }
        } else if (currentStep === 2) {
            // Validate step 2
            const password = document.getElementById('register-password').value;
            const confirm = document.getElementById('register-confirm').value;
            
            this.clearErrors(['register-password', 'register-confirm']);
            
            const passwordError = this.validatePassword(password);
            if (passwordError) {
                this.showError('register-password', passwordError);
                isValid = false;
            }
            
            if (password !== confirm) {
                this.showError('register-confirm', 'Las contraseñas no coinciden');
                isValid = false;
            }
        }
        
        if (isValid) {
            this.goToRegisterStep(currentStep + 1);
        }
    }
    
    goToRegisterStep(step) {
        // Update step visibility
        document.querySelectorAll('.register-step').forEach(stepEl => {
            stepEl.classList.toggle('active', stepEl.dataset.step == step);
        });
        
        // Update progress indicator
        document.querySelectorAll('.progress-step').forEach(indicator => {
            indicator.classList.toggle('active', indicator.dataset.step <= step);
        });
        
        this.currentRegisterStep = step;
        
        // Focus first input in new step
        setTimeout(() => {
            const firstInput = document.querySelector(`.register-step[data-step="${step}"] .form-input`);
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }
    
    validateRegistrationForm() {
        // Validate all fields
        const name = document.getElementById('register-name').value.trim();
        const email = document.getElementById('register-email').value.trim();
        const password = document.getElementById('register-password').value;
        const confirm = document.getElementById('register-confirm').value;
        const acceptTerms = document.getElementById('accept-terms').checked;
        
        this.clearErrors(['register-name', 'register-email', 'register-password', 'register-confirm', 'terms']);
        
        let isValid = true;
        
        if (!name) {
            this.showError('register-name', 'El nombre es requerido');
            this.goToRegisterStep(1);
            isValid = false;
        }
        
        const emailError = this.validateEmail(email);
        if (emailError) {
            this.showError('register-email', emailError);
            if (isValid) this.goToRegisterStep(1);
            isValid = false;
        }
        
        const passwordError = this.validatePassword(password);
        if (passwordError) {
            this.showError('register-password', passwordError);
            if (isValid) this.goToRegisterStep(2);
            isValid = false;
        }
        
        if (password !== confirm) {
            this.showError('register-confirm', 'Las contraseñas no coinciden');
            if (isValid) this.goToRegisterStep(2);
            isValid = false;
        }
        
        if (!acceptTerms) {
            this.showError('terms', 'Debes aceptar los términos y condiciones');
            isValid = false;
        }
        
        return isValid;
    }
    
    formatLicenseKey(e) {
        let value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        let formatted = '';
        
        for (let i = 0; i < value.length && i < 16; i++) {
            if (i > 0 && i % 4 === 0) {
                formatted += '-';
            }
            formatted += value[i];
        }
        
        e.target.value = formatted;
    }
    
    async pasteLicenseKey() {
        try {
            const text = await navigator.clipboard.readText();
            const licenseInput = document.getElementById('license-key');
            licenseInput.value = text;
            licenseInput.dispatchEvent(new Event('input'));
        } catch (error) {
            console.error('Failed to read clipboard:', error);
            this.showError('license-key', 'No se pudo pegar desde el portapapeles');
        }
    }
    
    updatePasswordStrength(password) {
        let strength = 0;
        const strengthBar = document.getElementById('password-strength');
        const strengthText = document.getElementById('strength-text');
        
        if (!strengthBar || !strengthText) return;
        
        // Check password strength
        if (password.length >= 8) strength++;
        if (password.length >= 12) strength++;
        if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        
        // Cap at 4
        strength = Math.min(strength, 4);
        
        // Update UI
        strengthBar.setAttribute('data-strength', strength);
        strengthBar.style.width = `${strength * 25}%`;
        
        const strengthLabels = ['', 'Débil', 'Regular', 'Buena', 'Excelente'];
        strengthText.textContent = strengthLabels[strength];
    }
    
    async getDeviceFingerprint() {
        // Get device fingerprint from the device fingerprint service
        if (window.deviceFingerprint) {
            return await window.deviceFingerprint.generate();
        }
        
        // Fallback to basic fingerprint
        return btoa(navigator.userAgent + screen.width + screen.height);
    }
    
    validateEmail(email) {
        if (!email) {
            return 'El correo electrónico es requerido';
        }
        
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return 'Formato de correo electrónico inválido';
        }
        
        return null;
    }
    
    validatePassword(password) {
        if (!password) {
            return 'La contraseña es requerida';
        }
        
        if (password.length < 8) {
            return 'La contraseña debe tener al menos 8 caracteres';
        }
        
        return null;
    }
    
    validateLicenseKey(key) {
        if (!key) {
            return 'La clave de licencia es requerida';
        }
        
        // Remove hyphens for validation
        const cleanKey = key.replace(/-/g, '');
        
        if (cleanKey.length !== 16) {
            return 'La clave de licencia debe tener 16 caracteres';
        }
        
        if (!/^[A-Z0-9]+$/.test(cleanKey)) {
            return 'La clave de licencia solo debe contener letras y números';
        }
        
        return null;
    }
    
    showError(fieldId, message) {
        const errorEl = document.getElementById(`${fieldId}-error`);
        if (errorEl) {
            errorEl.textContent = message;
        }
        
        // Add error class to input
        const input = document.getElementById(fieldId);
        if (input) {
            input.classList.add('error');
        }
    }
    
    clearErrors(fieldIds) {
        fieldIds.forEach(fieldId => {
            const errorEl = document.getElementById(`${fieldId}-error`);
            if (errorEl) {
                errorEl.textContent = '';
            }
            
            // Remove error class from input
            const input = document.getElementById(fieldId);
            if (input) {
                input.classList.remove('error');
            }
        });
    }
    
    setSubmitting(isSubmitting, buttonId) {
        this.isSubmitting = isSubmitting;
        const button = document.getElementById(buttonId);
        
        if (button) {
            button.disabled = isSubmitting;
            const textEl = button.querySelector('.btn-text');
            const loaderEl = button.querySelector('.btn-loader');
            
            if (textEl) textEl.style.display = isSubmitting ? 'none' : 'inline';
            if (loaderEl) loaderEl.style.display = isSubmitting ? 'inline-flex' : 'none';
        }
    }
    
    showSuccess(message) {
        const modal = document.getElementById('success-modal');
        const messageEl = document.getElementById('success-message');
        
        if (modal && messageEl) {
            messageEl.textContent = message;
            modal.style.display = 'flex';
        }
    }
    
    showErrorModal(message) {
        const modal = document.getElementById('error-modal');
        const messageEl = document.getElementById('error-message');
        
        if (modal && messageEl) {
            messageEl.textContent = message;
            modal.style.display = 'flex';
        }
    }
    
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    handleCancel() {
        console.log('LoginController: Cancel button clicked - closing application');
        
        try {
            // Use electron API to quit the application
            if (window.electronAPI && window.electronAPI.app && window.electronAPI.app.quit) {
                window.electronAPI.app.quit();
            } else if (window.coreDesk && window.coreDesk.app && window.coreDesk.app.quit) {
                window.coreDesk.app.quit();
            } else {
                // Fallback for development or if electronAPI is not available
                console.warn('LoginController: electronAPI not available, using window.close()');
                window.close();
            }
        } catch (error) {
            console.error('LoginController: Error closing application:', error);
            // Final fallback
            window.close();
        }
    }
}

// Initialize login controller
const loginController = new LoginController();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LoginController;
}