/**
 * MemoryManager.js
 * Memory management and leak prevention system for CoreDesk Framework
 * Handles cleanup of resources, event listeners, and async operations
 */

class MemoryManager {
    constructor() {
        this.trackedResources = new Map();
        this.activeTimers = new Set();
        this.eventListeners = new Map();
        this.abortControllers = new Map();
        this.observedElements = new WeakMap();
        this.cleanupCallbacks = new Set();
        
        this.memoryStats = {
            totalAllocations: 0,
            totalDeallocations: 0,
            currentAllocations: 0,
            peakMemory: 0
        };
        
        this.initialize();
    }

    /**
     * Initialize memory manager
     */
    initialize() {
        // Wait for logger to be available
        this.waitForLogger().then(() => {
            this.logger.info('Memory', '[MemoryManager] Initializing memory management system...', );
            
            this.setupMemoryMonitoring();
            this.setupGlobalCleanup();
            this.patchGlobalMethods();
            
            this.logger.info('Memory', '[MemoryManager] Memory management system initialized', );
        });
    }

    /**
     * Wait for logger to be available
     */
    async waitForLogger() {
        return new Promise((resolve) => {
            const checkLogger = () => {
                if (window.logger) {
                    this.logger = window.logger;
                    resolve();
                } else {
                    setTimeout(checkLogger, 50);
                }
            };
            checkLogger();
        });
    }

    /**
     * Setup memory monitoring
     */
    setupMemoryMonitoring() {
        // Monitor memory usage every 30 seconds
        this.memoryMonitorTimer = setInterval(() => {
            this.checkMemoryUsage();
        }, 30000);

        // Track this timer
        this.trackTimer('memoryMonitor', this.memoryMonitorTimer);

        // Monitor page visibility for cleanup opportunities
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.performMaintenanceCleanup();
            }
        });
    }

    /**
     * Setup global cleanup handlers
     */
    setupGlobalCleanup() {
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.performFullCleanup();
        });

        // Cleanup on navigation
        window.addEventListener('popstate', () => {
            this.performNavigationCleanup();
        });
    }

    /**
     * Patch global methods to track resources
     */
    patchGlobalMethods() {
        // Track setTimeout and setInterval
        this.patchTimerMethods();
        
        // Track fetch requests
        this.patchFetchMethod();
        
        // Track event listeners
        this.patchEventListenerMethods();
    }

    /**
     * Patch timer methods for tracking
     */
    patchTimerMethods() {
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;
        const originalClearTimeout = window.clearTimeout;
        const originalClearInterval = window.clearInterval;

        window.setTimeout = (callback, delay, ...args) => {
            const id = originalSetTimeout.call(window, (...callbackArgs) => {
                try {
                    callback(...callbackArgs);
                } finally {
                    this.activeTimers.delete(id);
                }
            }, delay, ...args);
            
            this.activeTimers.add(id);
            return id;
        };

        window.setInterval = (callback, interval, ...args) => {
            const id = originalSetInterval.call(window, callback, interval, ...args);
            this.activeTimers.add(id);
            return id;
        };

        window.clearTimeout = (id) => {
            this.activeTimers.delete(id);
            return originalClearTimeout.call(window, id);
        };

        window.clearInterval = (id) => {
            this.activeTimers.delete(id);
            return originalClearInterval.call(window, id);
        };
    }

    /**
     * Patch fetch method for tracking
     */
    patchFetchMethod() {
        if (!window.fetch._memoryManagerPatched) {
            const originalFetch = window.fetch;
            
            window.fetch = (url, options = {}) => {
                // Create abort controller if not provided
                if (!options.signal) {
                    const controller = new AbortController();
                    options.signal = controller.signal;
                    
                    // Track the controller
                    const requestId = this.generateRequestId();
                    this.abortControllers.set(requestId, controller);
                    
                    // Auto-cleanup after reasonable timeout
                    setTimeout(() => {
                        if (this.abortControllers.has(requestId)) {
                            controller.abort();
                            this.abortControllers.delete(requestId);
                        }
                    }, 30000); // 30 second timeout
                }
                
                return originalFetch.call(window, url, options);
            };
            
            window.fetch._memoryManagerPatched = true;
        }
    }

    /**
     * Patch event listener methods for tracking
     */
    patchEventListenerMethods() {
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        const originalRemoveEventListener = EventTarget.prototype.removeEventListener;

        EventTarget.prototype.addEventListener = function(type, listener, options) {
            // Track the listener
            if (!this._memoryManagerListeners) {
                this._memoryManagerListeners = new Map();
            }
            
            if (!this._memoryManagerListeners.has(type)) {
                this._memoryManagerListeners.set(type, new Set());
            }
            
            this._memoryManagerListeners.get(type).add(listener);
            
            return originalAddEventListener.call(this, type, listener, options);
        };

        EventTarget.prototype.removeEventListener = function(type, listener, options) {
            // Untrack the listener
            if (this._memoryManagerListeners && this._memoryManagerListeners.has(type)) {
                this._memoryManagerListeners.get(type).delete(listener);
                
                if (this._memoryManagerListeners.get(type).size === 0) {
                    this._memoryManagerListeners.delete(type);
                }
            }
            
            return originalRemoveEventListener.call(this, type, listener, options);
        };
    }

    /**
     * Track a resource for cleanup
     */
    trackResource(id, resource, cleanupFn) {
        this.trackedResources.set(id, {
            resource,
            cleanupFn,
            createdAt: Date.now(),
            type: this.getResourceType(resource)
        });
        
        this.memoryStats.totalAllocations++;
        this.memoryStats.currentAllocations++;
    }

    /**
     * Track a timer for cleanup
     */
    trackTimer(id, timerId) {
        this.trackResource(id, timerId, () => {
            if (typeof timerId === 'number') {
                clearTimeout(timerId);
                clearInterval(timerId);
            }
        });
    }

    /**
     * Track an event listener for cleanup
     */
    trackEventListener(id, element, type, listener, options) {
        this.trackResource(id, { element, type, listener, options }, () => {
            element.removeEventListener(type, listener, options);
        });
    }

    /**
     * Track an abort controller for cleanup
     */
    trackAbortController(id, controller) {
        this.trackResource(id, controller, () => {
            if (!controller.signal.aborted) {
                controller.abort();
            }
        });
    }

    /**
     * Untrack and cleanup a resource
     */
    untrackResource(id) {
        const tracked = this.trackedResources.get(id);
        if (tracked) {
            try {
                tracked.cleanupFn();
                this.memoryStats.totalDeallocations++;
                this.memoryStats.currentAllocations--;
            } catch (error) {
                this.logger.error('Memory', '[MemoryManager] Error cleaning up resource:', error);
            }
            
            this.trackedResources.delete(id);
        }
    }

    /**
     * Create memory-efficient async function wrapper
     */
    createAsyncWrapper(asyncFn, timeoutMs = 30000) {
        return async (...args) => {
            const controller = new AbortController();
            const requestId = this.generateRequestId();
            
            try {
                this.trackAbortController(requestId, controller);
                
                // Create timeout promise
                const timeoutPromise = new Promise((_, reject) => {
                    const timeoutId = setTimeout(() => {
                        controller.abort();
                        reject(new Error(`Operation timed out after ${timeoutMs}ms`));
                    }, timeoutMs);
                    
                    // Track timeout
                    this.trackTimer(`timeout_${requestId}`, timeoutId);
                });
                
                // Race between the async function and timeout
                const result = await Promise.race([
                    asyncFn(...args),
                    timeoutPromise
                ]);
                
                return result;
            } finally {
                this.untrackResource(requestId);
                this.untrackResource(`timeout_${requestId}`);
            }
        };
    }

    /**
     * Create debounced function to prevent excessive calls
     */
    createDebouncedFunction(fn, delayMs = 300) {
        let timeoutId = null;
        
        return (...args) => {
            if (timeoutId) {
                clearTimeout(timeoutId);
                this.activeTimers.delete(timeoutId);
            }
            
            timeoutId = setTimeout(() => {
                try {
                    fn(...args);
                } finally {
                    this.activeTimers.delete(timeoutId);
                    timeoutId = null;
                }
            }, delayMs);
            
            this.activeTimers.add(timeoutId);
        };
    }

    /**
     * Create throttled function to limit call frequency
     */
    createThrottledFunction(fn, limitMs = 100) {
        let lastCall = 0;
        let timeoutId = null;
        
        return (...args) => {
            const now = Date.now();
            
            if (now - lastCall >= limitMs) {
                lastCall = now;
                fn(...args);
            } else if (!timeoutId) {
                timeoutId = setTimeout(() => {
                    lastCall = Date.now();
                    fn(...args);
                    this.activeTimers.delete(timeoutId);
                    timeoutId = null;
                }, limitMs - (now - lastCall));
                
                this.activeTimers.add(timeoutId);
            }
        };
    }

    /**
     * Check memory usage and perform cleanup if needed
     */
    checkMemoryUsage() {
        try {
            // Update peak memory
            if (this.memoryStats.currentAllocations > this.memoryStats.peakMemory) {
                this.memoryStats.peakMemory = this.memoryStats.currentAllocations;
            }
            
            // Check if cleanup is needed
            if (this.memoryStats.currentAllocations > 1000) {
                this.logger.warn('Memory', '[MemoryManager] High memory usage detected, performing cleanup', );
                this.performMaintenanceCleanup();
            }
            
            // Log memory stats periodically
            if (this.memoryStats.currentAllocations > 0) {
                this.logger.info('Memory', `[MemoryManager] Memory stats: ${this.memoryStats.currentAllocations} active, ${this.memoryStats.peakMemory} peak`, );
            }
        } catch (error) {
            this.logger.error('Memory', '[MemoryManager] Error checking memory usage:', error);
        }
    }

    /**
     * Perform maintenance cleanup
     */
    performMaintenanceCleanup() {
        const now = Date.now();
        const maxAge = 5 * 60 * 1000; // 5 minutes
        
        // Clean up old resources
        for (const [id, tracked] of this.trackedResources.entries()) {
            if (now - tracked.createdAt > maxAge) {
                this.untrackResource(id);
            }
        }
        
        // Clean up orphaned abort controllers
        for (const [id, controller] of this.abortControllers.entries()) {
            if (controller.signal.aborted) {
                this.abortControllers.delete(id);
            }
        }
        
        // Trigger garbage collection if available
        if (window.gc && typeof window.gc === 'function') {
            window.gc();
        }
        
        this.logger.info('Memory', '[MemoryManager] Maintenance cleanup completed', );
    }

    /**
     * Perform navigation cleanup
     */
    performNavigationCleanup() {
        // Clean up page-specific resources
        this.cleanupPageResources();
        
        // Reset some stats
        this.memoryStats.peakMemory = this.memoryStats.currentAllocations;
        
        this.logger.info('Memory', '[MemoryManager] Navigation cleanup completed', );
    }

    /**
     * Perform full cleanup on page unload
     */
    performFullCleanup() {
        // Clean up all tracked resources
        for (const id of this.trackedResources.keys()) {
            this.untrackResource(id);
        }
        
        // Clear all active timers
        for (const timerId of this.activeTimers) {
            clearTimeout(timerId);
            clearInterval(timerId);
        }
        this.activeTimers.clear();
        
        // Abort all active requests
        for (const controller of this.abortControllers.values()) {
            if (!controller.signal.aborted) {
                controller.abort();
            }
        }
        this.abortControllers.clear();
        
        // Run cleanup callbacks
        for (const callback of this.cleanupCallbacks) {
            try {
                callback();
            } catch (error) {
                this.logger.error('Memory', '[MemoryManager] Error in cleanup callback:', error);
            }
        }
        
        this.logger.info('Memory', '[MemoryManager] Full cleanup completed', );
    }

    /**
     * Clean up page-specific resources
     */
    cleanupPageResources() {
        // Remove event listeners from DOM elements
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
            if (element._memoryManagerListeners) {
                for (const [type, listeners] of element._memoryManagerListeners.entries()) {
                    for (const listener of listeners) {
                        element.removeEventListener(type, listener);
                    }
                }
                element._memoryManagerListeners.clear();
            }
        });
    }

    /**
     * Register cleanup callback
     */
    registerCleanupCallback(callback) {
        this.cleanupCallbacks.add(callback);
    }

    /**
     * Unregister cleanup callback
     */
    unregisterCleanupCallback(callback) {
        this.cleanupCallbacks.delete(callback);
    }

    /**
     * Generate unique request ID
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Get resource type
     */
    getResourceType(resource) {
        if (typeof resource === 'number') return 'timer';
        if (resource instanceof AbortController) return 'abort_controller';
        if (resource && resource.element) return 'event_listener';
        return 'unknown';
    }

    /**
     * Get memory statistics
     */
    getMemoryStats() {
        return {
            ...this.memoryStats,
            activeTimers: this.activeTimers.size,
            trackedResources: this.trackedResources.size,
            abortControllers: this.abortControllers.size,
            cleanupCallbacks: this.cleanupCallbacks.size
        };
    }

    /**
     * Force garbage collection and cleanup
     */
    forceCleanup() {
        this.performMaintenanceCleanup();
        
        // Request garbage collection
        if (window.gc && typeof window.gc === 'function') {
            window.gc();
        }
        
        this.logger.info('Memory', '[MemoryManager] Forced cleanup completed', );
    }
}

// Create global instance
window.memoryManager = new MemoryManager();

console.log('[MemoryManager] Global instance created successfully');