/**
 * GlobalInit.js
 * Global initialization to resolve circular dependencies
 * This should be loaded after all core components are loaded
 */

class GlobalInit {
    /**
     * Initialize all components and resolve circular dependencies
     */
    static initializeComponents() {
        console.log('[GlobalInit] Initializing CoreDesk components...');
        
        // Initialize core dependencies first
        if (window.inputValidator) {
            window.inputValidator.initializeDependencies();
            console.log('[GlobalInit] InputValidator dependencies initialized');
        }
        
        if (window.securityManager) {
            window.securityManager.initializeDependencies();
            window.securityManager.initialize(); // Start security monitoring
            console.log('[GlobalInit] SecurityManager dependencies initialized');
        }

        // Initialize API services
        if (window.authApiService) {
            window.authApiService.initializeDependencies();
            console.log('[GlobalInit] AuthApiService dependencies initialized');
        }
        
        if (window.licenseApiService) {
            window.licenseApiService.initializeDependencies();
            console.log('[GlobalInit] LicenseApiService dependencies initialized');
        }
        
        // Initialize authentication manager
        if (window.unifiedAuthManager && window.unifiedAuthManager.initializeDependencies) {
            window.unifiedAuthManager.initializeDependencies();
            console.log('[GlobalInit] UnifiedAuthManager dependencies initialized');
        }
        
        if (window.licenseManager && window.licenseManager.initializeDependencies) {
            window.licenseManager.initializeDependencies();
            console.log('[GlobalInit] LicenseManager dependencies initialized');
        }
        
        if (window.dataSyncService && window.dataSyncService.initializeDependencies) {
            window.dataSyncService.initializeDependencies();
            console.log('[GlobalInit] DataSyncService dependencies initialized');
        }
        
        console.log('[GlobalInit] All CoreDesk components initialized successfully');
        
        // Emit initialization complete event
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('coredeskInitialized', {
                detail: { timestamp: new Date().toISOString() }
            }));
        }
    }
    
    /**
     * Initialize when DOM is ready
     */
    static initializeWhenReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                // Add a small delay to ensure all scripts have finished loading
                setTimeout(() => {
                    GlobalInit.initializeComponents();
                }, 100);
            });
        } else {
            // DOM is already ready
            setTimeout(() => {
                GlobalInit.initializeComponents();
            }, 100);
        }
    }
}

// Make available globally
if (typeof window !== 'undefined') {
    window.GlobalInit = GlobalInit;
    
    // Auto-initialize when script loads
    GlobalInit.initializeWhenReady();
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GlobalInit };
}

console.log('[GlobalInit] Global initialization system loaded');