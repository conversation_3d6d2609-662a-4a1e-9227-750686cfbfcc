/**
 * Global event management utilities
 * Provides centralized event handling and custom event utilities
 */

window.CoreDeskEvents = {
    // Event listeners registry
    listeners: new Map(),
    
    // Debug mode flag
    debug: false,
    
    /**
     * Add an event listener
     * @param {string} eventName - Name of the event
     * @param {Function} callback - Callback function
     * @param {Object} options - Event options
     */
    on(eventName, callback, options = {}) {
        if (!this.listeners.has(eventName)) {
            this.listeners.set(eventName, []);
        }
        
        const listener = {
            callback,
            options,
            id: Date.now() + Math.random()
        };
        
        this.listeners.get(eventName).push(listener);
        
        // Also add to DOM for global events
        if (options.global !== false) {
            window.addEventListener(eventName, callback, options);
        }
        
        if (this.debug) {
            console.log("Component", `[CoreDeskEvents] Listener added for: ${eventName}`);
        }
        
        return listener.id;
    },
    
    /**
     * Remove an event listener
     * @param {string} eventName - Name of the event
     * @param {Function|number} callbackOrId - Callback function or listener ID
     */
    off(eventName, callbackOrId) {
        const listeners = this.listeners.get(eventName);
        if (!listeners) return;
        
        let removed = false;
        
        if (typeof callbackOrId === 'number') {
            // Remove by ID
            const index = listeners.findIndex(l => l.id === callbackOrId);
            if (index !== -1) {
                const listener = listeners[index];
                listeners.splice(index, 1);
                window.removeEventListener(eventName, listener.callback);
                removed = true;
            }
        } else {
            // Remove by callback function
            const index = listeners.findIndex(l => l.callback === callbackOrId);
            if (index !== -1) {
                listeners.splice(index, 1);
                window.removeEventListener(eventName, callbackOrId);
                removed = true;
            }
        }
        
        if (removed && this.debug) {
            console.log("Component", `[CoreDeskEvents] Listener removed for: ${eventName}`);
        }
    },
    
    /**
     * Emit a custom event
     * @param {string} eventName - Name of the event
     * @param {*} detail - Event detail data
     * @param {Object} options - Event options
     */
    emit(eventName, detail = null, options = {}) {
        const event = new CustomEvent(eventName, {
            detail,
            bubbles: options.bubbles !== false,
            cancelable: options.cancelable !== false,
            ...options
        });
        
        if (this.debug) {
            console.log(`[CoreDeskEvents] Emitting event: ${eventName}`, detail);
        }
        
        // Dispatch to window for global handling
        window.dispatchEvent(event);
        
        return event;
    },
    
    /**
     * Add a one-time event listener
     * @param {string} eventName - Name of the event
     * @param {Function} callback - Callback function
     * @param {Object} options - Event options
     */
    once(eventName, callback, options = {}) {
        const onceCallback = (event) => {
            callback(event);
            this.off(eventName, onceCallback);
        };
        
        return this.on(eventName, onceCallback, { ...options, once: true });
    },
    
    /**
     * Wait for an event to be emitted
     * @param {string} eventName - Name of the event
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise} Promise that resolves with event detail
     */
    waitFor(eventName, timeout = 5000) {
        return new Promise((resolve, reject) => {
            let timeoutId;
            
            const cleanup = () => {
                if (timeoutId) clearTimeout(timeoutId);
            };
            
            const listener = (event) => {
                cleanup();
                resolve(event.detail);
            };
            
            this.once(eventName, listener);
            
            if (timeout > 0) {
                timeoutId = setTimeout(() => {
                    this.off(eventName, listener);
                    reject(new Error(`Event ${eventName} timeout after ${timeout}ms`));
                }, timeout);
            }
        });
    },
    
    /**
     * Create an event emitter for a specific namespace
     * @param {string} namespace - Event namespace
     * @returns {Object} Namespaced event emitter
     */
    createNamespace(namespace) {
        return {
            on: (eventName, callback, options) => {
                return this.on(`${namespace}:${eventName}`, callback, options);
            },
            off: (eventName, callbackOrId) => {
                return this.off(`${namespace}:${eventName}`, callbackOrId);
            },
            emit: (eventName, detail, options) => {
                return this.emit(`${namespace}:${eventName}`, detail, options);
            },
            once: (eventName, callback, options) => {
                return this.once(`${namespace}:${eventName}`, callback, options);
            },
            waitFor: (eventName, timeout) => {
                return this.waitFor(`${namespace}:${eventName}`, timeout);
            }
        };
    },
    
    /**
     * Get all registered listeners for an event
     * @param {string} eventName - Name of the event
     * @returns {Array} Array of listeners
     */
    getListeners(eventName) {
        return this.listeners.get(eventName) || [];
    },
    
    /**
     * Remove all listeners for an event
     * @param {string} eventName - Name of the event (optional)
     */
    removeAllListeners(eventName) {
        if (eventName) {
            const listeners = this.listeners.get(eventName) || [];
            listeners.forEach(listener => {
                window.removeEventListener(eventName, listener.callback);
            });
            this.listeners.delete(eventName);
            
            if (this.debug) {
                console.log("Component", `[CoreDeskEvents] All listeners removed for: ${eventName}`);
            }
        } else {
            // Remove all listeners
            this.listeners.forEach((listeners, eventName) => {
                listeners.forEach(listener => {
                    window.removeEventListener(eventName, listener.callback);
                });
            });
            this.listeners.clear();
            
            if (this.debug) {
                console.log('events', '[CoreDeskEvents] All listeners removed', );
            }
        }
    },
    
    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether to enable debug mode
     */
    setDebug(enabled) {
        this.debug = enabled;
        console.log("Component", `[CoreDeskEvents] Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    },
    
    /**
     * Get event statistics
     * @returns {Object} Event statistics
     */
    getStats() {
        const stats = {
            totalEvents: this.listeners.size,
            totalListeners: 0,
            eventBreakdown: {}
        };
        
        this.listeners.forEach((listeners, eventName) => {
            stats.totalListeners += listeners.length;
            stats.eventBreakdown[eventName] = listeners.length;
        });
        
        return stats;
    }
};

// Create convenient shortcuts for common events
window.CoreDeskEvents.module = window.CoreDeskEvents.createNamespace('module');
window.CoreDeskEvents.tab = window.CoreDeskEvents.createNamespace('tab');
window.CoreDeskEvents.panel = window.CoreDeskEvents.createNamespace('panel');
window.CoreDeskEvents.auth = window.CoreDeskEvents.createNamespace('auth');
window.CoreDeskEvents.sync = window.CoreDeskEvents.createNamespace('sync');
window.CoreDeskEvents.license = window.CoreDeskEvents.createNamespace('license');

// Set up global error handling for events
window.addEventListener('error', (event) => {
    window.CoreDeskEvents.emit(window.COREDESK_CONSTANTS.EVENTS.APP_ERROR, {
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
    });
});

window.addEventListener('unhandledrejection', (event) => {
    window.CoreDeskEvents.emit(window.COREDESK_CONSTANTS.EVENTS.APP_ERROR, {
        type: 'promise',
        message: event.reason?.message || 'Unhandled promise rejection',
        reason: event.reason
    });
});

// Enable debug mode in development
if (typeof process !== 'undefined' && process?.argv?.includes('--dev')) {
    window.CoreDeskEvents.setDebug(true);
}

console.log('[CoreDeskEvents] Global event system initialized');