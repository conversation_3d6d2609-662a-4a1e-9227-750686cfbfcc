/**
 * MainLogger.js
 * Simple logging system for main process
 */

class MainLogger {
    constructor() {
        this.logLevel = process.argv.includes('--dev') ? 'debug' : 'info';
        this.levels = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3
        };
        this.isWSL = this.detectWSL();
        this.fallbackLogging = false;
    }

    detectWSL() {
        try {
            const fs = require('fs');
            if (fs.existsSync('/proc/version')) {
                const version = fs.readFileSync('/proc/version', 'utf8');
                return version.toLowerCase().includes('microsoft') ||
                       version.toLowerCase().includes('wsl');
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    shouldLog(level) {
        return this.levels[level] >= this.levels[this.logLevel];
    }

    formatMessage(level, component, message) {
        const timestamp = new Date().toISOString();
        return `[${timestamp}] [${level.toUpperCase()}] [${component}] ${message}`;
    }

    /**
     * Safe console logging that handles EIO errors in WSL2
     */
    safeConsoleLog(method, message, data = null) {
        try {
            if (data !== null && data !== '') {
                console[method](message, data);
            } else {
                console[method](message);
            }
        } catch (error) {
            // Handle EIO errors in WSL2
            if (error.code === 'EIO' || error.message.includes('write EIO')) {
                if (!this.fallbackLogging) {
                    this.fallbackLogging = true;
                    // Try to write to stderr as fallback
                    try {
                        process.stderr.write(`[LOGGER-FALLBACK] ${message}\n`);
                    } catch (fallbackError) {
                        // If even stderr fails, silently continue
                        // This prevents the application from crashing
                    }
                }
            } else {
                // Re-throw non-EIO errors
                throw error;
            }
        }
    }

    debug(component, message, data = null) {
        if (this.shouldLog('debug')) {
            const formatted = this.formatMessage('debug', component, message);
            this.safeConsoleLog('debug', formatted, data);
        }
    }

    info(component, message, data = null) {
        if (this.shouldLog('info')) {
            const formatted = this.formatMessage('info', component, message);
            this.safeConsoleLog('log', formatted, data);
        }
    }

    warn(component, message, data = null) {
        if (this.shouldLog('warn')) {
            const formatted = this.formatMessage('warn', component, message);
            this.safeConsoleLog('warn', formatted, data);
        }
    }

    error(component, message, data = null) {
        if (this.shouldLog('error')) {
            const formatted = this.formatMessage('error', component, message);
            this.safeConsoleLog('error', formatted, data);
        }
    }
}

module.exports = new MainLogger();