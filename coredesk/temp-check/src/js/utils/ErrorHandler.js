/**
 * ErrorHandler.js
 * Centralized error handling system for CoreDesk
 * Provides standardized error processing, logging, and user-friendly messages
 */

class ErrorHandler {
    constructor() {
        this.errorTypes = {
            NETWORK: 'network',
            AUTH: 'authentication',
            VALIDATION: 'validation',
            FIREBASE: 'firebase',
            LICENSE: 'license',
            SYNC: 'synchronization',
            STORAGE: 'storage',
            UNKNOWN: 'unknown'
        };
        
        this.errorCodes = {
            // Network errors
            NETWORK_TIMEOUT: 'E001',
            NETWORK_OFFLINE: 'E002',
            NETWORK_UNREACHABLE: 'E003',
            
            // Authentication errors
            AUTH_INVALID_CREDENTIALS: 'E101',
            AUTH_TOKEN_EXPIRED: 'E102',
            AUTH_ACCESS_DENIED: 'E103',
            AUTH_FIREBASE_FAILED: 'E104',
            
            // Validation errors
            VALIDATION_REQUIRED_FIELD: 'E201',
            VALIDATION_INVALID_FORMAT: 'E202',
            VALIDATION_SIZE_LIMIT: 'E203',
            
            // Firebase errors
            FIREBASE_PERMISSION_DENIED: 'E301',
            FIREBASE_NOT_FOUND: 'E302',
            FIREBASE_QUOTA_EXCEEDED: 'E303',
            FIREBASE_UNAVAILABLE: 'E304',
            
            // License errors
            LICENSE_INVALID: 'E401',
            LICENSE_EXPIRED: 'E402',
            LICENSE_FEATURE_RESTRICTED: 'E403',
            
            // Sync errors
            SYNC_CONFLICT: 'E501',
            SYNC_FAILED: 'E502',
            SYNC_OFFLINE: 'E503',
            
            // Storage errors
            STORAGE_FULL: 'E601',
            STORAGE_CORRUPT: 'E602',
            STORAGE_ACCESS_DENIED: 'E603'
        };
        
        this.setupGlobalHandlers();
    }
    
    /**
     * Setup global error handlers
     */
    setupGlobalHandlers() {
        // Handle unhandled promise rejections
        if (typeof window !== 'undefined') {
            window.addEventListener('unhandledrejection', (event) => {
                console.error('Unhandled promise rejection:', event.reason);
                this.handleError(event.reason, 'unhandled-promise');
                event.preventDefault();
            });
            
            // Handle uncaught exceptions
            window.addEventListener('error', (event) => {
                console.error('Uncaught error:', event.error);
                this.handleError(event.error, 'uncaught-exception');
            });
        }
        
        // Node.js process handlers
        if (typeof process !== 'undefined') {
            process.on('unhandledRejection', (reason, promise) => {
                console.error('Unhandled Rejection at:', promise, 'reason:', reason);
                this.handleError(reason, 'unhandled-promise');
            });
            
            process.on('uncaughtException', (error) => {
                console.error('Uncaught Exception:', error);
                this.handleError(error, 'uncaught-exception');
            });
        }
    }
    
    /**
     * Main error handling method
     * @param {Error|Object} error - Error object or error data
     * @param {string} context - Context where error occurred
     * @param {Object} metadata - Additional error metadata
     * @returns {Object} Processed error object
     */
    handleError(error, context = 'unknown', metadata = {}) {
        const processedError = this.processError(error, context, metadata);
        
        // Log the error
        this.logError(processedError);
        
        // Send to monitoring if configured
        this.reportError(processedError);
        
        return processedError;
    }
    
    /**
     * Process and standardize error object
     * @param {Error|Object} error - Raw error
     * @param {string} context - Error context
     * @param {Object} metadata - Additional metadata
     * @returns {Object} Standardized error object
     */
    processError(error, context, metadata) {
        const timestamp = new Date().toISOString();
        const errorId = this.generateErrorId();
        
        // Extract error information
        let errorInfo = {
            id: errorId,
            timestamp,
            context,
            metadata: {
                ...metadata,
                userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js',
                url: typeof window !== 'undefined' ? window.location.href : 'N/A'
            }
        };
        
        if (error instanceof Error) {
            errorInfo = {
                ...errorInfo,
                name: error.name,
                message: error.message,
                stack: error.stack,
                type: this.determineErrorType(error),
                code: this.determineErrorCode(error)
            };
        } else if (typeof error === 'object' && error !== null) {
            errorInfo = {
                ...errorInfo,
                name: error.name || 'UnknownError',
                message: error.message || 'Unknown error occurred',
                type: error.type || this.errorTypes.UNKNOWN,
                code: error.code || this.errorCodes.UNKNOWN,
                data: error.data || null
            };
        } else {
            errorInfo = {
                ...errorInfo,
                name: 'UnknownError',
                message: String(error) || 'Unknown error occurred',
                type: this.errorTypes.UNKNOWN,
                code: 'E999'
            };
        }
        
        // Add user-friendly message
        errorInfo.userMessage = this.getUserFriendlyMessage(errorInfo);
        
        // Determine severity
        errorInfo.severity = this.determineSeverity(errorInfo);
        
        // Add recovery suggestions
        errorInfo.recovery = this.getRecoverySuggestions(errorInfo);
        
        return errorInfo;
    }
    
    /**
     * Determine error type from error object
     * @param {Error} error - Error object
     * @returns {string} Error type
     */
    determineErrorType(error) {
        const message = error.message.toLowerCase();
        const name = error.name.toLowerCase();
        
        if (message.includes('network') || message.includes('timeout') || message.includes('fetch')) {
            return this.errorTypes.NETWORK;
        }
        
        if (message.includes('auth') || message.includes('token') || message.includes('permission')) {
            return this.errorTypes.AUTH;
        }
        
        if (message.includes('firebase') || name.includes('firebase')) {
            return this.errorTypes.FIREBASE;
        }
        
        if (message.includes('license') || message.includes('activation')) {
            return this.errorTypes.LICENSE;
        }
        
        if (message.includes('sync') || message.includes('conflict')) {
            return this.errorTypes.SYNC;
        }
        
        if (message.includes('validation') || message.includes('invalid')) {
            return this.errorTypes.VALIDATION;
        }
        
        if (message.includes('storage') || message.includes('quota')) {
            return this.errorTypes.STORAGE;
        }
        
        return this.errorTypes.UNKNOWN;
    }
    
    /**
     * Determine specific error code
     * @param {Error} error - Error object
     * @returns {string} Error code
     */
    determineErrorCode(error) {
        const message = error.message.toLowerCase();
        
        // Network errors
        if (message.includes('timeout')) return this.errorCodes.NETWORK_TIMEOUT;
        if (message.includes('offline')) return this.errorCodes.NETWORK_OFFLINE;
        if (message.includes('unreachable')) return this.errorCodes.NETWORK_UNREACHABLE;
        
        // Auth errors
        if (message.includes('invalid credentials')) return this.errorCodes.AUTH_INVALID_CREDENTIALS;
        if (message.includes('token expired')) return this.errorCodes.AUTH_TOKEN_EXPIRED;
        if (message.includes('access denied')) return this.errorCodes.AUTH_ACCESS_DENIED;
        
        // Firebase errors
        if (message.includes('permission-denied')) return this.errorCodes.FIREBASE_PERMISSION_DENIED;
        if (message.includes('not-found')) return this.errorCodes.FIREBASE_NOT_FOUND;
        if (message.includes('quota-exceeded')) return this.errorCodes.FIREBASE_QUOTA_EXCEEDED;
        
        // License errors
        if (message.includes('invalid license')) return this.errorCodes.LICENSE_INVALID;
        if (message.includes('license expired')) return this.errorCodes.LICENSE_EXPIRED;
        
        // Sync errors
        if (message.includes('conflict')) return this.errorCodes.SYNC_CONFLICT;
        if (message.includes('sync failed')) return this.errorCodes.SYNC_FAILED;
        
        return 'E999'; // Unknown error
    }
    
    /**
     * Generate user-friendly error message
     * @param {Object} errorInfo - Processed error info
     * @returns {string} User-friendly message
     */
    getUserFriendlyMessage(errorInfo) {
        const codeMessages = {
            [this.errorCodes.NETWORK_TIMEOUT]: 'La conexión ha tardado demasiado. Por favor, verifica tu conexión a internet.',
            [this.errorCodes.NETWORK_OFFLINE]: 'No hay conexión a internet. Algunas funciones pueden no estar disponibles.',
            [this.errorCodes.AUTH_INVALID_CREDENTIALS]: 'Las credenciales ingresadas no son válidas. Por favor, verifica tu email y contraseña.',
            [this.errorCodes.AUTH_TOKEN_EXPIRED]: 'Tu sesión ha expirado. Por favor, inicia sesión nuevamente.',
            [this.errorCodes.FIREBASE_PERMISSION_DENIED]: 'No tienes permisos para acceder a esta información.',
            [this.errorCodes.LICENSE_INVALID]: 'La licencia no es válida. Por favor, verifica tu clave de licencia.',
            [this.errorCodes.LICENSE_EXPIRED]: 'Tu licencia ha expirado. Por favor, renueva tu suscripción.',
            [this.errorCodes.SYNC_CONFLICT]: 'Se ha detectado un conflicto de sincronización. Los datos serán revisados automáticamente.',
            [this.errorCodes.STORAGE_FULL]: 'El almacenamiento está lleno. Por favor, libera espacio antes de continuar.'
        };
        
        return codeMessages[errorInfo.code] || 'Ha ocurrido un error inesperado. Por favor, inténtalo de nuevo.';
    }
    
    /**
     * Determine error severity
     * @param {Object} errorInfo - Error information
     * @returns {string} Severity level
     */
    determineSeverity(errorInfo) {
        const criticalTypes = [this.errorTypes.AUTH, this.errorTypes.LICENSE];
        const warningTypes = [this.errorTypes.NETWORK, this.errorTypes.SYNC];
        
        if (criticalTypes.includes(errorInfo.type)) {
            return 'critical';
        } else if (warningTypes.includes(errorInfo.type)) {
            return 'warning';
        } else {
            return 'info';
        }
    }
    
    /**
     * Get recovery suggestions for error
     * @param {Object} errorInfo - Error information
     * @returns {Array} Recovery suggestions
     */
    getRecoverySuggestions(errorInfo) {
        const suggestions = {
            [this.errorCodes.NETWORK_TIMEOUT]: [
                'Verifica tu conexión a internet',
                'Intenta nuevamente en unos segundos',
                'Cambia a una red más estable si es posible'
            ],
            [this.errorCodes.AUTH_TOKEN_EXPIRED]: [
                'Inicia sesión nuevamente',
                'Verifica que tu email y contraseña sean correctos'
            ],
            [this.errorCodes.LICENSE_EXPIRED]: [
                'Renueva tu licencia',
                'Contacta al soporte técnico',
                'Verifica el estado de tu suscripción'
            ],
            [this.errorCodes.STORAGE_FULL]: [
                'Libera espacio eliminando archivos innecesarios',
                'Mueve archivos a almacenamiento externo',
                'Actualiza tu plan de almacenamiento'
            ]
        };
        
        return suggestions[errorInfo.code] || [
            'Inténtalo de nuevo',
            'Reinicia la aplicación si el problema persiste',
            'Contacta al soporte técnico si necesitas ayuda'
        ];
    }
    
    /**
     * Log error to console and storage
     * @param {Object} errorInfo - Processed error information
     */
    logError(errorInfo) {
        // Console logging with appropriate level
        const logLevel = this.getLogLevel(errorInfo.severity);
        console[logLevel](`[${errorInfo.id}] ${errorInfo.name}: ${errorInfo.message}`, {
            context: errorInfo.context,
            code: errorInfo.code,
            type: errorInfo.type,
            timestamp: errorInfo.timestamp,
            stack: errorInfo.stack
        });
        
        // Store in local storage for debugging
        this.storeErrorLocally(errorInfo);
    }
    
    /**
     * Get appropriate console log level
     * @param {string} severity - Error severity
     * @returns {string} Console method name
     */
    getLogLevel(severity) {
        const levels = {
            critical: 'error',
            warning: 'warn',
            info: 'info'
        };
        return levels[severity] || 'error';
    }
    
    /**
     * Store error locally for debugging
     * @param {Object} errorInfo - Error information
     */
    storeErrorLocally(errorInfo) {
        try {
            if (typeof localStorage !== 'undefined') {
                const errorLog = JSON.parse(localStorage.getItem('coredesk_error_log') || '[]');
                
                // Keep only last 50 errors
                if (errorLog.length >= 50) {
                    errorLog.shift();
                }
                
                errorLog.push({
                    id: errorInfo.id,
                    timestamp: errorInfo.timestamp,
                    type: errorInfo.type,
                    code: errorInfo.code,
                    message: errorInfo.message,
                    context: errorInfo.context,
                    severity: errorInfo.severity
                });
                
                localStorage.setItem('coredesk_error_log', JSON.stringify(errorLog));
            }
        } catch (e) {
            console.warn('Failed to store error locally:', e);
        }
    }
    
    /**
     * Report error to monitoring service
     * @param {Object} errorInfo - Error information
     */
    reportError(errorInfo) {
        // Only report critical errors in production
        if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'production' && errorInfo.severity === 'critical') {
            // TODO: Implement error reporting to monitoring service
            console.log('Error reported to monitoring service:', errorInfo.id);
        }
    }
    
    /**
     * Generate unique error ID
     * @returns {string} Unique error ID
     */
    generateErrorId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return `ERR_${timestamp}_${random}`.toUpperCase();
    }
    
    /**
     * Get error statistics
     * @returns {Object} Error statistics
     */
    getErrorStats() {
        try {
            if (typeof localStorage !== 'undefined') {
                const errorLog = JSON.parse(localStorage.getItem('coredesk_error_log') || '[]');
                
                const stats = {
                    total: errorLog.length,
                    byType: {},
                    bySeverity: {},
                    recent: errorLog.slice(-10)
                };
                
                errorLog.forEach(error => {
                    stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
                    stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
                });
                
                return stats;
            }
        } catch (e) {
            console.warn('Failed to get error statistics:', e);
        }
        
        return { total: 0, byType: {}, bySeverity: {}, recent: [] };
    }
    
    /**
     * Clear error log
     */
    clearErrorLog() {
        try {
            if (typeof localStorage !== 'undefined') {
                localStorage.removeItem('coredesk_error_log');
                console.log('Error log cleared');
            }
        } catch (e) {
            console.warn('Failed to clear error log:', e);
        }
    }
    
    /**
     * Create retry wrapper for functions
     * @param {Function} fn - Function to retry
     * @param {Object} options - Retry options
     * @returns {Function} Wrapped function with retry logic
     */
    createRetryWrapper(fn, options = {}) {
        const {
            maxRetries = 3,
            delay = 1000,
            backoff = 2,
            retryCondition = (error) => true
        } = options;
        
        return async (...args) => {
            let lastError;
            
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    return await fn(...args);
                } catch (error) {
                    lastError = error;
                    
                    if (attempt === maxRetries || !retryCondition(error)) {
                        throw this.handleError(error, `retry-failed-after-${attempt}-attempts`);
                    }
                    
                    const waitTime = delay * Math.pow(backoff, attempt - 1);
                    console.warn(`Attempt ${attempt} failed, retrying in ${waitTime}ms:`, error.message);
                    
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                }
            }
            
            throw lastError;
        };
    }
}

// Create singleton instance
const errorHandler = new ErrorHandler();

// Make available globally for browser environment
if (typeof window !== 'undefined') {
    window.ErrorHandler = ErrorHandler;
    window.errorHandler = errorHandler;
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ErrorHandler,
        errorHandler
    };
}