/**
 * Centralized logging system for CoreDesk Framework
 * Provides structured logging with levels, categories, and persistence
 */

class Logger {
    constructor() {
        this.levels = {
            DEBUG: 0,
            INFO: 1,
            WARN: 2,
            ERROR: 3,
            FATAL: 4
        };
        
        this.levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
        this.currentLevel = this.levels.INFO;
        this.maxLogEntries = 1000;
        this.logBuffer = [];
        this.categories = new Set();
        this.enabled = true;
        
        // Initialize
        this.initialize();
    }

    /**
     * Initialize the logger
     */
    initialize() {
        // Set log level based on environment
        if (typeof process !== 'undefined' && process.argv?.includes('--dev')) {
            this.currentLevel = this.levels.DEBUG;
        }
        
        // Load existing logs from localStorage
        this.loadLogsFromStorage();
        
        // Set up automatic log persistence
        this.setupAutoPersistence();
        
        console.log('[Logger] Initialized successfully');
    }

    /**
     * Set the minimum log level
     * @param {string|number} level - Log level name or number
     */
    setLevel(level) {
        if (typeof level === 'string') {
            level = this.levels[level.toUpperCase()];
        }
        
        if (level >= 0 && level <= 4) {
            this.currentLevel = level;
            this.info('Logger', `Log level set to: ${this.levelNames[level]}`);
        }
    }

    /**
     * Enable or disable logging
     * @param {boolean} enabled - Whether logging is enabled
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        console.log("Component", `[Logger] Logging ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Log a debug message
     * @param {string} category - Log category
     * @param {string} message - Log message
     * @param {*} data - Additional data
     */
    debug(category, message, data = null) {
        this.log(this.levels.DEBUG, category, message, data);
    }

    /**
     * Log an info message
     * @param {string} category - Log category
     * @param {string} message - Log message
     * @param {*} data - Additional data
     */
    info(category, message, data = null) {
        this.log(this.levels.INFO, category, message, data);
    }

    /**
     * Log a warning message
     * @param {string} category - Log category
     * @param {string} message - Log message
     * @param {*} data - Additional data
     */
    warn(category, message, data = null) {
        this.log(this.levels.WARN, category, message, data);
    }

    /**
     * Log an error message
     * @param {string} category - Log category
     * @param {string} message - Log message
     * @param {*} data - Additional data
     */
    error(category, message, data = null) {
        this.log(this.levels.ERROR, category, message, data);
    }

    /**
     * Log a fatal error message
     * @param {string} category - Log category
     * @param {string} message - Log message
     * @param {*} data - Additional data
     */
    fatal(category, message, data = null) {
        this.log(this.levels.FATAL, category, message, data);
    }

    /**
     * Log a message with specified level
     * @param {number} level - Log level
     * @param {string} category - Log category
     * @param {string} message - Log message
     * @param {*} data - Additional data
     */
    log(level, category, message, data = null) {
        if (!this.enabled || level < this.currentLevel) {
            return;
        }

        const logEntry = {
            timestamp: new Date().toISOString(),
            level: this.levelNames[level],
            category,
            message,
            data,
            id: Date.now() + Math.random()
        };

        // Add to buffer
        this.logBuffer.push(logEntry);
        this.categories.add(category);

        // Maintain buffer size
        if (this.logBuffer.length > this.maxLogEntries) {
            this.logBuffer.shift();
        }

        // Output to console with appropriate method
        this.outputToConsole(logEntry);

        // Emit log event for external listeners
        window.CoreDeskEvents?.emit('coredesk:log:entry', logEntry);
    }

    /**
     * Output log entry to console
     * @param {Object} logEntry - Log entry object
     */
    outputToConsole(logEntry) {
        const { timestamp, level, category, message, data } = logEntry;
        const timeStr = new Date(timestamp).toLocaleTimeString();
        const logMessage = `[${timeStr}] [${level}] [${category}] ${message}`;

        switch (level) {
            case 'DEBUG':
                console.debug(logMessage, data || '');
                break;
            case 'INFO':
                console.info(logMessage, data || '');
                break;
            case 'WARN':
                console.warn(logMessage, data || '');
                break;
            case 'ERROR':
            case 'FATAL':
                console.error(logMessage, data || '');
                if (data instanceof Error) {
                    console.error(data.stack);
                }
                break;
            default:
                console.log(logMessage, data || '');
        }
    }

    /**
     * Get logs by criteria
     * @param {Object} criteria - Filter criteria
     * @returns {Array} Filtered log entries
     */
    getLogs(criteria = {}) {
        let logs = [...this.logBuffer];

        if (criteria.level) {
            const minLevel = typeof criteria.level === 'string' 
                ? this.levels[criteria.level.toUpperCase()] 
                : criteria.level;
            logs = logs.filter(log => this.levels[log.level] >= minLevel);
        }

        if (criteria.category) {
            logs = logs.filter(log => log.category === criteria.category);
        }

        if (criteria.since) {
            const sinceDate = new Date(criteria.since);
            logs = logs.filter(log => new Date(log.timestamp) >= sinceDate);
        }

        if (criteria.limit) {
            logs = logs.slice(-criteria.limit);
        }

        return logs;
    }

    /**
     * Get all log categories
     * @returns {Array} Array of categories
     */
    getCategories() {
        return Array.from(this.categories).sort();
    }

    /**
     * Clear all logs
     */
    clear() {
        this.logBuffer = [];
        this.categories.clear();
        this.clearStoredLogs();
        this.info('Logger', 'Log buffer cleared');
    }

    /**
     * Export logs as JSON
     * @param {Object} criteria - Filter criteria
     * @returns {string} JSON string of logs
     */
    exportLogs(criteria = {}) {
        const logs = this.getLogs(criteria);
        return JSON.stringify({
            exportedAt: new Date().toISOString(),
            totalEntries: logs.length,
            logs
        }, null, 2);
    }

    /**
     * Create a category-specific logger
     * @param {string} category - Category name
     * @returns {Object} Category logger
     */
    createCategoryLogger(category) {
        return {
            debug: (message, data) => this.debug(category, message, data),
            info: (message, data) => this.info(category, message, data),
            warn: (message, data) => this.warn(category, message, data),
            error: (message, data) => this.error(category, message, data),
            fatal: (message, data) => this.fatal(category, message, data)
        };
    }

    /**
     * Set up automatic log persistence
     */
    setupAutoPersistence() {
        // Save logs every 30 seconds
        setInterval(() => {
            this.saveLogsToStorage();
        }, 30000);

        // Save logs before page unload
        window.addEventListener('beforeunload', () => {
            this.saveLogsToStorage();
        });
    }

    /**
     * Save logs to localStorage
     */
    saveLogsToStorage() {
        try {
            const logsToStore = this.logBuffer.slice(-500); // Store last 500 entries
            localStorage.setItem('coredesk_logs', JSON.stringify(logsToStore));
        } catch (error) {
            console.error('Logger', '[Logger] Failed to save logs to storage:', error);
        }
    }

    /**
     * Load logs from localStorage
     */
    loadLogsFromStorage() {
        try {
            const storedLogs = localStorage.getItem('coredesk_logs');
            if (storedLogs) {
                const logs = JSON.parse(storedLogs);
                this.logBuffer = Array.isArray(logs) ? logs : [];
                
                // Rebuild categories set
                this.logBuffer.forEach(log => {
                    if (log.category) {
                        this.categories.add(log.category);
                    }
                });
            }
        } catch (error) {
            console.error('Logger', '[Logger] Failed to load logs from storage:', error);
            this.logBuffer = [];
        }
    }

    /**
     * Clear stored logs
     */
    clearStoredLogs() {
        try {
            localStorage.removeItem('coredesk_logs');
        } catch (error) {
            console.error('Logger', '[Logger] Failed to clear stored logs:', error);
        }
    }

    /**
     * Get logger statistics
     * @returns {Object} Logger statistics
     */
    getStats() {
        const stats = {
            totalEntries: this.logBuffer.length,
            categoriesCount: this.categories.size,
            levelBreakdown: {},
            categoryBreakdown: {},
            oldestEntry: null,
            newestEntry: null
        };

        // Calculate level breakdown
        this.levelNames.forEach(level => {
            stats.levelBreakdown[level] = this.logBuffer.filter(log => log.level === level).length;
        });

        // Calculate category breakdown
        this.categories.forEach(category => {
            stats.categoryBreakdown[category] = this.logBuffer.filter(log => log.category === category).length;
        });

        // Get oldest and newest entries
        if (this.logBuffer.length > 0) {
            stats.oldestEntry = this.logBuffer[0].timestamp;
            stats.newestEntry = this.logBuffer[this.logBuffer.length - 1].timestamp;
        }

        return stats;
    }
}

// Create global logger instance
window.Logger = new Logger();

// Create convenience loggers for common categories
window.Logger.auth = window.Logger.createCategoryLogger('Auth');
window.Logger.module = window.Logger.createCategoryLogger('Module');
window.Logger.tab = window.Logger.createCategoryLogger('Tab');
window.Logger.panel = window.Logger.createCategoryLogger('Panel');
window.Logger.sync = window.Logger.createCategoryLogger('Sync');
window.Logger.license = window.Logger.createCategoryLogger('License');
window.Logger.database = window.Logger.createCategoryLogger('Database');
window.Logger.ui = window.Logger.createCategoryLogger('UI');

console.log('[Logger] Global logging system ready');