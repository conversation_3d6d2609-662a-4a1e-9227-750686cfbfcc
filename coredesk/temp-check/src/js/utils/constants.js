/**
 * Global constants for CoreDesk Framework
 */

// Application constants
window.COREDESK_CONSTANTS = {
    // Application info
    APP_NAME: 'CoreDesk Framework',
    APP_VERSION: '0.0.2',
    APP_BUILD: 'dev',
    
    // Module definitions - Now handled by Dynamic Module System
    // Legacy modules will be loaded dynamically
    MODULES: {
        // Dynamic modules will be registered here at runtime
        // Static definitions removed to support dynamic loading
    },
    
    // Panel types
    PANELS: {
        LEFT: 'left',
        RIGHT: 'right',
        BOTTOM: 'bottom'
    },
    
    // Activity bar items
    ACTIVITY_ITEMS: {
        EXPLORER: 'explorer',
        CLOUD: 'cloud',
        SEARCH: 'search',
        MODULES: 'modules',
        EXTENSIONS: 'extensions'
    },
    
    // Tab types
    TAB_TYPES: {
        DASHBOARD: 'dashboard',
        DOCUMENT: 'document',
        FORM: 'form',
        LIST: 'list',
        SETTINGS: 'settings'
    },
    
    // Event names
    EVENTS: {
        // Module events
        MODULE_SWITCHED: 'coredesk:module:switched',
        MODULE_LOADING: 'coredesk:module:loading',
        MODULE_LOADED: 'coredesk:module:loaded',
        MODULE_ERROR: 'coredesk:module:error',
        
        // Tab events
        TAB_CREATED: 'coredesk:tab:created',
        TAB_ACTIVATED: 'coredesk:tab:activated',
        TAB_CLOSED: 'coredesk:tab:closed',
        TAB_CHANGED: 'coredesk:tab:changed',
        
        // Panel events
        PANEL_OPENED: 'coredesk:panel:opened',
        PANEL_CLOSED: 'coredesk:panel:closed',
        PANEL_RESIZED: 'coredesk:panel:resized',
        
        // License events
        LICENSE_ACTIVATED: 'coredesk:license:activated',
        LICENSE_EXPIRED: 'coredesk:license:expired',
        LICENSE_TRIAL_REQUESTED: 'coredesk:license:trial-requested',
        
        // Sync events
        SYNC_STARTED: 'coredesk:sync:started',
        SYNC_COMPLETED: 'coredesk:sync:completed',
        SYNC_ERROR: 'coredesk:sync:error',
        SYNC_PROGRESS: 'coredesk:sync:progress',
        
        // App events
        APP_READY: 'coredesk:app:ready',
        APP_ERROR: 'coredesk:app:error',
        THEME_CHANGED: 'coredesk:theme:changed',
        LANGUAGE_CHANGED: 'coredesk:language:changed'
    },
    
    // Storage keys
    STORAGE_KEYS: {
        ACTIVE_MODULE: 'coredesk_active_module',
        TAB_STATE: 'coredesk_tab_state',
        PANEL_STATE: 'coredesk_panel_state',
        USER_SETTINGS: 'coredesk_user_settings',
        LICENSE_DATA: 'coredesk_license',
        DEVICE_FINGERPRINT: 'coredesk_device_fingerprint'
    },
    
    // API endpoints (extending auth config)
    API_ENDPOINTS: {
        // License endpoints
        TRIAL_ELIGIBILITY: '/licenses/trial-eligibility',
        REQUEST_TRIAL: '/licenses/request-trial',
        VALIDATE_LICENSE: '/licenses/validate',
        UPDATE_STORAGE: '/licenses/update-storage',
        
        // Sync endpoints
        SYNC_PUSH: '/sync/push',
        SYNC_PULL: '/sync/pull',
        
        // Module endpoints
        MODULE_DATA: '/modules/{moduleCode}/data',
        MODULE_SETTINGS: '/modules/{moduleCode}/settings'
    },
    
    // License types
    LICENSE_TYPES: {
        TRIAL: 'trial',
        PREMIUM: 'premium',
        ENTERPRISE: 'enterprise'
    },
    
    // License status
    LICENSE_STATUS: {
        ACTIVE: 'active',
        INACTIVE: 'inactive',
        EXPIRED: 'expired',
        SUSPENDED: 'suspended'
    },
    
    // Supported countries (Latinoamérica focus)
    COUNTRIES: {
        US: { code: 'US', name: 'Estados Unidos', flag: '🇺🇸' },
        MX: { code: 'MX', name: 'México', flag: '🇲🇽' },
        ES: { code: 'ES', name: 'España', flag: '🇪🇸' },
        AR: { code: 'AR', name: 'Argentina', flag: '🇦🇷' },
        CO: { code: 'CO', name: 'Colombia', flag: '🇨🇴' },
        CL: { code: 'CL', name: 'Chile', flag: '🇨🇱' },
        PE: { code: 'PE', name: 'Perú', flag: '🇵🇪' },
        VE: { code: 'VE', name: 'Venezuela', flag: '🇻🇪' },
        EC: { code: 'EC', name: 'Ecuador', flag: '🇪🇨' },
        GT: { code: 'GT', name: 'Guatemala', flag: '🇬🇹' },
        CU: { code: 'CU', name: 'Cuba', flag: '🇨🇺' },
        BO: { code: 'BO', name: 'Bolivia', flag: '🇧🇴' },
        DO: { code: 'DO', name: 'República Dominicana', flag: '🇩🇴' },
        HN: { code: 'HN', name: 'Honduras', flag: '🇭🇳' },
        PY: { code: 'PY', name: 'Paraguay', flag: '🇵🇾' },
        SV: { code: 'SV', name: 'El Salvador', flag: '🇸🇻' },
        NI: { code: 'NI', name: 'Nicaragua', flag: '🇳🇮' },
        CR: { code: 'CR', name: 'Costa Rica', flag: '🇨🇷' },
        PA: { code: 'PA', name: 'Panamá', flag: '🇵🇦' },
        UY: { code: 'UY', name: 'Uruguay', flag: '🇺🇾' },
        BR: { code: 'BR', name: 'Brasil', flag: '🇧🇷' }
    },
    
    // Supported languages
    LANGUAGES: {
        ES: { code: 'es', name: 'Español', nativeName: 'Español' },
        EN: { code: 'en', name: 'English', nativeName: 'English' },
        PT: { code: 'pt', name: 'Português', nativeName: 'Português' }
    },
    
    // Theme options
    THEMES: {
        DARK: 'dark',
        LIGHT: 'light'
    },
    
    // Density options
    DENSITY: {
        COMPACT: 'compact',
        NORMAL: 'normal',
        SPACIOUS: 'spacious'
    },
    
    // Default settings
    DEFAULT_SETTINGS: {
        theme: 'dark',
        language: 'es',
        country: 'US',
        density: 'normal',
        animations: true,
        autoSync: true,
        syncInterval: 5, // minutes
        notifications: true
    },
    
    // Database table names
    DB_TABLES: {
        USERS: 'coredesk_users',
        LICENSES: 'coredesk_licenses',
        SETTINGS: 'coredesk_settings',
        SYNC_LOG: 'coredesk_sync_log',
        
        // Dynamic module tables will be created at runtime
        // Static module table definitions removed
        MODULE_REGISTRY: 'coredesk_module_registry',
        MODULE_DATA: 'coredesk_module_data'
    },
    
    // File size limits
    FILE_LIMITS: {
        MAX_UPLOAD_SIZE: 50 * 1024 * 1024, // 50MB
        ALLOWED_EXTENSIONS: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt', '.rtf', '.jpg', '.jpeg', '.png'],
        IMAGE_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.svg'],
        DOCUMENT_EXTENSIONS: ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
        SPREADSHEET_EXTENSIONS: ['.xls', '.xlsx', '.csv']
    },
    
    // Sync configuration
    SYNC_CONFIG: {
        BATCH_SIZE: 100,
        MAX_RETRIES: 3,
        RETRY_DELAY: 5000, // 5 seconds
        CONFLICT_STRATEGIES: {
            LAST_WRITE_WINS: 'last-write-wins',
            MERGE: 'merge',
            MANUAL: 'manual'
        }
    },
    
    // Performance thresholds
    PERFORMANCE: {
        LOAD_TIMEOUT: 30000, // 30 seconds
        API_TIMEOUT: 10000, // 10 seconds
        DEBOUNCE_DELAY: 300, // 300ms
        ANIMATION_DURATION: 200 // 200ms
    }
};

// Freeze the constants object to prevent modifications
Object.freeze(window.COREDESK_CONSTANTS);

console.log('[Constants] Global constants loaded successfully');