/**
 * InputValidator.js
 * Comprehensive input validation and sanitization system
 * Provides security validation for all user inputs across the application
 * Enhanced with ErrorHandler integration and advanced security features
 */

// ErrorHandler will be available globally
// const { errorHandler } = require('./ErrorHandler');

class InputValidator {
    constructor() {
        // Initialize errorHandler reference lazily to avoid circular dependencies
        this.errorHandler = null;
        this.patterns = {
            email: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
            licenseKey: /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/,
            verificationCode: /^[0-9]{6}$/,
            alphanumeric: /^[a-zA-Z0-9]+$/,
            deviceId: /^[a-f0-9]{8,64}$/,
            fileName: /^[a-zA-Z0-9._-]+$/,
            path: /^[a-zA-Z0-9._/-]+$/,
            uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
            phoneNumber: /^[\+]?[1-9][\d]{0,15}$/,
            safeString: /^[a-zA-Z0-9\s\-_.,()\[\]]+$/,
            url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
            ipAddress: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
        };
        
        this.maxLengths = {
            email: 254,
            password: 128,
            licenseKey: 19,
            verificationCode: 6,
            fileName: 255,
            message: 1000,
            username: 50,
            deviceName: 100
        };
        
        this.dangerousPatterns = [
            // Script injection patterns (more specific)
            /<script[\s\S]*?<\/script>/gi,
            /javascript\s*:/gi,
            // Event handler patterns (more specific to avoid false positives)
            /\bon\w+\s*=\s*["'][^"']*["']/gi,
            /\bon\w+\s*=\s*[^"'\s>][^\s>]*/gi,
            // Data URLs with executable content
            /data:text\/html/gi,
            /data:.*base64.*script/gi,
            /vbscript\s*:/gi,
            // CSS expression and imports
            /expression\s*\(/gi,
            /import\s*\(/gi,
            // Direct eval calls (but not in legitimate contexts)
            /\beval\s*\(/gi,
            // SQL injection patterns (more targeted)
            /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\s+(all\s+)?.*?(from|into|where|set|values)\b)/gi,
            /('+.*?(-{2}|\/\*|\*\/|;))/gi,
            // XSS patterns (more specific to avoid HTML comments)
            /(<|%3C)(\/*)(s|%73|%53)(c|%63|%43)(r|%72|%52)(i|%69|%49)(p|%70|%50)(t|%74|%54)(\s|%20)/gi,
            // Dangerous JavaScript calls in attributes
            /(document\.(write|writeln|cookie|location|URL)|window\.(open|location|eval)|eval\(|setTimeout\(|setInterval\(|Function\()/gi,
            // Command injection patterns (more specific)
            /(\||&&|;|`|\$\(|\${).*?(rm|cat|ls|chmod|curl|wget|nc|telnet|ssh)/gi,
            // Path traversal patterns
            /(\.\.\/|\.\.\\|%2e%2e%2f|%2e%2e%5c){2,}/gi
        ];
        
        // Security limits
        this.securityLimits = {
            maxFileSize: 10 * 1024 * 1024, // 10MB
            maxJsonDepth: 10,
            maxArrayLength: 1000,
            maxRequestsPerMinute: 60,
            maxLoginAttempts: 5,
            lockoutDuration: 15 * 60 * 1000 // 15 minutes
        };
        
        // Track failed attempts for rate limiting
        this.failedAttempts = new Map();
        this.rateLimitStore = new Map();
    }

    /**
     * Initialize dependencies after all components are loaded
     */
    initializeDependencies() {
        if (!this.errorHandler && window.errorHandler) {
            this.errorHandler = window.errorHandler;
        }
    }

    /**
     * Safely handle errors through ErrorHandler if available
     */
    safeHandleError(error, context, metadata = {}) {
        // Try to initialize dependencies if not done yet
        this.initializeDependencies();
        
        if (this.errorHandler) {
            return this.errorHandler.handleError(error, context, metadata);
        }
        // Fallback to console logging if ErrorHandler not available
        console.error(`[InputValidator] ${context}:`, error, metadata);
        return null;
    }

    /**
     * Validate email address with enhanced security
     */
    validateEmail(email) {
        try {
            if (!email || typeof email !== 'string') {
                return { valid: false, error: 'Email is required' };
            }

            email = email.trim();
            
            if (email.length === 0) {
                return { valid: false, error: 'Email cannot be empty' };
            }

            if (email.length > this.maxLengths.email) {
                return { valid: false, error: 'Email is too long' };
            }

            if (!this.patterns.email.test(email)) {
                return { valid: false, error: 'Invalid email format' };
            }

            if (this.containsDangerousContent(email)) {
                this.safeHandleError(
                    new Error('Dangerous content in email input'),
                    'input-validation',
                    { input: email.substring(0, 50) + '...' }
                );
                return { valid: false, error: 'Email contains invalid characters' };
            }

            // Additional checks for email security
            if (this.hasRepeatedPatterns(email, 10)) {
                return { valid: false, error: 'Email format appears suspicious' };
            }

            if (this.containsHomographs(email)) {
                return { valid: false, error: 'Email contains suspicious characters' };
            }

            return { valid: true, sanitized: email.toLowerCase() };
        } catch (error) {
            this.safeHandleError(error, 'email-validation');
            return { valid: false, error: 'Email validation failed' };
        }
    }

    /**
     * Validate license key
     */
    validateLicenseKey(licenseKey) {
        if (!licenseKey || typeof licenseKey !== 'string') {
            return { valid: false, error: 'License key is required' };
        }

        licenseKey = licenseKey.trim().toUpperCase();
        
        if (licenseKey.length !== this.maxLengths.licenseKey) {
            return { valid: false, error: 'License key must be 19 characters long' };
        }

        if (!this.patterns.licenseKey.test(licenseKey)) {
            return { valid: false, error: 'Invalid license key format' };
        }

        return { valid: true, sanitized: licenseKey };
    }

    /**
     * Validate verification code
     */
    validateVerificationCode(code) {
        if (!code || typeof code !== 'string') {
            return { valid: false, error: 'Verification code is required' };
        }

        code = code.trim();
        
        if (code.length !== this.maxLengths.verificationCode) {
            return { valid: false, error: 'Verification code must be 6 digits' };
        }

        if (!this.patterns.verificationCode.test(code)) {
            return { valid: false, error: 'Verification code must contain only numbers' };
        }

        return { valid: true, sanitized: code };
    }

    /**
     * Validate password
     */
    validatePassword(password) {
        if (!password || typeof password !== 'string') {
            return { valid: false, error: 'Password is required' };
        }

        if (password.length < 8) {
            return { valid: false, error: 'Password must be at least 8 characters long' };
        }

        if (password.length > this.maxLengths.password) {
            return { valid: false, error: 'Password is too long' };
        }

        // Check for at least one uppercase, lowercase, number, and special character
        const hasUppercase = /[A-Z]/.test(password);
        const hasLowercase = /[a-z]/.test(password);
        const hasNumber = /\d/.test(password);
        const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

        if (!hasUppercase || !hasLowercase || !hasNumber || !hasSpecial) {
            return { 
                valid: false, 
                error: 'Password must contain uppercase, lowercase, number, and special character' 
            };
        }

        return { valid: true };
    }

    /**
     * Validate device name
     */
    validateDeviceName(deviceName) {
        if (!deviceName || typeof deviceName !== 'string') {
            return { valid: false, error: 'Device name is required' };
        }

        deviceName = deviceName.trim();
        
        if (deviceName.length === 0) {
            return { valid: false, error: 'Device name cannot be empty' };
        }

        if (deviceName.length > this.maxLengths.deviceName) {
            return { valid: false, error: 'Device name is too long' };
        }

        if (this.containsDangerousContent(deviceName)) {
            return { valid: false, error: 'Device name contains invalid characters' };
        }

        return { valid: true, sanitized: this.sanitizeText(deviceName) };
    }

    /**
     * Validate file name
     */
    validateFileName(fileName) {
        if (!fileName || typeof fileName !== 'string') {
            return { valid: false, error: 'File name is required' };
        }

        fileName = fileName.trim();
        
        if (fileName.length === 0) {
            return { valid: false, error: 'File name cannot be empty' };
        }

        if (fileName.length > this.maxLengths.fileName) {
            return { valid: false, error: 'File name is too long' };
        }

        if (!this.patterns.fileName.test(fileName)) {
            return { valid: false, error: 'Invalid file name format' };
        }

        // Check for dangerous file extensions
        const dangerousExtensions = ['.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js', '.jar'];
        const extension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
        
        if (dangerousExtensions.includes(extension)) {
            return { valid: false, error: 'File type not allowed' };
        }

        return { valid: true, sanitized: fileName };
    }

    /**
     * Validate URL
     */
    validateUrl(url) {
        if (!url || typeof url !== 'string') {
            return { valid: false, error: 'URL is required' };
        }

        url = url.trim();
        
        try {
            const urlObj = new URL(url);
            
            // Only allow http and https protocols
            if (!['http:', 'https:'].includes(urlObj.protocol)) {
                return { valid: false, error: 'Only HTTP and HTTPS URLs are allowed' };
            }

            if (this.containsDangerousContent(url)) {
                return { valid: false, error: 'URL contains invalid characters' };
            }

            return { valid: true, sanitized: url };
        } catch (error) {
            return { valid: false, error: 'Invalid URL format' };
        }
    }

    /**
     * Validate JSON input
     */
    validateJson(jsonString) {
        if (!jsonString || typeof jsonString !== 'string') {
            return { valid: false, error: 'JSON is required' };
        }

        try {
            const parsed = JSON.parse(jsonString);
            
            // Check for dangerous content in JSON
            const jsonStr = JSON.stringify(parsed);
            if (this.containsDangerousContent(jsonStr)) {
                return { valid: false, error: 'JSON contains invalid content' };
            }

            return { valid: true, parsed };
        } catch (error) {
            return { valid: false, error: 'Invalid JSON format' };
        }
    }

    /**
     * Validate numeric input
     */
    validateNumber(value, min = null, max = null) {
        if (value === null || value === undefined || value === '') {
            return { valid: false, error: 'Number is required' };
        }

        const num = Number(value);
        
        if (isNaN(num)) {
            return { valid: false, error: 'Must be a valid number' };
        }

        if (min !== null && num < min) {
            return { valid: false, error: `Number must be at least ${min}` };
        }

        if (max !== null && num > max) {
            return { valid: false, error: `Number must be at most ${max}` };
        }

        return { valid: true, sanitized: num };
    }

    /**
     * Validate boolean input
     */
    validateBoolean(value) {
        if (typeof value === 'boolean') {
            return { valid: true, sanitized: value };
        }

        if (typeof value === 'string') {
            const lower = value.toLowerCase().trim();
            if (['true', '1', 'yes', 'on'].includes(lower)) {
                return { valid: true, sanitized: true };
            }
            if (['false', '0', 'no', 'off'].includes(lower)) {
                return { valid: true, sanitized: false };
            }
        }

        return { valid: false, error: 'Must be a valid boolean value' };
    }

    /**
     * Sanitize text input
     */
    sanitizeText(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        return text
            .trim()
            .replace(/[<>]/g, '') // Remove potential HTML tags
            .replace(/['"]/g, '') // Remove quotes
            .replace(/\0/g, '') // Remove null bytes
            .substring(0, 1000); // Limit length
    }

    /**
     * Sanitize HTML input
     */
    sanitizeHtml(html) {
        if (!html || typeof html !== 'string') {
            return '';
        }

        // Remove dangerous HTML elements and attributes
        return html
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
            .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
            .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
            .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/vbscript:/gi, '')
            .replace(/data:text\/html/gi, '');
    }

    /**
     * Check if content contains dangerous patterns
     */
    containsDangerousContent(content) {
        if (!content || typeof content !== 'string') {
            return false;
        }

        return this.dangerousPatterns.some(pattern => pattern.test(content));
    }

    /**
     * Check if HTML content contains actual XSS threats (not just any HTML)
     * This is more lenient for legitimate HTML content in the DOM
     */
    containsXSSThreats(htmlContent) {
        if (!htmlContent || typeof htmlContent !== 'string') {
            return false;
        }

        // More specific XSS threat patterns that avoid false positives
        const xssPatterns = [
            // Script tags with actual content
            /<script[^>]*>[\s\S]*?<\/script>/gi,
            // Event handlers with javascript
            /\bon\w+\s*=\s*["'][^"']*javascript[^"']*["']/gi,
            /\bon\w+\s*=\s*["'][^"']*alert\s*\([^"']*["']/gi,
            // Data URLs with scripts
            /data:text\/html[^>]*script/gi,
            // CSS expression attacks
            /style\s*=\s*["'][^"']*expression\s*\([^"']*["']/gi,
            // Direct script injection attempts
            /javascript\s*:\s*eval\s*\(/gi,
            /vbscript\s*:\s*eval\s*\(/gi,
            // Dangerous iframe sources
            /src\s*=\s*["']javascript:[^"']*["']/gi,
            // Meta refresh attacks
            /<meta[^>]*http-equiv\s*=\s*["']refresh["'][^>]*url\s*=\s*javascript/gi
        ];

        return xssPatterns.some(pattern => pattern.test(htmlContent));
    }

    /**
     * Check if content is a legitimate HTML comment
     */
    isLegitimateHTMLComment(content) {
        if (!content || typeof content !== 'string') {
            return false;
        }

        // Check if it's just an HTML comment with normal content
        const commentPattern = /^\s*<!--[\s\S]*?-->\s*$/;
        return commentPattern.test(content);
    }

    /**
     * Check if content is legitimate HTML structure
     */
    isLegitimateHTML(content) {
        if (!content || typeof content !== 'string') {
            return false;
        }

        // Check for basic HTML structure without dangerous patterns
        const hasHTMLTags = /<[^>]+>/g.test(content);
        const hasOnlyWhitelistTags = /^[\s\S]*<\s*(div|span|p|h[1-6]|ul|ol|li|a|img|table|tr|td|th|thead|tbody|tfoot|form|input|textarea|select|option|button|br|hr|strong|em|b|i|u|small|label|fieldset|legend|nav|section|article|aside|header|footer|main|figure|figcaption|blockquote|cite|code|pre|kbd|samp|var|sub|sup|mark|del|ins|time|abbr|dfn|address|details|summary|dialog|menu|menuitem|meter|progress|output|datalist|keygen|map|area|canvas|audio|video|source|track|embed|object|param|picture|noscript|style|link|meta|base|title|head|body|html|!--[\s\S]*?-->)\s*[^>]*>[\s\S]*$/gi.test(content);

        return hasHTMLTags && !this.containsXSSThreats(content);
    }

    /**
     * Validate form data object
     */
    validateFormData(data, schema) {
        const errors = {};
        const sanitized = {};

        for (const [field, rules] of Object.entries(schema)) {
            const value = data[field];
            
            // Check if field is required
            if (rules.required && (value === undefined || value === null || value === '')) {
                errors[field] = `${field} is required`;
                continue;
            }

            // Skip validation if field is optional and empty
            if (!rules.required && (value === undefined || value === null || value === '')) {
                continue;
            }

            // Apply field-specific validation
            let result;
            switch (rules.type) {
                case 'email':
                    result = this.validateEmail(value);
                    break;
                case 'password':
                    result = this.validatePassword(value);
                    break;
                case 'licenseKey':
                    result = this.validateLicenseKey(value);
                    break;
                case 'verificationCode':
                    result = this.validateVerificationCode(value);
                    break;
                case 'fileName':
                    result = this.validateFileName(value);
                    break;
                case 'url':
                    result = this.validateUrl(value);
                    break;
                case 'number':
                    result = this.validateNumber(value, rules.min, rules.max);
                    break;
                case 'boolean':
                    result = this.validateBoolean(value);
                    break;
                case 'text':
                default:
                    result = { valid: true, sanitized: this.sanitizeText(value) };
                    break;
            }

            if (!result.valid) {
                errors[field] = result.error;
            } else if (result.sanitized !== undefined) {
                sanitized[field] = result.sanitized;
            } else {
                sanitized[field] = value;
            }
        }

        return {
            valid: Object.keys(errors).length === 0,
            errors,
            sanitized
        };
    }

    /**
     * Enhanced rate limiting validation with security logging
     */
    validateRateLimit(key, maxRequests = 10, windowMs = 60000) {
        try {
            const now = Date.now();
            const windowStart = now - windowMs;
            
            if (!this.rateLimitStore.has(key)) {
                this.rateLimitStore.set(key, []);
            }

            const requests = this.rateLimitStore.get(key);
            
            // Remove old requests outside the window
            const recentRequests = requests.filter(time => time > windowStart);
            
            if (recentRequests.length >= maxRequests) {
                // Log potential abuse
                this.safeHandleError(
                    new Error('Rate limit exceeded'),
                    'rate-limiting',
                    { 
                        key, 
                        requestCount: recentRequests.length, 
                        maxRequests, 
                        windowMs 
                    }
                );
                
                return {
                    valid: false,
                    error: 'Rate limit exceeded. Please try again later.',
                    retryAfter: Math.ceil((recentRequests[0] + windowMs - now) / 1000)
                };
            }

            // Add current request
            recentRequests.push(now);
            this.rateLimitStore.set(key, recentRequests);

            return { valid: true };
        } catch (error) {
            this.safeHandleError(error, 'rate-limit-validation');
            return { valid: false, error: 'Rate limit validation failed' };
        }
    }
    
    /**
     * Check for repeated patterns that might indicate malicious input
     */
    hasRepeatedPatterns(input, threshold = 5) {
        if (!input || typeof input !== 'string') return false;
        
        const patterns = {};
        for (let i = 0; i < input.length - 2; i++) {
            const pattern = input.substring(i, i + 3);
            patterns[pattern] = (patterns[pattern] || 0) + 1;
            if (patterns[pattern] > threshold) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check for homograph attacks (similar-looking characters)
     */
    containsHomographs(input) {
        if (!input || typeof input !== 'string') return false;
        
        // Common homograph characters that could be used in attacks
        const homographs = /[а-я]|[ѐ-ӿ]|[αβγδεζηθικλμνξοπρστυφχψω]/i;
        return homographs.test(input);
    }
    
    /**
     * Validate file upload with enhanced security
     */
    validateFileUpload(file, options = {}) {
        const {
            maxSize = this.securityLimits.maxFileSize,
            allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
            allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf'],
            scanForMalware = true
        } = options;
        
        try {
            if (!file) {
                return { valid: false, error: 'File is required' };
            }
            
            // Validate file size
            if (file.size > maxSize) {
                return { 
                    valid: false, 
                    error: `File size exceeds limit (${Math.round(maxSize / 1024 / 1024)}MB)` 
                };
            }
            
            // Validate file type
            if (!allowedTypes.includes(file.type)) {
                this.safeHandleError(
                    new Error('Unauthorized file type upload attempted'),
                    'file-validation',
                    { fileType: file.type, fileName: file.name }
                );
                return { valid: false, error: 'File type not allowed' };
            }
            
            // Validate file extension
            const extension = this.getFileExtension(file.name);
            if (!allowedExtensions.includes(extension)) {
                return { valid: false, error: 'File extension not allowed' };
            }
            
            // Check for suspicious file names
            if (this.containsDangerousContent(file.name)) {
                this.safeHandleError(
                    new Error('Suspicious file name detected'),
                    'file-validation',
                    { fileName: file.name }
                );
                return { valid: false, error: 'File name contains invalid characters' };
            }
            
            // Basic malware signature check (simple patterns)
            if (scanForMalware && this.containsMalwareSignatures(file.name)) {
                this.safeHandleError(
                    new Error('Potential malware detected in file'),
                    'malware-detection',
                    { fileName: file.name }
                );
                return { valid: false, error: 'File appears to contain malicious content' };
            }
            
            return { 
                valid: true, 
                sanitized: {
                    name: this.sanitizeFileName(file.name),
                    size: file.size,
                    type: file.type
                }
            };
        } catch (error) {
            this.safeHandleError(error, 'file-upload-validation');
            return { valid: false, error: 'File validation failed' };
        }
    }
    
    /**
     * Get file extension safely
     */
    getFileExtension(fileName) {
        if (!fileName || typeof fileName !== 'string') return '';
        const lastDot = fileName.lastIndexOf('.');
        return lastDot === -1 ? '' : fileName.substring(lastDot).toLowerCase();
    }
    
    /**
     * Sanitize file name
     */
    sanitizeFileName(fileName) {
        if (!fileName || typeof fileName !== 'string') return '';
        
        return fileName
            .replace(/[<>:"/\\|?*\x00-\x1f]/g, '') // Remove invalid characters
            .replace(/^\.+/, '') // Remove leading dots
            .substring(0, this.maxLengths.fileName); // Limit length
    }
    
    /**
     * Check for basic malware signatures in file names
     */
    containsMalwareSignatures(fileName) {
        if (!fileName || typeof fileName !== 'string') return false;
        
        const malwarePatterns = [
            /\.(exe|bat|cmd|com|scr|pif|vbs|vbe|js|jar|ws|wsf)$/i,
            /autorun\.inf/i,
            /desktop\.ini/i,
            /thumbs\.db/i
        ];
        
        return malwarePatterns.some(pattern => pattern.test(fileName));
    }
    
    /**
     * Validate API key or token format
     */
    validateApiKey(apiKey, options = {}) {
        const { 
            minLength = 32, 
            maxLength = 128, 
            allowedChars = /^[A-Za-z0-9_-]+$/ 
        } = options;
        
        try {
            if (!apiKey || typeof apiKey !== 'string') {
                return { valid: false, error: 'API key is required' };
            }
            
            if (apiKey.length < minLength || apiKey.length > maxLength) {
                return { 
                    valid: false, 
                    error: `API key must be between ${minLength} and ${maxLength} characters` 
                };
            }
            
            if (!allowedChars.test(apiKey)) {
                return { valid: false, error: 'API key contains invalid characters' };
            }
            
            return { valid: true, sanitized: apiKey };
        } catch (error) {
            this.safeHandleError(error, 'api-key-validation');
            return { valid: false, error: 'API key validation failed' };
        }
    }
    
    /**
     * Track and validate login attempts for brute force protection
     */
    validateLoginAttempt(identifier) {
        try {
            const now = Date.now();
            const key = `login_${identifier}`;
            
            if (!this.failedAttempts.has(key)) {
                this.failedAttempts.set(key, { count: 0, lastAttempt: now });
                return { valid: true };
            }
            
            const attempts = this.failedAttempts.get(key);
            
            // Reset count if lockout period has passed
            if (now - attempts.lastAttempt > this.securityLimits.lockoutDuration) {
                attempts.count = 0;
                attempts.lastAttempt = now;
                return { valid: true };
            }
            
            // Check if account is locked
            if (attempts.count >= this.securityLimits.maxLoginAttempts) {
                const remainingTime = Math.ceil(
                    (this.securityLimits.lockoutDuration - (now - attempts.lastAttempt)) / 1000 / 60
                );
                
                this.safeHandleError(
                    new Error('Account lockout due to failed login attempts'),
                    'security-lockout',
                    { identifier, attempts: attempts.count }
                );
                
                return {
                    valid: false,
                    error: `Account locked. Try again in ${remainingTime} minutes.`,
                    locked: true,
                    remainingTime
                };
            }
            
            return { valid: true };
        } catch (error) {
            this.safeHandleError(error, 'login-attempt-validation');
            return { valid: false, error: 'Login validation failed' };
        }
    }
    
    /**
     * Record failed login attempt
     */
    recordFailedLogin(identifier) {
        try {
            const now = Date.now();
            const key = `login_${identifier}`;
            
            if (!this.failedAttempts.has(key)) {
                this.failedAttempts.set(key, { count: 1, lastAttempt: now });
            } else {
                const attempts = this.failedAttempts.get(key);
                attempts.count++;
                attempts.lastAttempt = now;
            }
            
            const attempts = this.failedAttempts.get(key);
            
            // Log security event
            this.safeHandleError(
                new Error('Failed login attempt'),
                'failed-login',
                { identifier, attempts: attempts.count }
            );
        } catch (error) {
            this.safeHandleError(error, 'failed-login-recording');
        }
    }
    
    /**
     * Clear failed login attempts (after successful login)
     */
    clearFailedAttempts(identifier) {
        const key = `login_${identifier}`;
        this.failedAttempts.delete(key);
    }
    
    /**
     * Get security statistics
     */
    getSecurityStats() {
        return {
            rateLimitEntries: this.rateLimitStore.size,
            failedLoginAttempts: this.failedAttempts.size,
            dangerousPatterns: this.dangerousPatterns.length,
            securityLimits: this.securityLimits
        };
    }
}

// Create singleton instance
const inputValidator = new InputValidator();

// Make available globally for browser environment
if (typeof window !== 'undefined') {
    window.InputValidator = InputValidator;
    window.inputValidator = inputValidator;
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        InputValidator,
        inputValidator
    };
}

console.log('[InputValidator] Enhanced security validation system initialized');