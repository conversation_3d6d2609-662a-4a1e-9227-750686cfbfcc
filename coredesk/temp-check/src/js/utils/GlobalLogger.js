/**
 * GlobalLogger.js
 * Simple global logger fallback for all components
 */

// Create a simple global logger that all components can use
window.Logger = window.Logger || class SimpleLogger {
    static info(component, message, data = null) {
        console.log(`[${component}] ${message}`, data || '');
    }
    
    static error(component, message, error = null) {
        console.error(`[${component}] ${message}`, error || '');
    }
    
    static warn(component, message, data = null) {
        console.warn(`[${component}] ${message}`, data || '');
    }
    
    static debug(component, message, data = null) {
        console.debug(`[${component}] ${message}`, data || '');
    }
    
    // Instance methods for compatibility
    info(component, message, data = null) {
        return SimpleLogger.info(component, message, data);
    }
    
    error(component, message, error = null) {
        return SimpleLogger.error(component, message, error);
    }
    
    warn(component, message, data = null) {
        return SimpleLogger.warn(component, message, data);
    }
    
    debug(component, message, data = null) {
        return SimpleLogger.debug(component, message, data);
    }
};

// Create a global logger instance
window.logger = new window.Logger();

console.log('[GlobalLogger] Global logger initialized successfully');
