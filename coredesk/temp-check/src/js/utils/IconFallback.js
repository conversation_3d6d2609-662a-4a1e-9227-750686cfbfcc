/**
 * Icon Fallback System
 * Detecta si los iconos SVG se cargan correctamente y activa fallbacks si es necesario
 */

class IconFallback {
    constructor() {
        this.initialized = false;
        this.fallbackActive = false;
    }

    /**
     * Inicializa el sistema de fallback de iconos
     */
    init() {
        if (this.initialized) return;
        
        console.log('[IconFallback] Inicializando sistema de fallback de iconos...');
        
        // Esperar a que el DOM esté listo
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.checkIcons());
        } else {
            this.checkIcons();
        }
        
        this.initialized = true;
    }

    /**
     * Verifica si los iconos SVG se están cargando correctamente
     */
    async checkIcons() {
        console.log('[IconFallback] Verificando carga de iconos SVG...');

        // Verificar soporte para mask-image
        const supportsMaskImage = this.checkMaskImageSupport();
        console.log('[IconFallback] Soporte mask-image:', supportsMaskImage);

        // Primero intentar cargar los SVG con las rutas corregidas
        const svgLoadSuccess = await this.testSVGLoading();

        if (!svgLoadSuccess || !supportsMaskImage) {
            console.log('[IconFallback] SVG loading failed or mask-image not supported, activating fallback');
            this.activateFallback();

            // Forzar específicamente los iconos de paneles
            setTimeout(() => {
                this.forcePanelIconsVisible();
            }, 100);

            // Activar fallback para todos los iconos inmediatamente
            setTimeout(() => {
                this.forceAllIconsVisible();
            }, 200);
        } else {
            console.log('[IconFallback] SVG icons loaded successfully, no fallback needed');
            this.ensureIconsVisible();
        }

        return;

    }

    /**
     * Prueba la carga de archivos SVG con las rutas corregidas
     */
    async testSVGLoading() {
        console.log('[IconFallback] Probando carga de archivos SVG...');

        // Usar AssetPath para resolver rutas dinámicamente
        const iconNames = [
            'folder-mask',
            'cloud-mask',
            'search-mask',
            'grid-mask',
            'puzzle-mask',
            'settings-mask',
            'leftPanel-mask',
            'rightPanel-mask',
            'bottomPanel-mask'
        ];

        const svgPaths = iconNames.map(name => window.AssetPath.icon(name));

        let loadedCount = 0;
        const totalCount = svgPaths.length;

        for (const path of svgPaths) {
            try {
                const response = await fetch(path);
                if (response.ok) {
                    loadedCount++;
                    console.log(`[IconFallback] SVG cargado exitosamente: ${path}`);
                } else {
                    console.warn(`[IconFallback] Error HTTP ${response.status} cargando SVG: ${path}`);
                }
            } catch (error) {
                console.warn('[IconFallback] Error cargando SVG:', path, error.message);
            }
        }

        console.log(`[IconFallback] SVGs cargados: ${loadedCount}/${totalCount}`);

        // Si al menos el 70% de los SVG se cargan, consideramos exitoso
        const success = loadedCount >= totalCount * 0.7;
        console.log(`[IconFallback] Prueba de carga SVG: ${success ? 'EXITOSA' : 'FALLIDA'}`);

        return success;
    }

    /**
     * Verifica si el navegador soporta mask-image
     */
    checkMaskImageSupport() {
        const testElement = document.createElement('div');
        testElement.style.maskImage = 'url(data:image/svg+xml,<svg></svg>)';
        return testElement.style.maskImage !== '';
    }

    /**
     * Activa el sistema de fallback
     */
    activateFallback() {
        if (this.fallbackActive) return;

        console.log('[IconFallback] Activando sistema de fallback...');

        try {
            // Agregar clase CSS para activar fallback
            document.documentElement.classList.add('icon-fallback-active');

            // Agregar estilos de fallback dinámicamente
            this.addFallbackStyles();

            // Verificar y aplicar fallback a iconos específicos
            const panelIcons = document.querySelectorAll('.panel-icon');
            const activityIcons = document.querySelectorAll('.activity-icon');

            console.log('[IconFallback] Iconos de panel encontrados:', panelIcons.length);
            console.log('[IconFallback] Iconos de actividad encontrados:', activityIcons.length);

            // Aplicar fallback inmediato a iconos de panel
            panelIcons.forEach((icon, index) => {
                icon.style.setProperty('background-color', 'transparent', 'important');
                icon.style.setProperty('mask-image', 'none', 'important');
            });

            // Aplicar fallback inmediato a iconos de actividad
            activityIcons.forEach((icon, index) => {
                icon.style.setProperty('background-color', 'transparent', 'important');
                icon.style.setProperty('mask-image', 'none', 'important');
            });

            this.fallbackActive = true;
            console.log('[IconFallback] Sistema de fallback activado exitosamente');

        } catch (error) {
            console.error('[IconFallback] Error activando fallback:', error);
            this.fallbackActive = true; // Marcar como activado para evitar loops
        }
    }

    /**
     * Asegura que los iconos sean visibles
     */
    ensureIconsVisible() {
        // Verificar que los iconos tengan el color correcto
        const activityIcons = document.querySelectorAll('.activity-icon');
        const panelIcons = document.querySelectorAll('.panel-icon');
        
        activityIcons.forEach(icon => {
            if (getComputedStyle(icon).backgroundColor === 'rgba(0, 0, 0, 0)' || 
                getComputedStyle(icon).backgroundColor === 'transparent') {
                console.warn('[IconFallback] Icono de actividad transparente detectado');
                this.activateFallback();
            }
        });

        panelIcons.forEach(icon => {
            if (getComputedStyle(icon).backgroundColor === 'rgba(0, 0, 0, 0)' || 
                getComputedStyle(icon).backgroundColor === 'transparent') {
                console.warn('[IconFallback] Icono de panel transparente detectado');
                this.activateFallback();
            }
        });
    }

    /**
     * Agrega estilos de fallback dinámicamente
     */
    addFallbackStyles() {
        const style = document.createElement('style');
        style.id = 'icon-fallback-styles';
        style.textContent = `
            /* Fallback activo - mostrar caracteres Unicode */
            .icon-fallback-active .activity-icon {
                background-color: transparent !important;
                mask-image: none !important;
            }
            
            .icon-fallback-active .panel-icon {
                background-color: transparent !important;
                mask-image: none !important;
            }
            
            .icon-fallback-active .icon-explorer::after,
            .icon-fallback-active .icon-cloud::after,
            .icon-fallback-active .icon-search::after,
            .icon-fallback-active .icon-modules::after,
            .icon-fallback-active .icon-extensions::after,
            .icon-fallback-active .icon-settings::after {
                display: block !important;
            }
            
            .icon-fallback-active .icon-layout-sidebar-left::after,
            .icon-fallback-active .icon-layout-sidebar-right::after,
            .icon-fallback-active .icon-layout-panel::after {
                display: block !important;
                color: var(--foreground-muted) !important;
            }
            
            .icon-fallback-active .panel-toggle:hover .icon-layout-sidebar-left::after,
            .icon-fallback-active .panel-toggle:hover .icon-layout-sidebar-right::after,
            .icon-fallback-active .panel-toggle:hover .icon-layout-panel::after,
            .icon-fallback-active .panel-toggle.active .icon-layout-sidebar-left::after,
            .icon-fallback-active .panel-toggle.active .icon-layout-sidebar-right::after,
            .icon-fallback-active .panel-toggle.active .icon-layout-panel::after {
                color: var(--foreground-primary) !important;
            }
        `;

        // Remover estilo existente si existe
        const existingStyle = document.getElementById('icon-fallback-styles');
        if (existingStyle) {
            existingStyle.remove();
        }

        document.head.appendChild(style);
        console.log('[IconFallback] Estilos de fallback agregados');

        // Debug: verificar que la clase se aplicó
        console.log('[IconFallback] Clase icon-fallback-active aplicada:', document.documentElement.classList.contains('icon-fallback-active'));

        // Debug: verificar elementos de panel
        const panelIcons = document.querySelectorAll('.panel-icon');
        console.log('[IconFallback] Iconos de panel encontrados:', panelIcons.length);
        panelIcons.forEach((icon, index) => {
            console.log(`[IconFallback] Panel icon ${index}:`, icon.className, getComputedStyle(icon).backgroundColor);
        });
    }

    /**
     * Fuerza la activación del fallback (para testing)
     */
    forceFallback() {
        console.log('[IconFallback] Forzando activación de fallback...');
        this.activateFallback();
    }

    /**
     * Fuerza la visibilidad de todos los iconos usando fallback
     */
    forceAllIconsVisible() {
        console.log('[IconFallback] Forzando visibilidad de todos los iconos...');

        // Asegurar que la clase esté aplicada
        document.documentElement.classList.add('icon-fallback-active');

        // Forzar display de todos los pseudo-elementos de iconos
        const style = document.createElement('style');
        style.id = 'force-all-icons';
        style.textContent = `
            .icon-explorer::after,
            .icon-cloud::after,
            .icon-search::after,
            .icon-modules::after,
            .icon-extensions::after,
            .icon-settings::after,
            .icon-layout-sidebar-left::after,
            .icon-layout-sidebar-right::after,
            .icon-layout-panel::after {
                display: block !important;
                color: var(--foreground-muted) !important;
                position: absolute !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                font-size: 16px !important;
                font-weight: normal !important;
                z-index: 10 !important;
                line-height: 1 !important;
            }

            .activity-icon {
                background-color: transparent !important;
                mask-image: none !important;
            }

            .panel-icon {
                background-color: transparent !important;
                mask-image: none !important;
            }
        `;

        // Remover estilo existente si existe
        const existingStyle = document.getElementById('force-all-icons');
        if (existingStyle) {
            existingStyle.remove();
        }

        document.head.appendChild(style);
        console.log('[IconFallback] Fallback forzado para todos los iconos aplicado');
    }

    /**
     * Fuerza específicamente la visibilidad de iconos de paneles
     */
    forcePanelIconsVisible() {
        console.log('[IconFallback] Forzando visibilidad de iconos de paneles...');

        // Asegurar que la clase esté aplicada
        document.documentElement.classList.add('icon-fallback-active');

        // Forzar display de pseudo-elementos con máxima especificidad
        const style = document.createElement('style');
        style.id = 'force-panel-icons';
        style.textContent = `
            .icon-layout-sidebar-left::after,
            .icon-layout-sidebar-right::after,
            .icon-layout-panel::after {
                display: block !important;
                color: var(--foreground-muted) !important;
                position: absolute !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                font-size: 14px !important;
                font-weight: bold !important;
                z-index: 10 !important;
            }
        `;

        // Remover estilo existente si existe
        const existingForceStyle = document.getElementById('force-panel-icons');
        if (existingForceStyle) {
            existingForceStyle.remove();
        }

        document.head.appendChild(style);
        console.log('[IconFallback] Iconos de paneles forzados a ser visibles');
    }

    /**
     * Desactiva el fallback y restaura iconos SVG
     */
    disableFallback() {
        if (!this.fallbackActive) return;
        
        console.log('[IconFallback] Desactivando fallback...');
        document.documentElement.classList.remove('icon-fallback-active');
        
        const fallbackStyles = document.getElementById('icon-fallback-styles');
        if (fallbackStyles) {
            fallbackStyles.remove();
        }
        
        this.fallbackActive = false;
        console.log('[IconFallback] Fallback desactivado');
    }
}

// Crear instancia global
window.IconFallback = new IconFallback();

// Auto-inicializar
window.IconFallback.init();
