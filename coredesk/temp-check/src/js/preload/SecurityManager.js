/**
 * SecurityManager.js
 * Handles security policies and protections for preload script
 */

const { ipc<PERSON>ender<PERSON> } = require('electron');

class SecurityManager {
    constructor() {
        this.isDevelopment = process.argv.includes('--dev');
        this.ipcCallCount = 0;
        this.maxIpcCalls = 10000; // Prevent IPC flooding
    }

    initializeSecurity() {
        this.removeNodeAccess();
        
        if (this.isDevelopment) {
            this.setupDevelopmentLogging();
        }
        
        this.setupIpcRateLimit();
    }

    removeNodeAccess() {
        // Prevent access to Node.js APIs from renderer
        if (typeof window !== 'undefined') {
            window.nodeRequire = undefined;
            delete window.require;
            delete window.exports;
            delete window.module;
            delete window.process;
            delete window.global;
            delete window.Buffer;
            delete window.setImmediate;
            delete window.clearImmediate;
        }
    }

    setupDevelopmentLogging() {
        // Only in development mode, add logging
        const originalInvoke = ipcRenderer.invoke;
        const originalOn = ipcRenderer.on;

        ipcRenderer.invoke = function(channel, ...args) {
            // Log to console in development only
            if (console && console.debug) {
                console.debug('[IPC] Invoke:', channel, args.length > 0 ? '(with args)' : '(no args)');
            }
            return originalInvoke.call(this, channel, ...args);
        };

        ipcRenderer.on = function(channel, callback) {
            if (console && console.debug) {
                console.debug('[IPC] Listener added:', channel);
            }
            return originalOn.call(this, channel, callback);
        };
    }

    setupIpcRateLimit() {
        const originalInvoke = ipcRenderer.invoke;
        
        ipcRenderer.invoke = (channel, ...args) => {
            this.ipcCallCount++;
            
            if (this.ipcCallCount > this.maxIpcCalls) {
                throw new Error('IPC rate limit exceeded');
            }
            
            return originalInvoke.call(ipcRenderer, channel, ...args);
        };

        // Reset counter every minute
        setInterval(() => {
            this.ipcCallCount = 0;
        }, 60000);
    }

    validateContext() {
        // Ensure we're running in the correct context
        if (typeof window === 'undefined') {
            throw new Error('Preload script must run in renderer process');
        }

        if (typeof require === 'undefined') {
            throw new Error('Preload script requires Node.js context');
        }

        return true;
    }

    getSecurityInfo() {
        return {
            isDevelopment: this.isDevelopment,
            ipcCallCount: this.ipcCallCount,
            maxIpcCalls: this.maxIpcCalls,
            contextIsolated: process.contextIsolated,
            sandboxed: process.sandboxed
        };
    }
}

module.exports = SecurityManager;