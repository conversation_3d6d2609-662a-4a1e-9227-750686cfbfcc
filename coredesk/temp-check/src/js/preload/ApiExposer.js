/**
 * ApiExposer.js
 * Handles exposing secure APIs to renderer process
 */

const { ipcRenderer } = require('electron');

class ApiExposer {
    constructor() {
        this.validChannels = [
            'app:initialized',
            'app:initialization-error',
            'license:activated',
            'sync:completed',
            'sync:error'
        ];
    }

    getElectronAPI() {
        return {
            // Window controls
            window: this.getWindowAPI(),
            
            // App information
            app: this.getAppAPI(),
            
            // System information
            system: this.getSystemAPI(),
            
            // Database operations
            database: this.getDatabaseAPI(),
            
            // License operations
            license: this.getLicenseAPI(),
            
            // File system operations
            filesystem: this.getFileSystemAPI(),
            
            // Event management
            on: this.createEventListener(),
            removeListener: this.createEventRemover(),
            
            // Execute database query (alias for compatibility)
            executeQuery: (sql, params) => ipcRenderer.invoke('db:query', sql, params)
        };
    }

    getWindowAPI() {
        return {
            minimize: () => ipcRenderer.invoke('window:minimize'),
            maximize: () => ipcRenderer.invoke('window:maximize'),
            close: () => ipcRenderer.invoke('window:close'),
            isMaximized: () => ipcRenderer.invoke('window:isMaximized'),
            isMinimized: () => ipcRenderer.invoke('window:isMinimized'),
            isFocused: () => ipcRenderer.invoke('window:isFocused')
        };
    }

    getAppAPI() {
        return {
            getVersion: () => ipcRenderer.invoke('app:getVersion'),
            getPlatform: () => ipcRenderer.invoke('app:getPlatform'),
            getUserDataPath: () => ipcRenderer.invoke('app:getUserDataPath'),
            getAppPath: () => ipcRenderer.invoke('app:getAppPath')
        };
    }

    getSystemAPI() {
        return {
            getInfo: () => ipcRenderer.invoke('system:getInfo'),
            getBasicInfo: () => ipcRenderer.invoke('system:getBasicInfo'),
            getMemoryInfo: () => ipcRenderer.invoke('system:getMemoryInfo')
        };
    }

    getDatabaseAPI() {
        return {
            query: (sql, params) => ipcRenderer.invoke('db:query', sql, params),
            execute: (sql, params) => ipcRenderer.invoke('db:execute', sql, params),
            getSchema: () => ipcRenderer.invoke('db:getSchema')
        };
    }

    getLicenseAPI() {
        return {
            validate: (licenseKey, deviceFingerprint) => 
                ipcRenderer.invoke('license:validate', licenseKey, deviceFingerprint),
            activate: (licenseData) => 
                ipcRenderer.invoke('license:activate', licenseData),
            deactivate: () => 
                ipcRenderer.invoke('license:deactivate'),
            getStatus: () => 
                ipcRenderer.invoke('license:getStatus'),
            generateTrialKey: () => 
                ipcRenderer.invoke('license:generateTrialKey')
        };
    }

    getFileSystemAPI() {
        return {
            writeFile: (filePath, data, options) => 
                ipcRenderer.invoke('fs:writeFile', filePath, data, options),
            readFile: (filePath, options) => 
                ipcRenderer.invoke('fs:readFile', filePath, options),
            deleteFile: (filePath) => 
                ipcRenderer.invoke('fs:deleteFile', filePath),
            exists: (filePath) => 
                ipcRenderer.invoke('fs:exists', filePath),
            createDirectory: (dirPath) => 
                ipcRenderer.invoke('fs:createDirectory', dirPath),
            listDirectory: (dirPath) => 
                ipcRenderer.invoke('fs:listDirectory', dirPath)
        };
    }

    createEventListener() {
        return (channel, callback) => {
            if (this.isValidChannel(channel)) {
                ipcRenderer.on(channel, callback);
            } else {
                throw new Error(`Invalid channel for event listener: ${channel}`);
            }
        };
    }

    createEventRemover() {
        return (channel, callback) => {
            if (this.isValidChannel(channel)) {
                ipcRenderer.removeListener(channel, callback);
            } else {
                throw new Error(`Invalid channel for event removal: ${channel}`);
            }
        };
    }

    isValidChannel(channel) {
        return this.validChannels.includes(channel);
    }

    getValidChannels() {
        return [...this.validChannels];
    }
}

module.exports = ApiExposer;