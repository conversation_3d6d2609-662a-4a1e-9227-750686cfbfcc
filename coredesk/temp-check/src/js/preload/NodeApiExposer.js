/**
 * NodeApiExposer.js
 * Handles exposing limited Node.js APIs to renderer process
 */

class NodeApiExposer {
    getNodeAPI() {
        return {
            crypto: this.getCryptoAPI(),
            path: this.getPathAPI(),
            buffer: this.getBufferAPI()
        };
    }

    getCryptoAPI() {
        return {
            createHash: (algorithm) => {
                this.validateHashAlgorithm(algorithm);
                const crypto = require('crypto');
                return crypto.createHash(algorithm);
            },
            
            randomBytes: (size) => {
                this.validateRandomBytesSize(size);
                const crypto = require('crypto');
                return crypto.randomBytes(size);
            },
            
            randomUUID: () => {
                const crypto = require('crypto');
                return crypto.randomUUID();
            },
            
            createHmac: (algorithm, key) => {
                this.validateHashAlgorithm(algorithm);
                const crypto = require('crypto');
                return crypto.createHmac(algorithm, key);
            }
        };
    }

    getPathAPI() {
        return {
            join: (...paths) => {
                this.validatePathArguments(paths);
                const path = require('path');
                return path.join(...paths);
            },
            
            dirname: (p) => {
                this.validatePathString(p);
                const path = require('path');
                return path.dirname(p);
            },
            
            basename: (p, ext) => {
                this.validatePathString(p);
                const path = require('path');
                return path.basename(p, ext);
            },
            
            extname: (p) => {
                this.validatePathString(p);
                const path = require('path');
                return path.extname(p);
            },
            
            resolve: (...paths) => {
                this.validatePathArguments(paths);
                const path = require('path');
                return path.resolve(...paths);
            },
            
            relative: (from, to) => {
                this.validatePathString(from);
                this.validatePathString(to);
                const path = require('path');
                return path.relative(from, to);
            }
        };
    }

    getBufferAPI() {
        return {
            from: (data, encoding) => {
                return Buffer.from(data, encoding);
            },
            
            isBuffer: (obj) => {
                return Buffer.isBuffer(obj);
            },
            
            concat: (list, totalLength) => {
                if (!Array.isArray(list)) {
                    throw new Error('First argument must be an array');
                }
                return Buffer.concat(list, totalLength);
            }
        };
    }

    // Validation methods
    validateHashAlgorithm(algorithm) {
        const allowedAlgorithms = ['sha256', 'sha512', 'md5', 'sha1'];
        if (!algorithm || typeof algorithm !== 'string') {
            throw new Error('Algorithm must be a string');
        }
        if (!allowedAlgorithms.includes(algorithm.toLowerCase())) {
            throw new Error(`Unsupported hash algorithm: ${algorithm}`);
        }
    }

    validateRandomBytesSize(size) {
        if (typeof size !== 'number' || size < 1 || size > 1024) {
            throw new Error('Size must be a number between 1 and 1024');
        }
    }

    validatePathString(pathStr) {
        if (!pathStr || typeof pathStr !== 'string') {
            throw new Error('Path must be a non-empty string');
        }
        
        // Basic security check - prevent obvious path traversal
        if (pathStr.includes('..') && pathStr.includes('/')) {
            throw new Error('Path traversal patterns not allowed');
        }
    }

    validatePathArguments(paths) {
        if (!Array.isArray(paths) || paths.length === 0) {
            throw new Error('At least one path argument is required');
        }
        
        paths.forEach((path, index) => {
            if (typeof path !== 'string') {
                throw new Error(`Path argument at index ${index} must be a string`);
            }
        });
    }
}

module.exports = NodeApiExposer;