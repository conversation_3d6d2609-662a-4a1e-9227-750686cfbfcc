/**
 * Search Panel Component
 * Provides comprehensive search functionality across modules and documents
 */

class SearchPanel {
    constructor() {
        this.searchHistory = [];
        this.currentQuery = '';
        this.searchFilters = {
            type: 'all', // all, documents, modules, settings
            module: 'all', // all, lexflow, protocolx, etc.
            dateRange: 'all' // all, today, week, month
        };
        this.searchResults = [];
        this.isSearching = false;
        
        this.init();
    }
    
    init() {
        // Load search history from localStorage
        this.loadSearchHistory();
        
        // Set up event listeners when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }
    }
    
    setupEventListeners() {
        // Main search input
        const searchInput = document.getElementById('global-search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearchInput(e));
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch(e.target.value);
                }
            });
        }
        
        // Search button
        const searchButton = document.getElementById('search-button');
        if (searchButton) {
            searchButton.addEventListener('click', () => {
                const input = document.getElementById('global-search-input');
                if (input) {
                    this.performSearch(input.value);
                }
            });
        }
        
        // Filter buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-option')) {
                this.handleFilterChange(e.target);
            }
            
            if (e.target.classList.contains('clear-search')) {
                this.clearSearch();
            }
            
            if (e.target.classList.contains('search-history-item')) {
                this.selectHistoryItem(e.target.dataset.query);
            }
        });
    }
    
    /**
     * Render search panel content
     */
    renderSearchPanel() {
        return `
            <div class="search-panel">
                <!-- Search Input -->
                <div class="search-input-container">
                    <div class="search-input-wrapper">
                        <input type="text" 
                               id="global-search-input" 
                               class="search-input" 
                               placeholder="Buscar en todos los módulos..."
                               autocomplete="off">
                        <button id="search-button" class="search-btn">
                            <div class="search-icon">🔍</div>
                        </button>
                        <button class="clear-search" style="display: none;">
                            <div class="clear-icon">✕</div>
                        </button>
                    </div>
                </div>
                
                <!-- Search Filters -->
                <div class="search-filters">
                    <div class="filter-group">
                        <label>Tipo:</label>
                        <div class="filter-options">
                            <button class="filter-option active" data-filter="type" data-value="all">Todo</button>
                            <button class="filter-option" data-filter="type" data-value="documents">Documentos</button>
                            <button class="filter-option" data-filter="type" data-value="modules">Módulos</button>
                            <button class="filter-option" data-filter="type" data-value="settings">Configuración</button>
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <label>Módulo:</label>
                        <div class="filter-options">
                            <button class="filter-option active" data-filter="module" data-value="all">Todos</button>
                            <button class="filter-option" data-filter="module" data-value="lexflow">LexFlow</button>
                            <button class="filter-option" data-filter="module" data-value="protocolx">ProtocolX</button>
                            <button class="filter-option" data-filter="module" data-value="auditpro">AuditPro</button>
                            <button class="filter-option" data-filter="module" data-value="finsync">FinSync</button>
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <label>Fecha:</label>
                        <div class="filter-options">
                            <button class="filter-option active" data-filter="dateRange" data-value="all">Todas</button>
                            <button class="filter-option" data-filter="dateRange" data-value="today">Hoy</button>
                            <button class="filter-option" data-filter="dateRange" data-value="week">Esta semana</button>
                            <button class="filter-option" data-filter="dateRange" data-value="month">Este mes</button>
                        </div>
                    </div>
                </div>
                
                <!-- Search Results -->
                <div class="search-results-container">
                    <div id="search-results" class="search-results">
                        ${this.renderInitialState()}
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Render initial state (search history and suggestions)
     */
    renderInitialState() {
        const recentSearches = this.searchHistory.slice(0, 5);
        
        return `
            <div class="search-initial-state">
                ${recentSearches.length > 0 ? `
                    <div class="search-section">
                        <h4 class="section-title">Búsquedas recientes</h4>
                        <div class="search-history">
                            ${recentSearches.map(query => `
                                <div class="search-history-item" data-query="${query}">
                                    <div class="history-icon">🕐</div>
                                    <span class="history-text">${query}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                <div class="search-section">
                    <h4 class="section-title">Sugerencias</h4>
                    <div class="search-suggestions">
                        <div class="suggestion-item" data-query="documentos legales">
                            <div class="suggestion-icon">📄</div>
                            <span>Documentos legales</span>
                        </div>
                        <div class="suggestion-item" data-query="contratos">
                            <div class="suggestion-icon">📋</div>
                            <span>Contratos</span>
                        </div>
                        <div class="suggestion-item" data-query="reportes financieros">
                            <div class="suggestion-icon">📊</div>
                            <span>Reportes financieros</span>
                        </div>
                        <div class="suggestion-item" data-query="auditorías">
                            <div class="suggestion-icon">🔍</div>
                            <span>Auditorías</span>
                        </div>
                    </div>
                </div>
                
                <div class="search-tips">
                    <h4 class="section-title">Consejos de búsqueda</h4>
                    <ul class="tips-list">
                        <li>Usa comillas para buscar frases exactas: "contrato de trabajo"</li>
                        <li>Combina términos con AND, OR: cliente AND contrato</li>
                        <li>Usa * para búsquedas parciales: fact*</li>
                        <li>Busca por fecha: modificado:hoy</li>
                    </ul>
                </div>
            </div>
        `;
    }
    
    /**
     * Handle search input changes
     */
    handleSearchInput(e) {
        const query = e.target.value.trim();
        this.currentQuery = query;
        
        // Show/hide clear button
        const clearBtn = document.querySelector('.clear-search');
        if (clearBtn) {
            clearBtn.style.display = query ? 'block' : 'none';
        }
        
        // Real-time search for queries longer than 2 characters
        if (query.length > 2) {
            this.debouncedSearch(query);
        } else if (query.length === 0) {
            this.showInitialState();
        }
    }
    
    /**
     * Debounced search to avoid too many API calls
     */
    debouncedSearch = this.debounce((query) => {
        this.performSearch(query, false); // false = don't add to history for real-time search
    }, 300);
    
    /**
     * Perform search operation
     */
    async performSearch(query, addToHistory = true) {
        if (!query || query.trim() === '') {
            this.showInitialState();
            return;
        }
        
        query = query.trim();
        this.currentQuery = query;
        
        // Add to search history
        if (addToHistory) {
            this.addToSearchHistory(query);
        }
        
        // Show loading state
        this.showLoadingState();
        
        try {
            // Simulate search delay
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Perform actual search
            const results = await this.executeSearch(query);
            
            // Display results
            this.displaySearchResults(results, query);
            
        } catch (error) {
            console.error('Search error:', error);
            this.showErrorState('Error al realizar la búsqueda');
        }
    }
    
    /**
     * Execute search across different data sources
     */
    async executeSearch(query) {
        const results = {
            documents: [],
            modules: [],
            settings: [],
            total: 0
        };
        
        // Search in documents (mock data for now)
        if (this.searchFilters.type === 'all' || this.searchFilters.type === 'documents') {
            results.documents = this.searchDocuments(query);
        }
        
        // Search in modules
        if (this.searchFilters.type === 'all' || this.searchFilters.type === 'modules') {
            results.modules = this.searchModules(query);
        }
        
        // Search in settings
        if (this.searchFilters.type === 'all' || this.searchFilters.type === 'settings') {
            results.settings = this.searchSettings(query);
        }
        
        results.total = results.documents.length + results.modules.length + results.settings.length;
        
        return results;
    }
    
    /**
     * Search in documents (mock implementation)
     */
    searchDocuments(query) {
        const mockDocuments = [
            {
                id: 1,
                title: 'Contrato de Trabajo - Juan Pérez',
                type: 'contract',
                module: 'lexflow',
                content: 'Contrato de trabajo por tiempo indefinido...',
                lastModified: new Date(),
                path: '/documentos/contratos/juan_perez.pdf'
            },
            {
                id: 2,
                title: 'Reporte Financiero Q1 2024',
                type: 'report',
                module: 'finsync',
                content: 'Análisis financiero del primer trimestre...',
                lastModified: new Date(Date.now() - 86400000),
                path: '/reportes/financiero_q1_2024.xlsx'
            },
            {
                id: 3,
                title: 'Auditoría Interna - Procesos',
                type: 'audit',
                module: 'auditpro',
                content: 'Evaluación de procesos internos...',
                lastModified: new Date(Date.now() - 172800000),
                path: '/auditorias/procesos_internos.docx'
            }
        ];
        
        return mockDocuments.filter(doc => 
            doc.title.toLowerCase().includes(query.toLowerCase()) ||
            doc.content.toLowerCase().includes(query.toLowerCase())
        ).filter(doc => 
            this.searchFilters.module === 'all' || doc.module === this.searchFilters.module
        );
    }
    
    /**
     * Search in modules
     */
    searchModules(query) {
        const modules = [
            {
                id: 'lexflow',
                name: 'LexFlow',
                description: 'Gestión de documentos legales y workflow',
                category: 'legal'
            },
            {
                id: 'protocolx',
                name: 'ProtocolX',
                description: 'Protocolo de comunicaciones y notificaciones',
                category: 'communication'
            },
            {
                id: 'auditpro',
                name: 'AuditPro',
                description: 'Auditoría y control de procesos',
                category: 'audit'
            },
            {
                id: 'finsync',
                name: 'FinSync',
                description: 'Sincronización financiera y reportes',
                category: 'finance'
            }
        ];
        
        return modules.filter(module =>
            module.name.toLowerCase().includes(query.toLowerCase()) ||
            module.description.toLowerCase().includes(query.toLowerCase())
        );
    }
    
    /**
     * Search in settings
     */
    searchSettings(query) {
        const settings = [
            {
                id: 'account',
                name: 'Configuración de Cuenta',
                description: 'Gestionar perfil de usuario y preferencias',
                category: 'user'
            },
            {
                id: 'security',
                name: 'Configuración de Seguridad',
                description: 'Configurar autenticación y permisos',
                category: 'security'
            },
            {
                id: 'sync',
                name: 'Configuración de Sincronización',
                description: 'Configurar sincronización con la nube',
                category: 'sync'
            }
        ];
        
        return settings.filter(setting =>
            setting.name.toLowerCase().includes(query.toLowerCase()) ||
            setting.description.toLowerCase().includes(query.toLowerCase())
        );
    }
    
    /**
     * Display search results
     */
    displaySearchResults(results, query) {
        const resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) return;
        
        if (results.total === 0) {
            resultsContainer.innerHTML = this.renderNoResults(query);
            return;
        }
        
        resultsContainer.innerHTML = `
            <div class="search-results-header">
                <h3>Resultados para "${query}" (${results.total})</h3>
                <button class="clear-search">Limpiar búsqueda</button>
            </div>
            
            ${results.documents.length > 0 ? `
                <div class="results-section">
                    <h4 class="results-section-title">
                        <span class="section-icon">📄</span>
                        Documentos (${results.documents.length})
                    </h4>
                    <div class="results-list">
                        ${results.documents.map(doc => this.renderDocumentResult(doc, query)).join('')}
                    </div>
                </div>
            ` : ''}
            
            ${results.modules.length > 0 ? `
                <div class="results-section">
                    <h4 class="results-section-title">
                        <span class="section-icon">📦</span>
                        Módulos (${results.modules.length})
                    </h4>
                    <div class="results-list">
                        ${results.modules.map(module => this.renderModuleResult(module, query)).join('')}
                    </div>
                </div>
            ` : ''}
            
            ${results.settings.length > 0 ? `
                <div class="results-section">
                    <h4 class="results-section-title">
                        <span class="section-icon">⚙️</span>
                        Configuración (${results.settings.length})
                    </h4>
                    <div class="results-list">
                        ${results.settings.map(setting => this.renderSettingResult(setting, query)).join('')}
                    </div>
                </div>
            ` : ''}
        `;
    }
    
    /**
     * Render document search result
     */
    renderDocumentResult(doc, query) {
        const highlightedTitle = this.highlightSearchTerm(doc.title, query);
        const highlightedContent = this.highlightSearchTerm(doc.content, query);
        
        return `
            <div class="result-item document-result" data-id="${doc.id}" data-type="document">
                <div class="result-icon">📄</div>
                <div class="result-content">
                    <h5 class="result-title">${highlightedTitle}</h5>
                    <p class="result-description">${highlightedContent.substring(0, 120)}...</p>
                    <div class="result-meta">
                        <span class="result-module">${doc.module}</span>
                        <span class="result-date">${this.formatDate(doc.lastModified)}</span>
                        <span class="result-path">${doc.path}</span>
                    </div>
                </div>
                <div class="result-actions">
                    <button class="result-action" title="Abrir">📂</button>
                </div>
            </div>
        `;
    }
    
    /**
     * Render module search result
     */
    renderModuleResult(module, query) {
        const highlightedName = this.highlightSearchTerm(module.name, query);
        const highlightedDescription = this.highlightSearchTerm(module.description, query);
        
        return `
            <div class="result-item module-result" data-id="${module.id}" data-type="module">
                <div class="result-icon">📦</div>
                <div class="result-content">
                    <h5 class="result-title">${highlightedName}</h5>
                    <p class="result-description">${highlightedDescription}</p>
                    <div class="result-meta">
                        <span class="result-category">${module.category}</span>
                    </div>
                </div>
                <div class="result-actions">
                    <button class="result-action" title="Abrir módulo">🚀</button>
                </div>
            </div>
        `;
    }
    
    /**
     * Render setting search result
     */
    renderSettingResult(setting, query) {
        const highlightedName = this.highlightSearchTerm(setting.name, query);
        const highlightedDescription = this.highlightSearchTerm(setting.description, query);
        
        return `
            <div class="result-item setting-result" data-id="${setting.id}" data-type="setting">
                <div class="result-icon">⚙️</div>
                <div class="result-content">
                    <h5 class="result-title">${highlightedName}</h5>
                    <p class="result-description">${highlightedDescription}</p>
                    <div class="result-meta">
                        <span class="result-category">${setting.category}</span>
                    </div>
                </div>
                <div class="result-actions">
                    <button class="result-action" title="Configurar">⚙️</button>
                </div>
            </div>
        `;
    }
    
    /**
     * Render no results state
     */
    renderNoResults(query) {
        return `
            <div class="no-results">
                <div class="no-results-icon">🔍</div>
                <h3>No se encontraron resultados</h3>
                <p>No hay resultados para "${query}"</p>
                <div class="no-results-suggestions">
                    <h4>Intenta:</h4>
                    <ul>
                        <li>Verificar la ortografía</li>
                        <li>Usar términos más generales</li>
                        <li>Probar sinónimos</li>
                        <li>Ajustar los filtros de búsqueda</li>
                    </ul>
                </div>
            </div>
        `;
    }
    
    /**
     * Show loading state
     */
    showLoadingState() {
        const resultsContainer = document.getElementById('search-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="search-loading">
                    <div class="loading-spinner"></div>
                    <p>Buscando...</p>
                </div>
            `;
        }
    }
    
    /**
     * Show error state
     */
    showErrorState(message) {
        const resultsContainer = document.getElementById('search-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="search-error">
                    <div class="error-icon">❌</div>
                    <h3>Error en la búsqueda</h3>
                    <p>${message}</p>
                    <button class="retry-search">Reintentar</button>
                </div>
            `;
        }
    }
    
    /**
     * Show initial state
     */
    showInitialState() {
        const resultsContainer = document.getElementById('search-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = this.renderInitialState();
        }
    }
    
    /**
     * Handle filter changes
     */
    handleFilterChange(filterElement) {
        const filterType = filterElement.dataset.filter;
        const filterValue = filterElement.dataset.value;
        
        // Update filter
        this.searchFilters[filterType] = filterValue;
        
        // Update UI
        const filterGroup = filterElement.closest('.filter-group');
        filterGroup.querySelectorAll('.filter-option').forEach(btn => {
            btn.classList.remove('active');
        });
        filterElement.classList.add('active');
        
        // Re-run search if there's a current query
        if (this.currentQuery) {
            this.performSearch(this.currentQuery, false);
        }
    }
    
    /**
     * Clear search
     */
    clearSearch() {
        const searchInput = document.getElementById('global-search-input');
        if (searchInput) {
            searchInput.value = '';
            searchInput.focus();
        }
        
        this.currentQuery = '';
        this.showInitialState();
        
        const clearBtn = document.querySelector('.clear-search');
        if (clearBtn) {
            clearBtn.style.display = 'none';
        }
    }
    
    /**
     * Select item from search history
     */
    selectHistoryItem(query) {
        const searchInput = document.getElementById('global-search-input');
        if (searchInput) {
            searchInput.value = query;
        }
        this.performSearch(query);
    }
    
    /**
     * Add query to search history
     */
    addToSearchHistory(query) {
        // Remove if already exists
        this.searchHistory = this.searchHistory.filter(item => item !== query);
        
        // Add to beginning
        this.searchHistory.unshift(query);
        
        // Limit to 10 items
        this.searchHistory = this.searchHistory.slice(0, 10);
        
        // Save to localStorage
        this.saveSearchHistory();
    }
    
    /**
     * Load search history from localStorage
     */
    loadSearchHistory() {
        try {
            const saved = localStorage.getItem('coredesk_search_history');
            if (saved) {
                this.searchHistory = JSON.parse(saved);
            }
        } catch (error) {
            console.error('Error loading search history:', error);
            this.searchHistory = [];
        }
    }
    
    /**
     * Save search history to localStorage
     */
    saveSearchHistory() {
        try {
            localStorage.setItem('coredesk_search_history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.error('Error saving search history:', error);
        }
    }
    
    /**
     * Highlight search terms in text
     */
    highlightSearchTerm(text, term) {
        if (!term) return text;
        
        const regex = new RegExp(`(${term})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
    
    /**
     * Format date for display
     */
    formatDate(date) {
        const now = new Date();
        const diff = now - date;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (days === 0) return 'Hoy';
        if (days === 1) return 'Ayer';
        if (days < 7) return `Hace ${days} días`;
        
        return date.toLocaleDateString();
    }
    
    /**
     * Debounce utility function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Create global instance
const searchPanel = new SearchPanel();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = searchPanel;
}

// Make available globally
if (typeof window !== 'undefined') {
    window.searchPanel = searchPanel;
}