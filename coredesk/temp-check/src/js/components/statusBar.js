/**
 * Status Bar Component
 * Implements VS Code-inspired status bar with module info, connection status, and metrics
 * as specified in PRD section 3.4
 */

class StatusBar {
    constructor() {
        this.connectionStatus = 'offline';
        this.currentModule = null;
        this.syncStatus = 'idle';
        this.licenseInfo = null;
        this.updateInterval = null;
        
        this.initialize();
    }

    /**
     * Initialize the status bar
     */
    initialize() {
        console.log('UI', '[StatusBar] Initializing...', );
        
        this.createStatusBarStructure();
        this.setupEventListeners();
        this.startPeriodicUpdates();
        this.updateInitialStatus();
        
        console.log('UI', '[StatusBar] Initialized successfully', );
    }

    /**
     * Create the status bar HTML structure (use existing HTML structure)
     */
    createStatusBarStructure() {
        const statusBar = document.querySelector('.status-bar');
        
        // Don't override existing HTML structure, just ensure it exists
        if (!statusBar) {
            this.createStatusBarElement();
        }
        
        // The HTML structure already exists in index.html, just enhance it
        console.log('UI', '[StatusBar] Using existing HTML structure');
    }

    /**
     * Create status bar element if it doesn't exist
     */
    createStatusBarElement() {
        const statusBar = document.createElement('div');
        statusBar.id = 'statusbar';
        statusBar.className = 'status-bar';
        
        // Create the structure that matches index.html
        statusBar.innerHTML = `
            <div class="status-left">
                <span id="current-module" class="status-item">
                    <span class="status-icon">⚙️</span>
                    <span class="status-text">Sin módulo activo</span>
                </span>
                <span id="connection-status" class="status-item">
                    <span class="status-icon offline">🔴</span>
                    <span class="status-text">Offline</span>
                </span>
            </div>
            
            <div class="status-right">
                <span id="license-status" class="status-item">
                    <span class="status-text">Sin licencia</span>
                </span>
                <span id="sync-status" class="status-item sync-status-trigger" title="Estado de sincronización">
                    <span class="status-icon">🔄</span>
                    <span class="status-text">Sin sincronizar</span>
                </span>
            </div>
        `;
        
        // Insert at bottom of app container
        const appContainer = document.querySelector('.app-container');
        if (appContainer) {
            appContainer.appendChild(statusBar);
        } else {
            document.body.appendChild(statusBar);
        }
        
        return statusBar;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for module changes
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.MODULE_SWITCHED, (event) => {
            this.updateModuleStatus(event.detail.to);
        });

        // Listen for license activation
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.LICENSE_ACTIVATED, (event) => {
            this.updateLicenseStatus(event.detail.license);
        });

        // Listen for sync events
        window.addEventListener('coredesk:sync:started', () => {
            this.updateSyncStatus('syncing');
        });

        window.addEventListener('coredesk:sync:completed', () => {
            this.updateSyncStatus('completed');
        });

        window.addEventListener('coredesk:sync:error', () => {
            this.updateSyncStatus('error');
        });

        // Listen for connection changes
        window.addEventListener('online', () => {
            this.updateConnectionStatus('online');
        });

        window.addEventListener('offline', () => {
            this.updateConnectionStatus('offline');
        });

        // Click handlers for status items
        const statusBar = document.querySelector('.status-bar');
        if (statusBar) {
            statusBar.addEventListener('click', (e) => {
                const statusItem = e.target.closest('.status-item');
                if (statusItem) {
                    this.handleStatusItemClick(statusItem);
                }
            });
        }
    }

    /**
     * Handle status item clicks
     */
    handleStatusItemClick(item) {
        const id = item.id;
        
        console.log('UI', `[StatusBar] Status item clicked: ${id}`);
        
        switch (id) {
            case 'current-module':
                this.openModuleSelector();
                break;
            case 'sync-status':
                this.showSyncDetails();
                break;
            case 'license-status':
                this.showLicenseDetails();
                break;
            case 'connection-status':
                this.showConnectionDetails();
                break;
            default:
                console.log('UI', `[StatusBar] Unhandled status item: ${id}`);
                break;
        }
    }

    /**
     * Open module selector
     */
    openModuleSelector() {
        console.log('UI', '[StatusBar] Opening module selector', );
        
        if (window.exclusiveModuleController) {
            const availableModules = window.exclusiveModuleController.getAvailableModules();
            this.showModuleSelectionModal(availableModules);
        }
    }

    /**
     * Show module selection modal
     */
    showModuleSelectionModal(modules) {
        const modal = document.createElement('div');
        modal.className = 'modal-container';
        modal.innerHTML = `
            <div class="modal module-selector-modal">
                <div class="modal-header">
                    <h3 class="modal-title">Seleccionar Módulo</h3>
                    <button class="modal-close" id="modal-close">×</button>
                </div>
                <div class="modal-body">
                    <div class="module-grid">
                        ${modules.map(module => `
                            <div class="module-card ${module.isActive ? 'active' : ''}" data-module="${module.code}">
                                <div class="module-icon">
                                    ${this.getModuleIcon(module.code)}
                                </div>
                                <div class="module-info">
                                    <h4>${module.name}</h4>
                                    <p>${module.description || 'Módulo disponible'}</p>
                                </div>
                                ${module.isActive ? '<div class="active-indicator">Activo</div>' : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Event handlers
        modal.querySelector('#modal-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelectorAll('.module-card').forEach(card => {
            card.addEventListener('click', () => {
                const moduleCode = card.dataset.module;
                if (moduleCode && window.exclusiveModuleController) {
                    window.exclusiveModuleController.switchToModule(moduleCode);
                    document.body.removeChild(modal);
                }
            });
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Get module icon (same as panelManager implementation)
     */
    getModuleIcon(moduleCode) {
        const icons = {
            lexflow: '📄',
            protocolx: '📋',
            auditpro: '📊',
            finsync: '💰'
        };
        return icons[moduleCode] || '📦';
    }

    /**
     * Update module status
     */
    updateModuleStatus(moduleCode) {
        this.currentModule = moduleCode;
        const moduleElement = document.getElementById('current-module');
        
        if (moduleElement) {
            const statusText = moduleElement.querySelector('.status-text');
            const statusIcon = moduleElement.querySelector('.status-icon');
            
            if (moduleCode) {
                const moduleInfo = window.exclusiveModuleController?.getModuleInfo(moduleCode);
                if (moduleInfo) {
                    statusText.textContent = moduleInfo.name;
                    statusIcon.textContent = this.getModuleIcon(moduleCode);
                    moduleElement.style.color = moduleInfo.color || '#007acc';
                    moduleElement.classList.add('active');
                } else {
                    statusText.textContent = moduleCode.toUpperCase();
                    statusIcon.textContent = this.getModuleIcon(moduleCode);
                    moduleElement.classList.add('active');
                }
            } else {
                statusText.textContent = 'Sin módulo activo';
                statusIcon.textContent = '⚙️';
                moduleElement.classList.remove('active');
                moduleElement.style.color = '';
            }
        }
    }

    /**
     * Update connection status
     */
    updateConnectionStatus(status) {
        this.connectionStatus = status;
        const connectionElement = document.getElementById('connection-status');
        
        if (connectionElement) {
            const statusText = connectionElement.querySelector('.status-text');
            const statusIcon = connectionElement.querySelector('.status-icon');
            
            connectionElement.classList.remove('online', 'offline');
            connectionElement.classList.add(status);
            
            switch (status) {
                case 'online':
                    statusText.textContent = 'Conectado';
                    statusIcon.textContent = '🟢';
                    statusIcon.classList.remove('offline');
                    statusIcon.classList.add('online');
                    break;
                case 'offline':
                default:
                    statusText.textContent = 'Desconectado';
                    statusIcon.textContent = '🔴';
                    statusIcon.classList.remove('online');
                    statusIcon.classList.add('offline');
                    break;
            }
        }
    }

    /**
     * Update sync status
     */
    updateSyncStatus(status) {
        this.syncStatus = status;
        const syncElement = document.getElementById('sync-status');
        
        if (syncElement) {
            const statusText = syncElement.querySelector('.status-text');
            const statusIcon = syncElement.querySelector('.status-icon');
            
            syncElement.classList.remove('syncing', 'completed', 'error', 'idle');
            syncElement.classList.add(status);
            
            switch (status) {
                case 'syncing':
                    statusText.textContent = 'Sincronizando...';
                    statusIcon.textContent = '🔄';
                    statusIcon.style.animation = 'spin 1s linear infinite';
                    break;
                case 'completed':
                    statusText.textContent = 'Sincronización completa';
                    statusIcon.textContent = '✅';
                    statusIcon.style.animation = '';
                    setTimeout(() => {
                        this.updateSyncStatus('idle');
                    }, 3000);
                    break;
                case 'error':
                    statusText.textContent = 'Error de sincronización';
                    statusIcon.textContent = '❌';
                    statusIcon.style.animation = '';
                    break;
                case 'idle':
                default:
                    statusText.textContent = 'Sin sincronizar';
                    statusIcon.textContent = '🔄';
                    statusIcon.style.animation = '';
                    break;
            }
        }
    }

    /**
     * Update license status
     */
    updateLicenseStatus(license) {
        this.licenseInfo = license;
        const licenseElement = document.getElementById('license-status');
        
        if (licenseElement) {
            const statusText = licenseElement.querySelector('.status-text');
            
            if (license) {
                const expirationDate = new Date(license.validUntil);
                const daysRemaining = Math.ceil((expirationDate - new Date()) / (1000 * 60 * 60 * 24));
                
                statusText.textContent = `${license.type.toUpperCase()} - ${daysRemaining} días`;
                licenseElement.classList.add('active');
                
                if (daysRemaining <= 7) {
                    licenseElement.classList.add('warning');
                }
            } else {
                statusText.textContent = 'Sin licencia';
                licenseElement.classList.remove('active', 'warning');
            }
        }
    }

    /**
     * Update performance metrics
     */
    updatePerformanceMetrics() {
        const performanceElement = document.getElementById('performance-info');
        
        if (performanceElement && window.performance) {
            const statusText = performanceElement.querySelector('.status-text');
            
            // Simple CPU usage estimation based on timing
            const now = performance.now();
            const memoryInfo = performance.memory || { usedJSHeapSize: 0, totalJSHeapSize: 0 };
            const memoryUsage = Math.round((memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize) * 100) || 0;
            
            statusText.textContent = `MEM: ${memoryUsage}%`;
        }
    }

    /**
     * Update time
     */
    updateTime() {
        const timeElement = document.getElementById('time-info');
        
        if (timeElement) {
            const statusText = timeElement.querySelector('.status-text');
            const now = new Date();
            const timeString = now.toLocaleTimeString('es-ES', { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false
            });
            
            statusText.textContent = timeString;
        }
    }

    /**
     * Start periodic updates
     */
    startPeriodicUpdates() {
        // Update time every second
        this.updateInterval = setInterval(() => {
            this.updateTime();
            this.updatePerformanceMetrics();
        }, 1000);
        
        // Track interval with memory manager
        window.memoryManager?.trackTimer('statusBarUpdates', this.updateInterval);
    }

    /**
     * Update initial status
     */
    updateInitialStatus() {
        // Set initial connection status
        this.updateConnectionStatus(navigator.onLine ? 'online' : 'offline');
        
        // Set initial module status
        if (window.exclusiveModuleController) {
            const currentModule = window.exclusiveModuleController.currentModule;
            if (currentModule) {
                this.updateModuleStatus(currentModule);
            }
        }
        
        // Set initial license status
        if (window.licenseManager) {
            const license = window.licenseManager.currentLicense;
            if (license) {
                this.updateLicenseStatus(license);
            }
        }
        
        // Set initial time
        this.updateTime();
    }

    /**
     * Show sync details
     */
    showSyncDetails() {
        console.log('UI', '[StatusBar] Showing sync details', );
        
        // Open sync status panel if available
        if (window.syncStatusPanel) {
            window.syncStatusPanel.show();
        } else if (window.SyncStatusPanel) {
            // Try to create instance if class is available
            const syncPanel = new window.SyncStatusPanel();
            syncPanel.show();
        } else {
            console.warn('UI', '[StatusBar] SyncStatusPanel not available');
            // Show a simple modal as fallback
            this.showSimpleSyncModal();
        }
    }

    /**
     * Show simple sync modal fallback
     */
    showSimpleSyncModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-container';
        modal.innerHTML = `
            <div class="modal sync-modal">
                <div class="modal-header">
                    <h3 class="modal-title">Estado de Sincronización</h3>
                    <button class="modal-close" id="sync-modal-close">×</button>
                </div>
                <div class="modal-body">
                    <div class="sync-info">
                        <div class="status-row">
                            <span class="status-label">Estado:</span>
                            <span class="status-value">${this.getSyncStatusText()}</span>
                        </div>
                        <div class="status-row">
                            <span class="status-label">Conexión:</span>
                            <span class="status-value">${this.connectionStatus === 'online' ? 'Conectado' : 'Desconectado'}</span>
                        </div>
                        <div class="status-row">
                            <span class="status-label">Última sincronización:</span>
                            <span class="status-value">No disponible</span>
                        </div>
                    </div>
                    <div class="sync-actions">
                        <button class="btn btn-primary" onclick="alert('Función de sincronización manual no implementada')">
                            🔄 Sincronizar Ahora
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Event handlers
        modal.querySelector('#sync-modal-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Get sync status text
     */
    getSyncStatusText() {
        switch (this.syncStatus) {
            case 'syncing': return 'Sincronizando...';
            case 'completed': return 'Sincronización completa';
            case 'error': return 'Error de sincronización';
            case 'idle':
            default: return 'Sincronización inactiva';
        }
    }

    /**
     * Show license details
     */
    showLicenseDetails() {
        console.log('UI', '[StatusBar] Showing license details', );
        // TODO: Implement license details modal
    }

    /**
     * Show connection details
     */
    showConnectionDetails() {
        console.log('UI', '[StatusBar] Showing connection details', );
        // TODO: Implement connection details modal
    }

    /**
     * Show performance details
     */
    showPerformanceDetails() {
        console.log('UI', '[StatusBar] Showing performance details', );
        // TODO: Implement performance details modal
    }

    /**
     * Cleanup
     */
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            window.memoryManager?.untrackResource('statusBarUpdates');
        }
    }

    /**
     * Get current status
     */
    getStatus() {
        return {
            connectionStatus: this.connectionStatus,
            currentModule: this.currentModule,
            syncStatus: this.syncStatus,
            licenseInfo: this.licenseInfo,
            initialized: true
        };
    }
}

// Create global instance
window.statusBar = new StatusBar();
window.statusBarInitialized = true;

console.log('UI', '[StatusBar] Global instance created successfully', );
