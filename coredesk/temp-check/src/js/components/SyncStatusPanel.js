/**
 * SyncStatusPanel.js
 * UI component for displaying data synchronization status and controls
 * Shows sync progress, conflicts, and provides manual sync controls
 */

class SyncStatusPanel {
    constructor() {
        this.panel = null;
        this.isVisible = false;
        this.dataSyncService = null;
        this.updateInterval = null;
        this.updateIntervalMs = 5000; // 5 seconds
        
        this.syncStatus = {
            isOnline: navigator.onLine,
            isSyncing: false,
            lastSync: null,
            queueSize: 0,
            conflictCount: 0,
            cloudEnabled: false
        };
        
        this.initialize();
    }

    /**
     * Initialize the sync status panel
     */
    async initialize() {
        console.log('SyncStatus', '[SyncStatusPanel] Initializing...', );
        
        try {
            // Wait for DataSyncService to be available
            await this.waitForDataSyncService();
            
            // Create panel UI
            this.createPanel();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Start status updates
            this.startStatusUpdates();
            
            console.log('SyncStatus', '[SyncStatusPanel] Initialized successfully', );
            
        } catch (error) {
            console.error('SyncStatus', '[SyncStatusPanel] Initialization failed:', error);
        }
    }

    /**
     * Wait for DataSyncService to be available
     * @returns {Promise<void>}
     * @private
     */
    async waitForDataSyncService() {
        return new Promise((resolve) => {
            const checkService = () => {
                if (window.dataSyncService) {
                    this.dataSyncService = window.dataSyncService;
                    resolve();
                } else {
                    setTimeout(checkService, 100);
                }
            };
            checkService();
        });
    }

    /**
     * Create the panel UI
     * @private
     */
    createPanel() {
        this.panel = document.createElement('div');
        this.panel.className = 'sync-status-panel';
        this.panel.id = 'sync-status-panel';
        this.panel.style.display = 'none';
        
        this.panel.innerHTML = `
            <div class="sync-panel-header">
                <h3>Estado de Sincronización</h3>
                <button class="sync-panel-close" id="sync-panel-close">×</button>
            </div>
            
            <div class="sync-panel-content">
                <!-- Connection Status -->
                <div class="sync-section">
                    <h4>Conexión</h4>
                    <div class="status-row">
                        <span class="status-label">Estado de Red:</span>
                        <span id="network-status" class="status-value">
                            <span class="status-indicator"></span>
                            <span class="status-text">Verificando...</span>
                        </span>
                    </div>
                    <div class="status-row">
                        <span class="status-label">Sincronización:</span>
                        <span id="sync-enabled-status" class="status-value">
                            <span class="status-indicator"></span>
                            <span class="status-text">Verificando...</span>
                        </span>
                    </div>
                    <div class="status-row">
                        <span class="status-label">Nube:</span>
                        <span id="cloud-sync-status" class="status-value">
                            <span class="status-indicator"></span>
                            <span class="status-text">Verificando...</span>
                        </span>
                    </div>
                </div>
                
                <!-- Sync Status -->
                <div class="sync-section">
                    <h4>Estado Actual</h4>
                    <div class="status-row">
                        <span class="status-label">Última Sincronización:</span>
                        <span id="last-sync-time" class="status-value">Nunca</span>
                    </div>
                    <div class="status-row">
                        <span class="status-label">Operaciones Pendientes:</span>
                        <span id="queue-size" class="status-value">0</span>
                    </div>
                    <div class="status-row">
                        <span class="status-label">Conflictos:</span>
                        <span id="conflict-count" class="status-value">0</span>
                    </div>
                </div>
                
                <!-- Progress -->
                <div class="sync-section" id="sync-progress-section" style="display: none;">
                    <h4>Progreso de Sincronización</h4>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div id="progress-bar-fill" class="progress-fill"></div>
                        </div>
                        <span id="progress-text" class="progress-text">0%</span>
                    </div>
                    <div id="sync-details" class="sync-details">
                        <div class="sync-detail-item">
                            <span>Local:</span>
                            <span id="local-operations">0 operaciones</span>
                        </div>
                        <div class="sync-detail-item">
                            <span>Nube:</span>
                            <span id="cloud-operations">0 operaciones</span>
                        </div>
                    </div>
                </div>
                
                <!-- Conflicts -->
                <div class="sync-section" id="conflicts-section" style="display: none;">
                    <h4>Conflictos de Sincronización</h4>
                    <div id="conflicts-list" class="conflicts-list">
                        <!-- Conflicts will be populated here -->
                    </div>
                </div>
                
                <!-- Statistics -->
                <div class="sync-section">
                    <h4>Estadísticas</h4>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Total Operaciones:</span>
                            <span id="total-operations" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Sincronizaciones Exitosas:</span>
                            <span id="successful-syncs" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Sincronizaciones Fallidas:</span>
                            <span id="failed-syncs" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Conflictos Resueltos:</span>
                            <span id="resolved-conflicts" class="stat-value">0</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="sync-panel-footer">
                <div class="sync-controls">
                    <button id="force-sync-btn" class="btn btn-primary" disabled>
                        <span class="btn-icon">🔄</span>
                        Sincronizar Ahora
                    </button>
                    <button id="resolve-conflicts-btn" class="btn btn-secondary" disabled>
                        <span class="btn-icon">⚡</span>
                        Resolver Conflictos
                    </button>
                    <button id="sync-settings-btn" class="btn btn-secondary">
                        <span class="btn-icon">⚙️</span>
                        Configuración
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.panel);
    }

    /**
     * Set up event listeners
     * @private
     */
    setupEventListeners() {
        // Close panel
        const closeButton = this.panel.querySelector('#sync-panel-close');
        closeButton.addEventListener('click', () => this.hide());
        
        // Control buttons
        const forceSyncBtn = this.panel.querySelector('#force-sync-btn');
        forceSyncBtn.addEventListener('click', () => this.forceSync());
        
        const resolveConflictsBtn = this.panel.querySelector('#resolve-conflicts-btn');
        resolveConflictsBtn.addEventListener('click', () => this.showConflictResolution());
        
        const syncSettingsBtn = this.panel.querySelector('#sync-settings-btn');
        syncSettingsBtn.addEventListener('click', () => this.showSyncSettings());
        
        // Listen for sync events
        if (this.dataSyncService) {
            this.dataSyncService.on('sync-started-operation', (data) => {
                this.handleSyncStarted(data);
            });
            
            this.dataSyncService.on('sync-completed', (data) => {
                this.handleSyncCompleted(data);
            });
            
            this.dataSyncService.on('sync-error', (data) => {
                this.handleSyncError(data);
            });
            
            this.dataSyncService.on('network-changed', (data) => {
                this.handleNetworkChanged(data);
            });
            
            this.dataSyncService.on('conflict-requires-resolution', (data) => {
                this.handleConflictDetected(data);
            });
        }
        
        // Network status events
        window.addEventListener('online', () => {
            this.updateNetworkStatus(true);
        });
        
        window.addEventListener('offline', () => {
            this.updateNetworkStatus(false);
        });
    }

    /**
     * Show the sync status panel
     */
    show() {
        console.log('SyncStatus', '[SyncStatusPanel] Showing panel', );
        
        this.isVisible = true;
        this.panel.style.display = 'block';
        
        // Update status immediately
        this.updateStatus();
        
        // Focus on panel
        this.panel.focus();
    }

    /**
     * Hide the sync status panel
     */
    hide() {
        this.isVisible = false;
        this.panel.style.display = 'none';
        console.log('SyncStatus', '[SyncStatusPanel] Panel hidden', );
    }

    /**
     * Toggle panel visibility
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * Start periodic status updates
     * @private
     */
    startStatusUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.updateInterval = setInterval(() => {
            if (this.isVisible && this.dataSyncService) {
                this.updateStatus();
            }
        }, this.updateIntervalMs);
        
        // Update immediately
        this.updateStatus();
    }

    /**
     * Stop periodic status updates
     * @private
     */
    stopStatusUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * Update the panel status
     * @private
     */
    updateStatus() {
        if (!this.dataSyncService) {
            return;
        }
        
        try {
            // Get current stats (includes status and all data)
            const stats = this.dataSyncService.getSyncStats();
            const status = stats.status; // This is the string status
            
            // Update connection status
            this.updateConnectionStatus(status);
            
            // Update sync status (pass stats object, not status string)
            this.updateSyncStatus(stats);
            
            // Update statistics
            this.updateStatistics(stats);
            
            // Update controls
            this.updateControls(stats);
            
            // Update conflicts
            this.updateConflicts(stats);
            
        } catch (error) {
            console.error('SyncStatus', '[SyncStatusPanel] Error updating status:', error);
        }
    }

    /**
     * Update connection status section
     * @param {Object} status - Sync status
     * @private
     */
    updateConnectionStatus(status) {
        // Network status
        const networkStatus = this.panel.querySelector('#network-status');
        const networkIndicator = networkStatus.querySelector('.status-indicator');
        const networkText = networkStatus.querySelector('.status-text');
        
        if (status.isOnline) {
            networkIndicator.className = 'status-indicator online';
            networkText.textContent = 'Conectado';
        } else {
            networkIndicator.className = 'status-indicator offline';
            networkText.textContent = 'Sin conexión';
        }
        
        // Sync enabled status
        const syncEnabledStatus = this.panel.querySelector('#sync-enabled-status');
        const syncIndicator = syncEnabledStatus.querySelector('.status-indicator');
        const syncText = syncEnabledStatus.querySelector('.status-text');
        
        if (status.syncEnabled) {
            syncIndicator.className = 'status-indicator enabled';
            syncText.textContent = 'Habilitada';
        } else {
            syncIndicator.className = 'status-indicator disabled';
            syncText.textContent = 'Deshabilitada';
        }
        
        // Cloud sync status
        const cloudSyncStatus = this.panel.querySelector('#cloud-sync-status');
        const cloudIndicator = cloudSyncStatus.querySelector('.status-indicator');
        const cloudText = cloudSyncStatus.querySelector('.status-text');
        
        if (status.cloudSyncEnabled) {
            cloudIndicator.className = 'status-indicator enabled';
            cloudText.textContent = 'Habilitada';
        } else {
            cloudIndicator.className = 'status-indicator disabled';
            cloudText.textContent = 'Deshabilitada';
        }
    }

    /**
     * Update sync status section
     * @param {Object} status - Sync status
     * @private
     */
    updateSyncStatus(stats) {
        // Last sync time
        const lastSyncElement = this.panel.querySelector('#last-sync-time');
        if (stats.lastSyncTime) {
            const lastSync = new Date(stats.lastSyncTime);
            lastSyncElement.textContent = this.formatRelativeTime(lastSync);
        } else {
            lastSyncElement.textContent = 'Nunca';
        }
        
        // Queue size
        const queueSizeElement = this.panel.querySelector('#queue-size');
        if (queueSizeElement) {
            queueSizeElement.textContent = (stats.queueLength || 0).toString();
        }
        
        // Conflict count
        const conflictCountElement = this.panel.querySelector('#conflict-count');
        if (conflictCountElement) {
            conflictCountElement.textContent = (stats.conflictsResolved || 0).toString();
        }
        
        // Show/hide progress section
        const progressSection = this.panel.querySelector('#sync-progress-section');
        if (progressSection) {
            if (stats.syncInProgress) {
                progressSection.style.display = 'block';
                this.updateSyncProgress();
            } else {
                progressSection.style.display = 'none';
            }
        }
    }

    /**
     * Update sync progress
     * @private
     */
    updateSyncProgress() {
        // This would be enhanced with actual progress data
        const progressFill = this.panel.querySelector('#progress-bar-fill');
        const progressText = this.panel.querySelector('#progress-text');
        
        // Simulate progress
        const progress = Math.min(100, Math.random() * 100);
        progressFill.style.width = `${progress}%`;
        progressText.textContent = `${Math.round(progress)}%`;
        
        // Update operation counts
        const localOpsElement = this.panel.querySelector('#local-operations');
        const cloudOpsElement = this.panel.querySelector('#cloud-operations');
        
        localOpsElement.textContent = '0 operaciones';
        cloudOpsElement.textContent = '0 operaciones';
    }

    /**
     * Update statistics section
     * @param {Object} stats - Sync statistics
     * @private
     */
    updateStatistics(stats) {
        const totalOpsElement = this.panel.querySelector('#total-operations');
        if (totalOpsElement) {
            totalOpsElement.textContent = (stats.totalOperations || 0).toString();
        }
        
        const successfulSyncsElement = this.panel.querySelector('#successful-syncs');
        if (successfulSyncsElement) {
            successfulSyncsElement.textContent = (stats.successfulOperations || 0).toString();
        }
        
        const failedSyncsElement = this.panel.querySelector('#failed-syncs');
        if (failedSyncsElement) {
            failedSyncsElement.textContent = (stats.failedOperations || 0).toString();
        }
        
        const resolvedConflictsElement = this.panel.querySelector('#resolved-conflicts');
        if (resolvedConflictsElement) {
            resolvedConflictsElement.textContent = (stats.conflictsResolved || 0).toString();
        }
    }

    /**
     * Update control buttons
     * @param {Object} status - Sync status
     * @private
     */
    updateControls(stats) {
        const forceSyncBtn = this.panel.querySelector('#force-sync-btn');
        const resolveConflictsBtn = this.panel.querySelector('#resolve-conflicts-btn');
        
        if (forceSyncBtn) {
            // Enable/disable force sync button
            forceSyncBtn.disabled = !stats.syncEnabled || stats.syncInProgress || !stats.isOnline;
            
            // Update button text for syncing state
            if (stats.syncInProgress) {
                forceSyncBtn.innerHTML = '<span class="btn-icon">⏳</span>Sincronizando...';
            } else {
                forceSyncBtn.innerHTML = '<span class="btn-icon">🔄</span>Sincronizar Ahora';
            }
        }
        
        if (resolveConflictsBtn) {
            // Enable/disable resolve conflicts button
            resolveConflictsBtn.disabled = (stats.conflictsResolved || 0) === 0;
        }
    }

    /**
     * Update conflicts section
     * @param {Object} status - Sync status
     * @private
     */
    updateConflicts(stats) {
        const conflictsSection = this.panel.querySelector('#conflicts-section');
        const conflictsList = this.panel.querySelector('#conflicts-list');
        
        if (conflictsSection && conflictsList && (stats.conflictsResolved || 0) > 0) {
            conflictsSection.style.display = 'block';
            
            // This would be populated with actual conflicts
            conflictsList.innerHTML = `
                <div class="conflict-item">
                    <div class="conflict-info">
                        <span class="conflict-type">Conflicto de datos</span>
                        <span class="conflict-description">Documento modificado en ambos dispositivos</span>
                    </div>
                    <button class="btn btn-sm btn-secondary">Resolver</button>
                </div>
            `;
        } else if (conflictsSection) {
            conflictsSection.style.display = 'none';
        }
    }

    /**
     * Force synchronization
     * @private
     */
    async forceSync() {
        if (!this.dataSyncService) {
            return;
        }
        
        console.log('SyncStatus', '[SyncStatusPanel] Force sync requested', );
        
        try {
            const result = await this.dataSyncService.forcSync();
            
            if (result.success) {
                this.showNotification('Sincronización completada exitosamente', 'success');
            } else {
                this.showNotification(`Error en sincronización: ${result.reason}`, 'error');
            }
            
        } catch (error) {
            console.error('SyncStatus', '[SyncStatusPanel] Force sync failed:', error);
            this.showNotification('Error al forzar sincronización', 'error');
        }
    }

    /**
     * Show conflict resolution interface
     * @private
     */
    showConflictResolution() {
        console.log('SyncStatus', '[SyncStatusPanel] Showing conflict resolution', );
        
        // This would open a detailed conflict resolution modal
        this.showNotification('Funcionalidad de resolución de conflictos próximamente', 'info');
    }

    /**
     * Show sync settings
     * @private
     */
    showSyncSettings() {
        console.log('SyncStatus', '[SyncStatusPanel] Showing sync settings', );
        
        // This would open the configuration panel
        if (window.configurationPanel) {
            window.configurationPanel.show('sync');
        } else {
            this.showNotification('Panel de configuración no disponible', 'warning');
        }
    }

    /**
     * Handle sync started event
     * @param {Object} data - Event data
     * @private
     */
    handleSyncStarted(data) {
        console.log('SyncStatus', '[SyncStatusPanel] Sync started:', data);
        
        this.syncStatus.isSyncing = true;
        this.updateStatus();
        
        this.showNotification('Sincronización iniciada', 'info');
    }

    /**
     * Handle sync completed event
     * @param {Object} data - Event data
     * @private
     */
    handleSyncCompleted(data) {
        console.log('SyncStatus', '[SyncStatusPanel] Sync completed:', data);
        
        this.syncStatus.isSyncing = false;
        this.syncStatus.lastSync = new Date(data.timestamp);
        this.updateStatus();
        
        const totalOps = data.operations.local.pushed + data.operations.cloud.pushed;
        if (totalOps > 0) {
            this.showNotification(`Sincronización completada: ${totalOps} operaciones`, 'success');
        }
    }

    /**
     * Handle sync error event
     * @param {Object} data - Event data
     * @private
     */
    handleSyncError(data) {
        console.error('SyncStatus', '[SyncStatusPanel] Sync error:', data);
        
        this.syncStatus.isSyncing = false;
        this.updateStatus();
        
        this.showNotification(`Error de sincronización: ${data.error}`, 'error');
    }

    /**
     * Handle network changed event
     * @param {Object} data - Event data
     * @private
     */
    handleNetworkChanged(data) {
        console.log('SyncStatus', '[SyncStatusPanel] Network changed:', data);
        
        this.syncStatus.isOnline = data.isOnline;
        this.updateStatus();
        
        const message = data.isOnline ? 'Conexión restaurada' : 'Sin conexión a internet';
        const type = data.isOnline ? 'success' : 'warning';
        this.showNotification(message, type);
    }

    /**
     * Handle conflict detected event
     * @param {Object} data - Event data
     * @private
     */
    handleConflictDetected(data) {
        console.log('SyncStatus', '[SyncStatusPanel] Conflict detected:', data);
        
        this.syncStatus.conflictCount++;
        this.updateStatus();
        
        this.showNotification('Conflicto de sincronización detectado', 'warning');
    }

    /**
     * Update network status
     * @param {boolean} isOnline - Network status
     * @private
     */
    updateNetworkStatus(isOnline) {
        this.syncStatus.isOnline = isOnline;
        if (this.isVisible) {
            this.updateStatus();
        }
    }

    /**
     * Format relative time
     * @param {Date} date - Date to format
     * @returns {string} Formatted time
     * @private
     */
    formatRelativeTime(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffSeconds = Math.floor(diffMs / 1000);
        const diffMinutes = Math.floor(diffSeconds / 60);
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffSeconds < 60) {
            return 'Hace unos segundos';
        } else if (diffMinutes < 60) {
            return `Hace ${diffMinutes} minuto${diffMinutes > 1 ? 's' : ''}`;
        } else if (diffHours < 24) {
            return `Hace ${diffHours} hora${diffHours > 1 ? 's' : ''}`;
        } else {
            return `Hace ${diffDays} día${diffDays > 1 ? 's' : ''}`;
        }
    }

    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type
     * @private
     */
    showNotification(message, type = 'info') {
        // This would integrate with the global notification system
        console.log("Component", `[SyncStatusPanel] ${type.toUpperCase()}: ${message}`);
        
        if (window.coreDesk && window.coreDesk.showNotification) {
            window.coreDesk.showNotification(message, type);
        }
    }

    /**
     * Get panel status
     * @returns {Object} Panel status
     */
    getStatus() {
        return {
            isVisible: this.isVisible,
            syncStatus: this.syncStatus,
            hasDataSyncService: !!this.dataSyncService
        };
    }
}

// Create global instance
window.syncStatusPanel = new SyncStatusPanel();

console.log('SyncStatus', '[SyncStatusPanel] Class defined and global instance created successfully', );