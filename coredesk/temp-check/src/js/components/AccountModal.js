/**
 * AccountModal.js
 * Account management modal for CoreDesk Framework
 * Handles user account information, preferences, and account actions
 */

class AccountModal {
    constructor() {
        this.modal = null;
        this.isVisible = false;
        this.userInfo = null;
        
        this.initialize();
    }

    /**
     * Initialize the account modal
     */
    async initialize() {
        console.log('UI', '[AccountModal] Initializing...', );
        
        try {
            // Load user information
            await this.loadUserInfo();
            
            // Create modal UI
            this.createModal();
            
            // Set up event listeners
            this.setupEventListeners();
            
            console.log('UI', '[AccountModal] Initialized successfully', );
            
        } catch (error) {
            console.error('UI', '[AccountModal] Initialization failed:', error);
        }
    }

    /**
     * Load user information
     * @private
     */
    async loadUserInfo() {
        try {
            // IMPORTANT: Get real user info from authentication system first
            let userInfo = null;
            
            // Try to get current authenticated user from CoreDeskAuth
            if (window.CoreDeskAuth?.utils?.getCurrentUser) {
                const currentUser = window.CoreDeskAuth.utils.getCurrentUser();
                if (currentUser) {
                    // Construct full name from firstName and lastName if available
                    const fullName = currentUser.firstName && currentUser.lastName ? 
                        `${currentUser.firstName} ${currentUser.lastName}` : 
                        (currentUser.name || currentUser.email);
                        
                    userInfo = {
                        name: fullName,
                        firstName: currentUser.firstName || '',
                        lastName: currentUser.lastName || '',
                        email: currentUser.email,
                        company: currentUser.company || '',
                        phone: currentUser.phone || '',
                        accountType: currentUser.role === 'admin' ? 'Administrador' : 'Usuario',
                        avatar: currentUser.avatar,
                        preferences: {
                            language: currentUser.language || 'es',
                            theme: currentUser.theme || 'dark',
                            notifications: currentUser.notifications !== undefined ? currentUser.notifications : true
                        }
                    };
                    console.log('AccountModal [AccountModal] User info loaded:', userInfo);
                }
            }
            
            // Fallback to demo data only if no real user is found
            if (!userInfo) {
                console.warn('AccountModal [AccountModal] No authenticated user found, using demo data');
                userInfo = {
                    name: 'Usuario',
                    email: '<EMAIL>',
                    accountType: 'Demo',
                    avatar: null,
                    preferences: {
                        language: 'es',
                        theme: 'dark',
                        notifications: true
                    }
                };
            }

            // Get info from license manager if available
            if (window.licenseManager) {
                const licenseStatus = window.licenseManager.getLicenseStatus();
                if (licenseStatus.license) {
                    userInfo.email = licenseStatus.license.email || userInfo.email;
                    userInfo.accountType = this.formatLicenseType(licenseStatus.license.type);
                }
            }

            // Get theme from theme manager
            if (window.themeManager) {
                userInfo.preferences.theme = window.themeManager.getCurrentTheme();
            }

            this.userInfo = userInfo;
            console.log('UI', '[AccountModal] User info loaded:', this.userInfo);
            
        } catch (error) {
            console.error('UI', '[AccountModal] Error loading user info:', error);
            // Set default user info
            this.userInfo = {
                name: 'Usuario',
                email: '<EMAIL>',
                accountType: 'Demo',
                avatar: null,
                preferences: {
                    language: 'es',
                    theme: 'dark',
                    notifications: true
                }
            };
        }
    }

    /**
     * Create the account modal UI
     * @private
     */
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'account-modal-container';
        this.modal.id = 'account-modal';
        this.modal.style.display = 'none';
        
        this.modal.innerHTML = `
            <div class="account-modal">
                <div class="account-modal-header">
                    <h2>Mi Cuenta</h2>
                    <button class="account-modal-close" id="account-modal-close">×</button>
                </div>
                
                <div class="account-modal-body">
                    <!-- User Profile Section -->
                    <div class="account-section">
                        <div class="account-profile">
                            <div class="profile-avatar">
                                <div class="avatar-placeholder">
                                    <span class="avatar-icon">👤</span>
                                </div>
                            </div>
                            <div class="profile-info">
                                <h3 class="profile-name">${this.userInfo.name}</h3>
                                <p class="profile-email">${this.userInfo.email}</p>
                                <span class="account-type-badge">${this.userInfo.accountType}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Settings Section -->
                    <div class="account-section">
                        <h4 class="section-title">Configuración de Cuenta</h4>
                        
                        <div class="account-setting-item">
                            <label for="account-firstName" class="setting-label">Nombre</label>
                            <input type="text" id="account-firstName" class="setting-input" value="${this.userInfo.firstName}" required>
                        </div>
                        
                        <div class="account-setting-item">
                            <label for="account-lastName" class="setting-label">Apellido</label>
                            <input type="text" id="account-lastName" class="setting-input" value="${this.userInfo.lastName}" required>
                        </div>
                        
                        <div class="account-setting-item">
                            <label for="account-email" class="setting-label">Email</label>
                            <input type="email" id="account-email" class="setting-input" value="${this.userInfo.email}" readonly>
                            <small class="setting-help">El email está asociado a tu licencia</small>
                        </div>
                        
                        <div class="account-setting-item">
                            <label for="account-company" class="setting-label">Empresa (Opcional)</label>
                            <input type="text" id="account-company" class="setting-input" value="${this.userInfo.company}">
                        </div>
                        
                        <div class="account-setting-item">
                            <label for="account-phone" class="setting-label">Teléfono (Opcional)</label>
                            <input type="tel" id="account-phone" class="setting-input" value="${this.userInfo.phone}">
                        </div>
                        
                        <div class="account-setting-item">
                            <label for="account-language" class="setting-label">Idioma</label>
                            <select id="account-language" class="setting-input">
                                <option value="es" ${this.userInfo.preferences.language === 'es' ? 'selected' : ''}>Español</option>
                                <option value="en" ${this.userInfo.preferences.language === 'en' ? 'selected' : ''}>English</option>
                                <option value="pt" ${this.userInfo.preferences.language === 'pt' ? 'selected' : ''}>Português</option>
                            </select>
                        </div>
                        
                        <div class="account-setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="account-notifications" ${this.userInfo.preferences.notifications ? 'checked' : ''}>
                                Recibir notificaciones
                            </label>
                        </div>
                    </div>
                    
                    <!-- License Information Section -->
                    <div class="account-section">
                        <h4 class="section-title">Información de Licencia</h4>
                        <div id="license-info-section" class="license-info">
                            <!-- License info will be populated here -->
                        </div>
                    </div>
                    
                    <!-- Quick Actions Section -->
                    <div class="account-section">
                        <h4 class="section-title">Acciones Rápidas</h4>
                        <div class="quick-actions">
                            <button id="change-password-btn" class="action-btn secondary">Cambiar Contraseña</button>
                            <button id="export-data-btn" class="action-btn secondary">Exportar Datos</button>
                            <button id="open-settings-btn" class="action-btn secondary">Abrir Configuración</button>
                            <button id="activate-license-btn" class="action-btn primary">Gestionar Licencia</button>
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="account-modal-footer">
                    <button class="btn btn-secondary" id="account-cancel">Cancelar</button>
                    <button class="btn btn-primary" id="account-save">Guardar Cambios</button>
                </div>
            </div>
        `;
        
        // Add to document
        document.body.appendChild(this.modal);
        
        // Populate license information
        this.updateLicenseInfo();
    }
    
    /**
     * Setup event listeners
     * @private
     */
    setupEventListeners() {
        // Close modal
        const closeBtn = this.modal.querySelector('#account-modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Account', '[AccountModal] Close button clicked');
                this.hide();
            });
        } else {
            console.warn('Account', '[AccountModal] Close button not found');
        }
        
        // Cancel button
        const cancelBtn = this.modal.querySelector('#account-cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Account', '[AccountModal] Cancel button clicked');
                this.hide();
            });
        } else {
            console.warn('Account', '[AccountModal] Cancel button not found');
        }
        
        // Save button
        this.modal.querySelector('#account-save').addEventListener('click', () => {
            this.saveChanges();
        });
        
        // Quick action buttons
        this.modal.querySelector('#change-password-btn').addEventListener('click', () => {
            this.showChangePasswordDialog();
        });
        
        this.modal.querySelector('#export-data-btn').addEventListener('click', () => {
            this.exportUserData();
        });
        
        this.modal.querySelector('#open-settings-btn').addEventListener('click', () => {
            this.hide();
            if (window.configurationPanel) {
                window.configurationPanel.show();
            }
        });
        
        this.modal.querySelector('#activate-license-btn').addEventListener('click', () => {
            this.hide();
            if (window.licenseActivationModal) {
                window.licenseActivationModal.show('license');
            }
        });
        
        // Close on background click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });
        
        // Listen for setting changes
        this.modal.addEventListener('change', (e) => {
            if (e.target.matches('[data-setting]') || e.target.id.startsWith('account-')) {
                this.markChanges();
            }
        });
    }
    
    /**
     * Update license information display
     * @private
     */
    updateLicenseInfo() {
        const licenseSection = this.modal.querySelector('#license-info-section');
        if (!licenseSection) return;
        
        let licenseHTML = '';
        
        if (window.licenseManager) {
            const licenseStatus = window.licenseManager.getLicenseStatus();
            
            if (licenseStatus.isActivated && licenseStatus.license) {
                const license = licenseStatus.license;
                licenseHTML = `
                    <div class="license-details">
                        <div class="license-item">
                            <span class="license-label">Estado:</span>
                            <span class="license-value active">✓ Activa</span>
                        </div>
                        <div class="license-item">
                            <span class="license-label">Tipo:</span>
                            <span class="license-value">${this.formatLicenseType(license.type)}</span>
                        </div>
                        <div class="license-item">
                            <span class="license-label">Clave:</span>
                            <span class="license-value">${license.key}</span>
                        </div>
                        ${license.expiresAt ? `
                            <div class="license-item">
                                <span class="license-label">Vence:</span>
                                <span class="license-value">${new Date(license.expiresAt).toLocaleDateString()}</span>
                            </div>
                        ` : ''}
                    </div>
                `;
            } else {
                licenseHTML = `
                    <div class="license-details">
                        <div class="license-item">
                            <span class="license-label">Estado:</span>
                            <span class="license-value inactive">⚠ Sin Licencia</span>
                        </div>
                        <p class="license-message">No tienes una licencia activa. Activa tu licencia para acceder a todas las funcionalidades.</p>
                    </div>
                `;
            }
        } else {
            licenseHTML = `
                <div class="license-details">
                    <p class="license-message">Información de licencia no disponible.</p>
                </div>
            `;
        }
        
        licenseSection.innerHTML = licenseHTML;
    }
    
    /**
     * Save account changes
     * @private
     */
    async saveChanges() {
        try {
            console.log('UI', '[AccountModal] Saving account changes...', );
            
            // Collect form data
            const firstName = this.modal.querySelector('#account-firstName').value.trim();
            const lastName = this.modal.querySelector('#account-lastName').value.trim();
            const company = this.modal.querySelector('#account-company').value.trim();
            const phone = this.modal.querySelector('#account-phone').value.trim();
            const language = this.modal.querySelector('#account-language').value;
            const notifications = this.modal.querySelector('#account-notifications').checked;
            
            // Validate required fields
            if (!firstName || !lastName) {
                this.showNotification('Nombre y apellido son requeridos', 'error');
                return;
            }
            
            // Prepare update data for backend API
            const updateData = {
                firstName,
                lastName,
                ...(company && { company }),
                ...(phone && { phone })
            };
            
            // Try to save to backend via API (with better error handling)
            let backendSaveSuccess = false;
            let backendError = null;
            
            try {
                if (window.authApiService && window.authApiService.updateProfile) {
                    console.log('AccountModal: Attempting backend profile update...');
                    const result = await window.authApiService.updateProfile(updateData);
                    
                    if (!result.success) {
                        backendError = result.error || 'Unknown backend error';
                        console.warn('AccountModal: Backend update failed:', result.error);
                        
                        // Check if it's an authentication error
                        if (result.code === 'AUTH_NO_TOKEN' || result.code === 'AUTH_INVALID_TOKEN') {
                            this.showNotification('Sesión expirada. Los cambios se guardaron localmente.', 'warning');
                        } else {
                            this.showNotification(`Error del servidor: ${result.error}. Cambios guardados localmente.`, 'warning');
                        }
                    } else {
                        backendSaveSuccess = true;
                        console.log('AccountModal: Backend update successful');
                    }
                } else {
                    backendError = 'AuthApiService not available';
                    console.warn('AccountModal: AuthApiService not available');
                }
            } catch (apiError) {
                backendError = apiError.message;
                console.warn('AccountModal: Backend API error:', apiError.message);
                this.showNotification('Error de conexión. Los cambios se guardaron localmente.', 'warning');
            }
            
            // Update local user info
            this.userInfo.firstName = firstName;
            this.userInfo.lastName = lastName;
            this.userInfo.name = `${firstName} ${lastName}`;
            this.userInfo.company = company;
            this.userInfo.phone = phone;
            this.userInfo.preferences.language = language;
            this.userInfo.preferences.notifications = notifications;
            
            // Update localStorage user data
            const currentUser = window.CoreDeskAuth?.utils?.getCurrentUser();
            if (currentUser) {
                const updatedUser = {
                    ...currentUser,
                    firstName,
                    lastName,
                    name: `${firstName} ${lastName}`,
                    company,
                    phone,
                    language,
                    notifications
                };
                localStorage.setItem('coredesk_user', JSON.stringify(updatedUser));
            }
            
            // Save preferences separately
            localStorage.setItem('coredesk_user_preferences', JSON.stringify(this.userInfo.preferences));
            
            // Apply language change if needed
            if (window.I18n && window.I18n.setLanguage) {
                window.I18n.setLanguage(language);
                window.I18n.applyTranslations();
            }
            
            // Update title bar display
            if (window.titleBar && window.titleBar.updateAccountButton) {
                window.titleBar.updateAccountButton();
            }
            
            // Show success message based on backend status
            if (backendSaveSuccess) {
                this.showNotification('Cambios guardados correctamente en el servidor', 'success');
            } else {
                this.showNotification('Cambios guardados localmente (sin conexión al servidor)', 'info');
            }
            
            // Hide modal after short delay
            setTimeout(() => {
                this.hide();
            }, 1500);
            
            console.log('UI', '[AccountModal] Account changes saved successfully', );
            
        } catch (error) {
            console.error('UI', '[AccountModal] Error saving changes:', error);
            this.showNotification('Error al guardar cambios: ' + error.message, 'error');
        }
    }
    
    /**
     * Mark that there are unsaved changes
     * @private
     */
    markChanges() {
        const saveButton = this.modal.querySelector('#account-save');
        if (saveButton) {
            saveButton.textContent = 'Guardar Cambios *';
            saveButton.disabled = false;
        }
    }
    
    /**
     * Show change password dialog
     * @private
     */
    showChangePasswordDialog() {
        // This would show a password change dialog
        this.showNotification('Cambio de contraseña próximamente disponible', 'info');
    }
    
    /**
     * Export user data
     * @private
     */
    exportUserData() {
        try {
            const userData = {
                account: this.userInfo,
                exportedAt: new Date().toISOString(),
                version: 'v2.0.0'
            };
            
            const blob = new Blob([JSON.stringify(userData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `coredesk-account-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            
            this.showNotification('Datos exportados correctamente', 'success');
            
        } catch (error) {
            console.error('UI', '[AccountModal] Error exporting data:', error);
            this.showNotification('Error al exportar datos', 'error');
        }
    }
    
    /**
     * Format license type for display
     * @param {string} type - License type
     * @returns {string} Formatted type
     * @private
     */
    formatLicenseType(type) {
        const types = {
            trial: 'Demo Gratuita',
            standard: 'Estándar',
            professional: 'Profesional',
            enterprise: 'Empresarial'
        };
        
        return types[type] || 'Desconocida';
    }
    
    /**
     * Show account modal
     */
    show() {
        // Refresh user info and license data
        this.loadUserInfo().then(() => {
            this.updateLicenseInfo();
        });
        
        this.modal.style.display = 'flex';
        this.isVisible = true;
        
        // Focus on modal
        this.modal.focus();
        
        console.log('UI', '[AccountModal] Modal shown', );
    }
    
    /**
     * Hide account modal
     */
    hide() {
        this.modal.style.display = 'none';
        this.isVisible = false;
        
        console.log('UI', '[AccountModal] Modal hidden', );
    }
    
    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type
     */
    showNotification(message, type = 'info') {
        // Simple implementation - could be enhanced with proper notification system
        const notification = document.createElement('div');
        notification.className = `account-notification account-notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
            color: white;
            border-radius: 4px;
            z-index: 10001;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 3000);
    }
    
    /**
     * Get account modal status
     * @returns {Object} Modal status
     */
    getStatus() {
        return {
            isVisible: this.isVisible,
            userInfo: this.userInfo
        };
    }
}

// Create global instance
window.accountModal = new AccountModal();

console.log('UI', '[AccountModal] Class defined and global instance created successfully', );