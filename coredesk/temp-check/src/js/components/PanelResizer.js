/**
 * PanelResizer.js
 * Handles resizing functionality for CoreDesk panels (left, right, bottom)
 */

class PanelResizer {
    constructor() {
        this.isDragging = false;
        this.currentResizer = null;
        this.startX = 0;
        this.startY = 0;
        this.startWidth = 0;
        this.startHeight = 0;
        this.minPanelSize = 200;
        this.maxPanelSize = 800;
        
        this.initialize();
    }

    /**
     * Initialize panel resizer
     */
    initialize() {
        console.log('PanelResizer', 'Initializing panel resizer...');
        
        // Add resize handles to panels
        this.addResizeHandles();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('PanelResizer', 'Panel resizer initialized successfully');
    }

    /**
     * Add resize handles to all panels
     */
    addResizeHandles() {
        // Left panel resize handle (right edge)
        const leftPanel = document.getElementById('left-panel');
        if (leftPanel) {
            const leftHandle = this.createResizeHandle('horizontal', 'left');
            leftPanel.appendChild(leftHandle);
        }

        // Right panel resize handle (left edge)
        const rightPanel = document.getElementById('right-panel');
        if (rightPanel) {
            const rightHandle = this.createResizeHandle('horizontal', 'right');
            rightPanel.appendChild(rightHandle);
        }

        // Bottom panel resize handle (top edge)
        const bottomPanel = document.getElementById('bottom-panel');
        if (bottomPanel) {
            const bottomHandle = this.createResizeHandle('vertical', 'bottom');
            bottomPanel.appendChild(bottomHandle);
        }
    }

    /**
     * Create a resize handle element
     * @param {string} direction - 'horizontal' or 'vertical'
     * @param {string} panel - 'left', 'right', or 'bottom'
     * @returns {HTMLElement} The resize handle element
     */
    createResizeHandle(direction, panel) {
        const handle = document.createElement('div');
        handle.className = `panel-resize-handle ${direction}`;
        handle.dataset.panel = panel;
        handle.dataset.direction = direction;
        
        // Add visual indicator on hover
        handle.style.transition = 'background var(--transition-fast)';
        
        return handle;
    }

    /**
     * Setup event listeners for resizing
     */
    setupEventListeners() {
        // Mouse events for drag start
        document.addEventListener('mousedown', this.handleMouseDown.bind(this));
        
        // Global mouse events for dragging
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
        
        // Prevent text selection during drag
        document.addEventListener('selectstart', this.handleSelectStart.bind(this));
        
        // Handle window resize to ensure panels stay within bounds
        window.addEventListener('resize', this.handleWindowResize.bind(this));
    }

    /**
     * Handle mouse down on resize handles
     * @param {MouseEvent} event
     */
    handleMouseDown(event) {
        const resizeHandle = event.target.closest('.panel-resize-handle');
        if (!resizeHandle) return;

        event.preventDefault();
        
        this.isDragging = true;
        this.currentResizer = resizeHandle;
        this.startX = event.clientX;
        this.startY = event.clientY;

        const panel = resizeHandle.closest('.side-panel, .bottom-panel');
        if (!panel) return;

        // Store initial dimensions
        const rect = panel.getBoundingClientRect();
        this.startWidth = rect.width;
        this.startHeight = rect.height;

        // Add dragging class for cursor and visual feedback
        document.body.classList.add('panel-resizing');
        document.body.style.cursor = resizeHandle.dataset.direction === 'horizontal' ? 'ew-resize' : 'ns-resize';
        
        // Prevent pointer events on other elements during resize
        document.body.style.userSelect = 'none';
        document.body.style.pointerEvents = 'none';
        resizeHandle.style.pointerEvents = 'auto';

        console.log('PanelResizer', `Started resizing ${resizeHandle.dataset.panel} panel`);
    }

    /**
     * Handle mouse move during dragging
     * @param {MouseEvent} event
     */
    handleMouseMove(event) {
        if (!this.isDragging || !this.currentResizer) return;

        event.preventDefault();

        const panelType = this.currentResizer.dataset.panel;
        const direction = this.currentResizer.dataset.direction;
        const panel = this.currentResizer.closest('.side-panel, .bottom-panel');
        
        if (!panel) return;

        if (direction === 'horizontal') {
            this.resizeHorizontalPanel(panel, panelType, event);
        } else if (direction === 'vertical') {
            this.resizeVerticalPanel(panel, panelType, event);
        }
    }

    /**
     * Resize horizontal panels (left/right)
     * @param {HTMLElement} panel
     * @param {string} panelType
     * @param {MouseEvent} event
     */
    resizeHorizontalPanel(panel, panelType, event) {
        const deltaX = event.clientX - this.startX;
        let newWidth;

        if (panelType === 'left') {
            newWidth = this.startWidth + deltaX;
        } else if (panelType === 'right') {
            newWidth = this.startWidth - deltaX;
        }

        // Apply constraints
        newWidth = Math.max(this.minPanelSize, Math.min(this.maxPanelSize, newWidth));

        // Update panel width
        panel.style.width = `${newWidth}px`;
        panel.style.minWidth = `${newWidth}px`;

        // Update CSS custom property for consistency
        if (panelType === 'left') {
            document.documentElement.style.setProperty('--left-panel-width', `${newWidth}px`);
        } else if (panelType === 'right') {
            document.documentElement.style.setProperty('--right-panel-width', `${newWidth}px`);
        }

        // Trigger layout update event
        this.triggerPanelResizeEvent(panelType, { width: newWidth });
    }

    /**
     * Resize vertical panels (bottom)
     * @param {HTMLElement} panel
     * @param {string} panelType
     * @param {MouseEvent} event
     */
    resizeVerticalPanel(panel, panelType, event) {
        const deltaY = event.clientY - this.startY;
        let newHeight;

        if (panelType === 'bottom') {
            newHeight = this.startHeight - deltaY;
        }

        // Apply constraints
        newHeight = Math.max(this.minPanelSize, Math.min(this.maxPanelSize, newHeight));

        // Update panel height
        panel.style.height = `${newHeight}px`;

        // Update CSS custom property for consistency
        document.documentElement.style.setProperty('--bottom-panel-height', `${newHeight}px`);

        // Trigger layout update event
        this.triggerPanelResizeEvent(panelType, { height: newHeight });
    }

    /**
     * Handle mouse up to end dragging
     * @param {MouseEvent} event
     */
    handleMouseUp(event) {
        if (!this.isDragging) return;

        event.preventDefault();

        // Reset dragging state
        this.isDragging = false;
        this.currentResizer = null;

        // Remove dragging classes and styles
        document.body.classList.remove('panel-resizing');
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
        document.body.style.pointerEvents = '';

        // Save current panel sizes to localStorage
        this.savePanelSizes();

        console.log('PanelResizer', 'Finished resizing panel');
    }

    /**
     * Handle select start to prevent text selection during drag
     * @param {Event} event
     */
    handleSelectStart(event) {
        if (this.isDragging) {
            event.preventDefault();
        }
    }

    /**
     * Handle window resize to ensure panels stay within bounds
     */
    handleWindowResize() {
        // Check if any panels exceed the new window size and adjust if necessary
        this.constrainPanelSizes();
    }

    /**
     * Constrain panel sizes to window dimensions
     */
    constrainPanelSizes() {
        const leftPanel = document.getElementById('left-panel');
        const rightPanel = document.getElementById('right-panel');
        const bottomPanel = document.getElementById('bottom-panel');

        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        if (leftPanel && !leftPanel.classList.contains('hidden')) {
            const currentWidth = leftPanel.getBoundingClientRect().width;
            const maxWidth = Math.min(windowWidth * 0.4, this.maxPanelSize);
            if (currentWidth > maxWidth) {
                leftPanel.style.width = `${maxWidth}px`;
                leftPanel.style.minWidth = `${maxWidth}px`;
            }
        }

        if (rightPanel && !rightPanel.classList.contains('hidden')) {
            const currentWidth = rightPanel.getBoundingClientRect().width;
            const maxWidth = Math.min(windowWidth * 0.4, this.maxPanelSize);
            if (currentWidth > maxWidth) {
                rightPanel.style.width = `${maxWidth}px`;
                rightPanel.style.minWidth = `${maxWidth}px`;
            }
        }

        if (bottomPanel && !bottomPanel.classList.contains('hidden')) {
            const currentHeight = bottomPanel.getBoundingClientRect().height;
            const maxHeight = Math.min(windowHeight * 0.4, this.maxPanelSize);
            if (currentHeight > maxHeight) {
                bottomPanel.style.height = `${maxHeight}px`;
            }
        }
    }

    /**
     * Trigger panel resize event for other components to listen to
     * @param {string} panelType
     * @param {Object} dimensions
     */
    triggerPanelResizeEvent(panelType, dimensions) {
        window.dispatchEvent(new CustomEvent('panel:resized', {
            detail: {
                panel: panelType,
                dimensions: dimensions
            }
        }));
    }

    /**
     * Save current panel sizes to localStorage with per-content-type support
     */
    savePanelSizes() {
        const sizes = this.getSavedPanelSizes() || {};

        const leftPanel = document.getElementById('left-panel');
        const rightPanel = document.getElementById('right-panel');
        const bottomPanel = document.getElementById('bottom-panel');

        // Get current content types from panel manager
        const panelManager = window.panelManager;
        const currentLeftContent = panelManager?.panels?.left?.currentContent || 'explorer';
        const currentRightContent = panelManager?.panels?.right?.currentContent || 'default';
        const currentBottomContent = panelManager?.panels?.bottom?.currentContent || 'terminal';

        // Initialize content-specific size objects if they don't exist
        if (!sizes.leftContent) sizes.leftContent = {};
        if (!sizes.rightContent) sizes.rightContent = {};
        if (!sizes.bottomContent) sizes.bottomContent = {};

        // Save current sizes for active content types
        if (leftPanel && !leftPanel.classList.contains('hidden')) {
            sizes.leftContent[currentLeftContent] = leftPanel.getBoundingClientRect().width;
            sizes.left = leftPanel.getBoundingClientRect().width; // Keep legacy for fallback
        }

        if (rightPanel && !rightPanel.classList.contains('hidden')) {
            sizes.rightContent[currentRightContent] = rightPanel.getBoundingClientRect().width;
            sizes.right = rightPanel.getBoundingClientRect().width; // Keep legacy for fallback
        }

        if (bottomPanel && !bottomPanel.classList.contains('hidden')) {
            sizes.bottomContent[currentBottomContent] = bottomPanel.getBoundingClientRect().height;
            sizes.bottom = bottomPanel.getBoundingClientRect().height; // Keep legacy for fallback
        }

        localStorage.setItem('coredesk_panel_sizes', JSON.stringify(sizes));
        console.log('PanelResizer', 'Panel sizes saved:', {
            leftContent: currentLeftContent,
            rightContent: currentRightContent,
            bottomContent: currentBottomContent,
            sizes: sizes
        });
    }

    /**
     * Load and apply saved panel sizes based on current content type
     */
    loadPanelSizes() {
        try {
            const sizes = this.getSavedPanelSizes();
            if (!sizes) return;

            // Get current content types from panel manager
            const panelManager = window.panelManager;
            const currentLeftContent = panelManager?.panels?.left?.currentContent || 'explorer';
            const currentRightContent = panelManager?.panels?.right?.currentContent || 'default';
            const currentBottomContent = panelManager?.panels?.bottom?.currentContent || 'terminal';

            // Load content-specific sizes or fall back to legacy sizes
            const leftSize = sizes.leftContent?.[currentLeftContent] || sizes.left || this.getDefaultSizeForContent('left', currentLeftContent);
            const rightSize = sizes.rightContent?.[currentRightContent] || sizes.right || this.getDefaultSizeForContent('right', currentRightContent);
            const bottomSize = sizes.bottomContent?.[currentBottomContent] || sizes.bottom || this.getDefaultSizeForContent('bottom', currentBottomContent);

            // Apply sizes
            if (leftSize) {
                const leftPanel = document.getElementById('left-panel');
                if (leftPanel && !leftPanel.classList.contains('hidden')) {
                    leftPanel.style.width = `${leftSize}px`;
                    leftPanel.style.minWidth = `${leftSize}px`;
                }
            }

            if (rightSize) {
                const rightPanel = document.getElementById('right-panel');
                if (rightPanel && !rightPanel.classList.contains('hidden')) {
                    rightPanel.style.width = `${rightSize}px`;
                    rightPanel.style.minWidth = `${rightSize}px`;
                }
            }

            if (bottomSize) {
                const bottomPanel = document.getElementById('bottom-panel');
                if (bottomPanel && !bottomPanel.classList.contains('hidden')) {
                    bottomPanel.style.height = `${bottomSize}px`;
                }
            }

            console.log('PanelResizer', 'Panel sizes loaded for content types:', {
                left: `${currentLeftContent}: ${leftSize}px`,
                right: `${currentRightContent}: ${rightSize}px`,
                bottom: `${currentBottomContent}: ${bottomSize}px`
            });
        } catch (error) {
            console.warn('PanelResizer', 'Error loading panel sizes:', error);
        }
    }

    /**
     * Get saved panel sizes from localStorage
     * @returns {Object|null}
     */
    getSavedPanelSizes() {
        try {
            const savedSizes = localStorage.getItem('coredesk_panel_sizes');
            return savedSizes ? JSON.parse(savedSizes) : null;
        } catch (error) {
            console.warn('PanelResizer', 'Error parsing saved panel sizes:', error);
            return null;
        }
    }

    /**
     * Get default size for specific content type
     * @param {string} panelType - 'left', 'right', or 'bottom'
     * @param {string} contentType - content type like 'explorer', 'cloud', etc.
     * @returns {number}
     */
    getDefaultSizeForContent(panelType, contentType) {
        const defaults = {
            left: {
                explorer: 400,     // Explorer needs more space for file tree
                cloud: 280,        // Cloud panel is more compact
                search: 300,       // Search results need medium space
                modules: 320,      // Module list needs medium space
                extensions: 280    // Extensions list is compact
            },
            right: {
                default: 300,
                properties: 250,
                outline: 280,
                timeline: 320
            },
            bottom: {
                terminal: 250,
                console: 200,
                output: 180,
                debug: 300
            }
        };

        return defaults[panelType]?.[contentType] || (panelType === 'bottom' ? 200 : 300);
    }

    /**
     * Load panel size for specific content type (called when switching panels)
     * @param {string} panelType - 'left', 'right', or 'bottom'
     * @param {string} contentType - content type like 'explorer', 'cloud', etc.
     */
    loadPanelSizeForContent(panelType, contentType) {
        try {
            const sizes = this.getSavedPanelSizes();
            const contentKey = `${panelType}Content`;
            const savedSize = sizes?.[contentKey]?.[contentType];
            const defaultSize = this.getDefaultSizeForContent(panelType, contentType);
            const targetSize = savedSize || defaultSize;

            const panel = document.getElementById(`${panelType}-panel`);
            if (panel && !panel.classList.contains('hidden')) {
                if (panelType === 'bottom') {
                    panel.style.height = `${targetSize}px`;
                } else {
                    panel.style.width = `${targetSize}px`;
                    panel.style.minWidth = `${targetSize}px`;
                }

                console.log('PanelResizer', `Loaded size for ${panelType} panel (${contentType}):`, `${targetSize}px`);
            }
        } catch (error) {
            console.warn('PanelResizer', `Error loading size for ${panelType} panel (${contentType}):`, error);
        }
    }

    /**
     * Set minimum panel size
     * @param {number} size
     */
    setMinPanelSize(size) {
        this.minPanelSize = Math.max(100, size);
    }

    /**
     * Set maximum panel size
     * @param {number} size
     */
    setMaxPanelSize(size) {
        this.maxPanelSize = Math.max(this.minPanelSize, size);
    }

    /**
     * Get current status of panel resizer
     * @returns {Object}
     */
    getStatus() {
        return {
            isDragging: this.isDragging,
            minPanelSize: this.minPanelSize,
            maxPanelSize: this.maxPanelSize,
            currentResizer: this.currentResizer?.dataset.panel || null
        };
    }
}

// Create global instance and load saved sizes
window.panelResizer = new PanelResizer();

// Load saved panel sizes after initialization
setTimeout(() => {
    window.panelResizer.loadPanelSizes();
}, 100);

console.log('PanelResizer', 'Panel resizer component loaded successfully');