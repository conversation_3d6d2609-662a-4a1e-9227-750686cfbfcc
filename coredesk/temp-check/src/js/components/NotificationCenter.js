/**
 * Notification Center Component
 * Handles in-app notifications, toasts, and notification history
 */

class NotificationCenter {
    constructor() {
        this.notifications = [];
        this.toasts = [];
        this.settings = {
            showToasts: true,
            playSound: true,
            maxToasts: 5,
            toastDuration: 5000,
            categories: {
                system: true,
                modules: true,
                sync: true,
                security: true,
                updates: true
            }
        };
        
        this.init();
    }
    
    init() {
        // Load notifications from localStorage
        this.loadNotifications();
        this.loadSettings();
        
        // Create notification container if it doesn't exist
        this.createNotificationContainer();
        
        // Add welcome notification if no notifications exist
        if (this.notifications.length === 0) {
            this.addNotification({
                title: 'Bienvenido a CoreDesk Framework',
                message: 'Tu plataforma de agentes IA está lista para usar.',
                type: 'info',
                category: 'system',
                persistent: true
            });
        }
    }
    
    /**
     * Create notification containers in DOM
     */
    createNotificationContainer() {
        // Toast container
        if (!document.getElementById('toast-container')) {
            const toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }
        
        // Notification center overlay
        if (!document.getElementById('notification-center-overlay')) {
            const overlay = document.createElement('div');
            overlay.id = 'notification-center-overlay';
            overlay.className = 'notification-center-overlay';
            overlay.style.display = 'none';
            overlay.innerHTML = this.renderNotificationCenter();
            document.body.appendChild(overlay);
            
            // Add event listeners
            this.setupNotificationCenterListeners();
        }
    }
    
    /**
     * Setup event listeners for notification center
     */
    setupNotificationCenterListeners() {
        const overlay = document.getElementById('notification-center-overlay');
        if (!overlay) return;
        
        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.closeNotificationCenter();
            }
        });
        
        // Close button
        overlay.addEventListener('click', (e) => {
            if (e.target.classList.contains('close-notification-center')) {
                this.closeNotificationCenter();
            }
        });
        
        // Mark all as read
        overlay.addEventListener('click', (e) => {
            if (e.target.classList.contains('mark-all-read-btn')) {
                this.markAllAsRead();
            }
        });
        
        // Clear all notifications
        overlay.addEventListener('click', (e) => {
            if (e.target.classList.contains('clear-all-btn')) {
                this.clearAllNotifications();
            }
        });
        
        // Individual notification actions
        overlay.addEventListener('click', (e) => {
            if (e.target.classList.contains('notification-close')) {
                const notificationId = e.target.dataset.notificationId;
                this.removeNotification(notificationId);
            }
            
            if (e.target.classList.contains('notification-item')) {
                const notificationId = e.target.dataset.notificationId;
                this.markAsRead(notificationId);
            }
        });
        
        // Settings toggle
        overlay.addEventListener('change', (e) => {
            if (e.target.classList.contains('notification-setting')) {
                const setting = e.target.dataset.setting;
                this.updateSetting(setting, e.target.checked);
            }
        });
    }
    
    /**
     * Add a new notification
     */
    addNotification(options) {
        const notification = {
            id: this.generateId(),
            title: options.title,
            message: options.message,
            type: options.type || 'info', // info, success, warning, error
            category: options.category || 'system',
            timestamp: new Date(),
            read: false,
            persistent: options.persistent || false,
            actions: options.actions || [],
            data: options.data || {}
        };
        
        // Add to notifications list
        this.notifications.unshift(notification);
        
        // Keep only last 100 notifications
        if (this.notifications.length > 100) {
            this.notifications = this.notifications.slice(0, 100);
        }
        
        // Save to localStorage
        this.saveNotifications();
        
        // Show toast if enabled
        if (this.settings.showToasts && this.shouldShowToast(notification)) {
            this.showToast(notification);
        }
        
        // Play sound if enabled
        if (this.settings.playSound) {
            this.playNotificationSound(notification.type);
        }
        
        // Update notification center if open
        this.updateNotificationCenter();
        
        // Update notification badge
        this.updateNotificationBadge();
        
        return notification.id;
    }
    
    /**
     * Show toast notification (convenience method with string parameters)
     */
    showToast(titleOrNotification, message = null, type = 'info', options = {}) {
        // Handle both object and string parameters
        let notification;
        
        if (typeof titleOrNotification === 'string') {
            // Called with string parameters: showToast(title, message, type, options)
            notification = {
                id: this.generateId(),
                title: titleOrNotification,
                message: message || '',
                type: type,
                timestamp: new Date(),
                actions: options.actions || [],
                ...options
            };
        } else {
            // Called with notification object: showToast(notification)
            notification = titleOrNotification;
            // Ensure actions property exists
            if (!notification.actions) {
                notification.actions = [];
            }
        }
        
        return this.showToastInternal(notification);
    }

    /**
     * Internal show toast notification method
     */
    showToastInternal(notification) {
        // Don't show toast if we already have max toasts
        if (this.toasts.length >= this.settings.maxToasts) {
            return;
        }
        
        const toast = {
            id: notification.id,
            element: this.createToastElement(notification)
        };
        
        // Add to toasts array
        this.toasts.push(toast);
        
        // Add to DOM
        const container = document.getElementById('toast-container');
        if (container) {
            container.appendChild(toast.element);
        }
        
        // Auto-remove after duration (unless persistent)
        if (!notification.persistent) {
            setTimeout(() => {
                this.removeToast(toast.id);
            }, this.settings.toastDuration);
        }
    }
    
    /**
     * Create toast element
     */
    createToastElement(notification) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${notification.type}`;
        toast.dataset.notificationId = notification.id;
        
        toast.innerHTML = `
            <div class="toast-icon">${this.getNotificationIcon(notification.type)}</div>
            <div class="toast-content">
                <div class="toast-title">${notification.title}</div>
                <div class="toast-message">${notification.message}</div>
            </div>
            <button class="toast-close" onclick="notificationCenter.removeToast('${notification.id}')">×</button>
        `;
        
        // Add click handler for actions
        if (notification.actions && notification.actions.length > 0) {
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'toast-actions';
            
            notification.actions.forEach(action => {
                const button = document.createElement('button');
                button.className = 'toast-action';
                button.textContent = action.label;
                button.onclick = () => {
                    if (action.callback) action.callback();
                    this.removeToast(notification.id);
                };
                actionsContainer.appendChild(button);
            });
            
            toast.appendChild(actionsContainer);
        }
        
        // Animate in
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        return toast;
    }
    
    /**
     * Remove toast
     */
    removeToast(toastId) {
        const toastIndex = this.toasts.findIndex(t => t.id === toastId);
        if (toastIndex === -1) return;
        
        const toast = this.toasts[toastIndex];
        
        // Animate out
        toast.element.classList.add('hiding');
        
        setTimeout(() => {
            // Remove from DOM
            if (toast.element.parentNode) {
                toast.element.parentNode.removeChild(toast.element);
            }
            
            // Remove from array
            this.toasts.splice(toastIndex, 1);
        }, 300);
    }
    
    /**
     * Show notification center
     */
    showNotificationCenter() {
        const overlay = document.getElementById('notification-center-overlay');
        if (overlay) {
            overlay.innerHTML = this.renderNotificationCenter();
            overlay.style.display = 'flex';
            this.setupNotificationCenterListeners();
        }
    }
    
    /**
     * Close notification center
     */
    closeNotificationCenter() {
        const overlay = document.getElementById('notification-center-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
    
    /**
     * Render notification center
     */
    renderNotificationCenter() {
        const unreadCount = this.getUnreadCount();
        const categories = this.getNotificationsByCategory();
        
        return `
            <div class="notification-center">
                <div class="notification-header">
                    <h3>Centro de Notificaciones</h3>
                    <div class="notification-header-actions">
                        ${unreadCount > 0 ? `
                            <button class="btn-link mark-all-read-btn">
                                Marcar todo como leído (${unreadCount})
                            </button>
                        ` : ''}
                        <button class="btn-link clear-all-btn">Limpiar todo</button>
                        <button class="close-notification-center">×</button>
                    </div>
                </div>
                
                <div class="notification-filters">
                    <button class="filter-btn active" data-category="all">
                        Todas (${this.notifications.length})
                    </button>
                    ${Object.entries(categories).map(([category, notifications]) => `
                        <button class="filter-btn" data-category="${category}">
                            ${this.getCategoryLabel(category)} (${notifications.length})
                        </button>
                    `).join('')}
                </div>
                
                <div class="notification-list">
                    ${this.notifications.length === 0 ? this.renderEmptyNotifications() : 
                      this.notifications.map(notification => this.renderNotificationItem(notification)).join('')}
                </div>
                
                <div class="notification-settings">
                    <h4>Configuración</h4>
                    <div class="settings-grid">
                        <label class="setting-item">
                            <input type="checkbox" class="notification-setting" 
                                   data-setting="showToasts" ${this.settings.showToasts ? 'checked' : ''}>
                            <span>Mostrar notificaciones emergentes</span>
                        </label>
                        <label class="setting-item">
                            <input type="checkbox" class="notification-setting" 
                                   data-setting="playSound" ${this.settings.playSound ? 'checked' : ''}>
                            <span>Reproducir sonidos</span>
                        </label>
                        ${Object.entries(this.settings.categories).map(([category, enabled]) => `
                            <label class="setting-item">
                                <input type="checkbox" class="notification-setting" 
                                       data-setting="categories.${category}" ${enabled ? 'checked' : ''}>
                                <span>${this.getCategoryLabel(category)}</span>
                            </label>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Render individual notification item
     */
    renderNotificationItem(notification) {
        return `
            <div class="notification-item ${notification.read ? 'read' : 'unread'}" 
                 data-notification-id="${notification.id}">
                <div class="notification-icon ${notification.type}">
                    ${this.getNotificationIcon(notification.type)}
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-meta">
                        <span class="notification-category">${this.getCategoryLabel(notification.category)}</span>
                        <span class="notification-time">${this.formatTime(notification.timestamp)}</span>
                    </div>
                </div>
                <div class="notification-actions">
                    ${notification.actions ? notification.actions.map(action => `
                        <button class="notification-action" onclick="${action.callback}">${action.label}</button>
                    `).join('') : ''}
                    <button class="notification-close" data-notification-id="${notification.id}">×</button>
                </div>
            </div>
        `;
    }
    
    /**
     * Render empty notifications state
     */
    renderEmptyNotifications() {
        return `
            <div class="empty-notifications">
                <div class="empty-icon">🔔</div>
                <h4>No hay notificaciones</h4>
                <p>Todas las notificaciones aparecerán aquí</p>
            </div>
        `;
    }
    
    /**
     * Get notification icon based on type
     */
    getNotificationIcon(type) {
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            system: '🔧',
            sync: '🔄',
            security: '🔒',
            update: '🆙'
        };
        
        return icons[type] || icons.info;
    }
    
    /**
     * Get category label
     */
    getCategoryLabel(category) {
        const labels = {
            system: 'Sistema',
            modules: 'Módulos',
            sync: 'Sincronización',
            security: 'Seguridad',
            updates: 'Actualizaciones'
        };
        
        return labels[category] || category;
    }
    
    /**
     * Get notifications grouped by category
     */
    getNotificationsByCategory() {
        const categories = {};
        
        this.notifications.forEach(notification => {
            if (!categories[notification.category]) {
                categories[notification.category] = [];
            }
            categories[notification.category].push(notification);
        });
        
        return categories;
    }
    
    /**
     * Get unread notifications count
     */
    getUnreadCount() {
        return this.notifications.filter(n => !n.read).length;
    }
    
    /**
     * Mark notification as read
     */
    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && !notification.read) {
            notification.read = true;
            this.saveNotifications();
            this.updateNotificationCenter();
            this.updateNotificationBadge();
        }
    }
    
    /**
     * Mark all notifications as read
     */
    markAllAsRead() {
        this.notifications.forEach(notification => {
            notification.read = true;
        });
        
        this.saveNotifications();
        this.updateNotificationCenter();
        this.updateNotificationBadge();
    }
    
    /**
     * Remove notification
     */
    removeNotification(notificationId) {
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
        this.saveNotifications();
        this.updateNotificationCenter();
        this.updateNotificationBadge();
    }
    
    /**
     * Clear all notifications
     */
    clearAllNotifications() {
        if (confirm('¿Estás seguro de que quieres eliminar todas las notificaciones?')) {
            this.notifications = [];
            this.saveNotifications();
            this.updateNotificationCenter();
            this.updateNotificationBadge();
        }
    }
    
    /**
     * Update notification setting
     */
    updateSetting(settingPath, value) {
        const keys = settingPath.split('.');
        let target = this.settings;
        
        for (let i = 0; i < keys.length - 1; i++) {
            target = target[keys[i]];
        }
        
        target[keys[keys.length - 1]] = value;
        this.saveSettings();
    }
    
    /**
     * Update notification center content
     */
    updateNotificationCenter() {
        const overlay = document.getElementById('notification-center-overlay');
        if (overlay && overlay.style.display !== 'none') {
            overlay.innerHTML = this.renderNotificationCenter();
            this.setupNotificationCenterListeners();
        }
    }
    
    /**
     * Update notification badge in UI
     */
    updateNotificationBadge() {
        const unreadCount = this.getUnreadCount();
        
        // Update any notification badges in the UI
        const badges = document.querySelectorAll('.notification-badge');
        badges.forEach(badge => {
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        });
    }
    
    /**
     * Should show toast for notification
     */
    shouldShowToast(notification) {
        // Check if category is enabled
        if (!this.settings.categories[notification.category]) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Play notification sound
     */
    playNotificationSound(type) {
        // In a real implementation, this would play actual sound files
        // For now, we'll just use the browser's built-in beep
        if (window.AudioContext) {
            try {
                const audioContext = new AudioContext();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                // Different frequencies for different types
                const frequencies = {
                    info: 440,
                    success: 523,
                    warning: 349,
                    error: 220
                };
                
                oscillator.frequency.value = frequencies[type] || frequencies.info;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                
                oscillator.start();
                oscillator.stop(audioContext.currentTime + 0.3);
            } catch (error) {
                console.warn('Could not play notification sound:', error);
            }
        }
    }
    
    /**
     * Format timestamp
     */
    formatTime(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (seconds < 60) return 'Ahora';
        if (minutes < 60) return `Hace ${minutes}m`;
        if (hours < 24) return `Hace ${hours}h`;
        if (days < 7) return `Hace ${days}d`;
        
        return timestamp.toLocaleDateString();
    }
    
    /**
     * Generate unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    /**
     * Save notifications to localStorage
     */
    saveNotifications() {
        try {
            localStorage.setItem('coredesk_notifications', JSON.stringify(this.notifications));
        } catch (error) {
            console.error('Failed to save notifications:', error);
        }
    }
    
    /**
     * Load notifications from localStorage
     */
    loadNotifications() {
        try {
            const saved = localStorage.getItem('coredesk_notifications');
            if (saved) {
                this.notifications = JSON.parse(saved).map(n => ({
                    ...n,
                    timestamp: new Date(n.timestamp)
                }));
            }
        } catch (error) {
            console.error('Failed to load notifications:', error);
            this.notifications = [];
        }
    }
    
    /**
     * Save settings to localStorage
     */
    saveSettings() {
        try {
            localStorage.setItem('coredesk_notification_settings', JSON.stringify(this.settings));
        } catch (error) {
            console.error('Failed to save notification settings:', error);
        }
    }
    
    /**
     * Load settings from localStorage
     */
    loadSettings() {
        try {
            const saved = localStorage.getItem('coredesk_notification_settings');
            if (saved) {
                this.settings = { ...this.settings, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.error('Failed to load notification settings:', error);
        }
    }
}

// Create global instance
const notificationCenter = new NotificationCenter();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = notificationCenter;
}

// Make available globally
if (typeof window !== 'undefined') {
    window.notificationCenter = notificationCenter;
    
    // Global helper function for easy notification creation
    window.showNotification = (title, message, type = 'info', options = {}) => {
        return notificationCenter.addNotification({
            title,
            message,
            type,
            ...options
        });
    };
}