/**
 * Update Dialogs Component
 * Handles update-related dialogs and notifications for CoreDesk
 * 
 * Features:
 * - Update available notifications
 * - Download progress dialogs
 * - Installation confirmation dialogs
 * - Update error handling
 * - Channel switching interface
 */

class UpdateDialogs {
    constructor() {
        this.currentDialog = null;
        this.updateInfo = null;
        this.downloadProgress = 0;
        
        // Dialog templates (will be initialized in init method)
        this.templates = {};
        
        // Initialize
        this.init();
    }

    init() {
        // Initialize dialog templates
        this.initializeTemplates();
        
        // Listen for update events from main process
        this.setupUpdateEventListeners();
        
        // Create dialog containers
        this.createDialogContainers();
        
        console.log('[UpdateDialogs] Initialized successfully');
    }

    /**
     * Initialize dialog templates
     */
    initializeTemplates() {
        // Note: These template methods don't exist yet, but the pattern was trying to call them
        // For now, we'll initialize empty templates and create content dynamically
        this.templates = {
            updateAvailable: null,
            downloadProgress: null,
            installReady: null,
            updateError: null,
            channelSwitch: null
        };
    }

    /**
     * Setup event listeners for update events from main process
     */
    setupUpdateEventListeners() {
        if (window.electronAPI) {
            // Listen for update available
            window.electronAPI.onUpdateAvailable((updateInfo) => {
                this.handleUpdateAvailable(updateInfo);
            });

            // Listen for download progress
            window.electronAPI.onUpdateProgress((progress) => {
                this.handleDownloadProgress(progress);
            });

            // Listen for update downloaded
            window.electronAPI.onUpdateDownloaded((updateInfo) => {
                this.handleUpdateDownloaded(updateInfo);
            });

            // Listen for update errors
            window.electronAPI.onUpdateError((error) => {
                this.handleUpdateError(error);
            });
        }
    }

    /**
     * Create dialog containers in DOM
     */
    createDialogContainers() {
        // Create main dialog overlay
        if (!document.getElementById('update-dialog-overlay')) {
            const overlay = document.createElement('div');
            overlay.id = 'update-dialog-overlay';
            overlay.className = 'update-dialog-overlay hidden';
            document.body.appendChild(overlay);
        }

        // Create notification area for update toasts
        if (!document.getElementById('update-notifications')) {
            const container = document.createElement('div');
            container.id = 'update-notifications';
            container.className = 'update-notifications';
            document.body.appendChild(container);
        }
    }

    /**
     * Handle update available notification
     */
    handleUpdateAvailable(updateInfo) {
        this.updateInfo = updateInfo;
        console.log('[UpdateDialogs] Update available:', updateInfo);
        
        // Show notification toast
        this.showUpdateToast({
            title: 'Actualización Disponible',
            message: `CoreDesk v${updateInfo.version} está disponible`,
            type: 'info',
            actions: [
                {
                    text: 'Ver Detalles',
                    action: () => this.showUpdateAvailableDialog(updateInfo)
                },
                {
                    text: 'Más Tarde',
                    action: () => this.dismissNotification()
                }
            ]
        });

        // Add to notification center
        if (window.notificationCenter) {
            window.notificationCenter.addNotification({
                title: 'Actualización Disponible',
                message: `CoreDesk v${updateInfo.version} está disponible para descargar`,
                type: 'info',
                category: 'updates',
                persistent: true,
                action: () => this.showUpdateAvailableDialog(updateInfo)
            });
        }
    }

    /**
     * Handle download progress updates
     */
    handleDownloadProgress(progress) {
        this.downloadProgress = progress.percent;
        
        if (this.currentDialog && this.currentDialog.type === 'download-progress') {
            this.updateDownloadProgress(progress);
        }
    }

    /**
     * Handle update downloaded notification
     */
    handleUpdateDownloaded(updateInfo) {
        console.log('[UpdateDialogs] Update downloaded:', updateInfo);
        
        // Close download progress dialog if open
        if (this.currentDialog && this.currentDialog.type === 'download-progress') {
            this.closeCurrentDialog();
        }

        // Show install ready dialog
        this.showInstallReadyDialog(updateInfo);
    }

    /**
     * Handle update errors
     */
    handleUpdateError(error) {
        console.error('[UpdateDialogs] Update error:', error);
        
        // Close any open dialogs
        this.closeCurrentDialog();
        
        // Show error dialog
        this.showUpdateErrorDialog(error);
    }

    /**
     * Show update available dialog
     */
    showUpdateAvailableDialog(updateInfo) {
        const dialog = this.createDialog('update-available', {
            title: 'Nueva Versión Disponible',
            content: this.generateUpdateAvailableContent(updateInfo),
            buttons: [
                {
                    text: 'Descargar Ahora',
                    class: 'btn-primary',
                    action: () => this.startDownload()
                },
                {
                    text: 'Ver Changelog',
                    class: 'btn-secondary',
                    action: () => this.showChangelog(updateInfo)
                },
                {
                    text: 'Recordar Más Tarde',
                    class: 'btn-default',
                    action: () => this.closeCurrentDialog()
                }
            ]
        });

        this.showDialog(dialog);
    }

    /**
     * Show download progress dialog
     */
    showDownloadProgressDialog() {
        const dialog = this.createDialog('download-progress', {
            title: 'Descargando Actualización',
            content: this.generateDownloadProgressContent(),
            buttons: [
                {
                    text: 'Cancelar',
                    class: 'btn-secondary',
                    action: () => this.cancelDownload()
                }
            ],
            closable: false
        });

        this.showDialog(dialog);
    }

    /**
     * Show install ready dialog
     */
    showInstallReadyDialog(updateInfo) {
        const dialog = this.createDialog('install-ready', {
            title: 'Actualización Lista para Instalar',
            content: this.generateInstallReadyContent(updateInfo),
            buttons: [
                {
                    text: 'Instalar y Reiniciar',
                    class: 'btn-primary',
                    action: () => this.installUpdate()
                },
                {
                    text: 'Instalar al Cerrar',
                    class: 'btn-secondary',
                    action: () => this.installOnQuit()
                },
                {
                    text: 'Más Tarde',
                    class: 'btn-default',
                    action: () => this.closeCurrentDialog()
                }
            ]
        });

        this.showDialog(dialog);
    }

    /**
     * Show update error dialog
     */
    showUpdateErrorDialog(error) {
        const dialog = this.createDialog('update-error', {
            title: 'Error de Actualización',
            content: this.generateUpdateErrorContent(error),
            buttons: [
                {
                    text: 'Reintentar',
                    class: 'btn-primary',
                    action: () => this.checkForUpdates()
                },
                {
                    text: 'Cerrar',
                    class: 'btn-default',
                    action: () => this.closeCurrentDialog()
                }
            ]
        });

        this.showDialog(dialog);
    }

    /**
     * Generate update available content
     */
    generateUpdateAvailableContent(updateInfo) {
        return `
            <div class="update-info">
                <div class="version-info">
                    <div class="current-version">
                        <span class="label">Versión Actual:</span>
                        <span class="version">${updateInfo.currentVersion || 'Desconocida'}</span>
                    </div>
                    <div class="new-version">
                        <span class="label">Nueva Versión:</span>
                        <span class="version highlight">${updateInfo.version}</span>
                    </div>
                </div>
                
                <div class="update-details">
                    <div class="release-date">
                        <span class="label">Fecha de Lanzamiento:</span>
                        <span class="date">${this.formatDate(updateInfo.releaseDate)}</span>
                    </div>
                    
                    <div class="download-size">
                        <span class="label">Tamaño:</span>
                        <span class="size">${this.formatBytes(updateInfo.downloadSize || 0)}</span>
                    </div>
                </div>

                ${updateInfo.releaseNotes ? `
                    <div class="release-notes">
                        <h4>Novedades en esta versión:</h4>
                        <div class="notes-content">${updateInfo.releaseNotes}</div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Generate download progress content
     */
    generateDownloadProgressContent() {
        return `
            <div class="download-progress">
                <div class="progress-info">
                    <span class="progress-text">Descargando actualización...</span>
                    <span class="progress-percent">0%</span>
                </div>
                
                <div class="progress-bar-container">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="download-stats">
                    <span class="speed">Velocidad: Calculando...</span>
                    <span class="eta">Tiempo estimado: Calculando...</span>
                </div>
            </div>
        `;
    }

    /**
     * Generate install ready content
     */
    generateInstallReadyContent(updateInfo) {
        return `
            <div class="install-ready">
                <div class="success-icon">✅</div>
                <div class="message">
                    <h3>Actualización Descargada</h3>
                    <p>CoreDesk v${updateInfo.version} se ha descargado correctamente y está listo para instalar.</p>
                </div>
                
                <div class="install-options">
                    <div class="option">
                        <strong>Instalar Ahora:</strong> La aplicación se cerrará, instalará la actualización y se reiniciará automáticamente.
                    </div>
                    <div class="option">
                        <strong>Instalar al Cerrar:</strong> La actualización se instalará la próxima vez que cierres la aplicación.
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate update error content
     */
    generateUpdateErrorContent(error) {
        return `
            <div class="update-error">
                <div class="error-icon">❌</div>
                <div class="error-message">
                    <h3>Error al Actualizar</h3>
                    <p>Se produjo un error durante el proceso de actualización.</p>
                    
                    <div class="error-details">
                        <strong>Detalles del error:</strong>
                        <code>${error.message || 'Error desconocido'}</code>
                    </div>
                    
                    <div class="error-suggestions">
                        <p><strong>Sugerencias:</strong></p>
                        <ul>
                            <li>Verifica tu conexión a internet</li>
                            <li>Intenta nuevamente en unos minutos</li>
                            <li>Contacta soporte si el problema persiste</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create a dialog element
     */
    createDialog(type, config) {
        const dialog = {
            type: type,
            element: document.createElement('div'),
            config: config
        };

        dialog.element.className = `update-dialog ${type}`;
        dialog.element.innerHTML = `
            <div class="dialog-header">
                <h3 class="dialog-title">${config.title}</h3>
                ${config.closable !== false ? '<button class="dialog-close">×</button>' : ''}
            </div>
            
            <div class="dialog-content">
                ${config.content}
            </div>
            
            <div class="dialog-actions">
                ${config.buttons.map(button => 
                    `<button class="dialog-btn ${button.class}" data-action="${button.action}">${button.text}</button>`
                ).join('')}
            </div>
        `;

        // Attach event listeners
        this.attachDialogEventListeners(dialog);

        return dialog;
    }

    /**
     * Attach event listeners to dialog
     */
    attachDialogEventListeners(dialog) {
        const element = dialog.element;
        
        // Close button
        const closeBtn = element.querySelector('.dialog-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeCurrentDialog());
        }

        // Action buttons
        const actionButtons = element.querySelectorAll('.dialog-btn');
        actionButtons.forEach((button, index) => {
            button.addEventListener('click', () => {
                const action = dialog.config.buttons[index].action;
                if (typeof action === 'function') {
                    action();
                }
            });
        });
    }

    /**
     * Show dialog
     */
    showDialog(dialog) {
        this.closeCurrentDialog(); // Close any existing dialog
        
        const overlay = document.getElementById('update-dialog-overlay');
        overlay.innerHTML = '';
        overlay.appendChild(dialog.element);
        overlay.classList.remove('hidden');
        
        this.currentDialog = dialog;
    }

    /**
     * Close current dialog
     */
    closeCurrentDialog() {
        if (this.currentDialog) {
            const overlay = document.getElementById('update-dialog-overlay');
            overlay.classList.add('hidden');
            overlay.innerHTML = '';
            
            this.currentDialog = null;
        }
    }

    /**
     * Show update toast notification
     */
    showUpdateToast(config) {
        const toast = document.createElement('div');
        toast.className = `update-toast ${config.type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <div class="toast-title">${config.title}</div>
                <div class="toast-message">${config.message}</div>
                ${config.actions ? `
                    <div class="toast-actions">
                        ${config.actions.map(action => 
                            `<button class="toast-btn">${action.text}</button>`
                        ).join('')}
                    </div>
                ` : ''}
            </div>
            <button class="toast-close">×</button>
        `;

        // Attach event listeners
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.removeToast(toast));

        if (config.actions) {
            const actionButtons = toast.querySelectorAll('.toast-btn');
            actionButtons.forEach((button, index) => {
                button.addEventListener('click', () => {
                    config.actions[index].action();
                    this.removeToast(toast);
                });
            });
        }

        // Add to container
        const container = document.getElementById('update-notifications');
        container.appendChild(toast);

        // Auto-remove after delay
        if (config.autoRemove !== false) {
            setTimeout(() => this.removeToast(toast), config.duration || 10000);
        }
    }

    /**
     * Remove toast notification
     */
    removeToast(toast) {
        if (toast && toast.parentNode) {
            toast.classList.add('fade-out');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    /**
     * Start download
     */
    async startDownload() {
        try {
            this.closeCurrentDialog();
            this.showDownloadProgressDialog();
            
            const result = await window.electronAPI.updateDownload();
            if (!result.success) {
                throw new Error(result.error);
            }
        } catch (error) {
            this.handleUpdateError(error);
        }
    }

    /**
     * Cancel download
     */
    async cancelDownload() {
        // TODO: Implement download cancellation
        this.closeCurrentDialog();
    }

    /**
     * Install update
     */
    async installUpdate() {
        try {
            const result = await window.electronAPI.updateInstall();
            if (!result.success) {
                throw new Error(result.error);
            }
        } catch (error) {
            this.handleUpdateError(error);
        }
    }

    /**
     * Install on quit
     */
    installOnQuit() {
        // TODO: Set flag to install on quit
        this.closeCurrentDialog();
        this.showUpdateToast({
            title: 'Actualización Programada',
            message: 'La actualización se instalará cuando cierres la aplicación',
            type: 'success'
        });
    }

    /**
     * Check for updates
     */
    async checkForUpdates() {
        try {
            this.closeCurrentDialog();
            const result = await window.electronAPI.updateCheck();
            if (!result.success) {
                throw new Error(result.error);
            }
        } catch (error) {
            this.handleUpdateError(error);
        }
    }

    /**
     * Show changelog
     */
    showChangelog(updateInfo) {
        if (updateInfo.changelogUrl) {
            window.open(updateInfo.changelogUrl, '_blank');
        }
    }

    /**
     * Update download progress
     */
    updateDownloadProgress(progress) {
        if (this.currentDialog && this.currentDialog.type === 'download-progress') {
            const element = this.currentDialog.element;
            
            // Update progress bar
            const progressFill = element.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = `${progress.percent}%`;
            }
            
            // Update progress text
            const progressPercent = element.querySelector('.progress-percent');
            if (progressPercent) {
                progressPercent.textContent = `${Math.round(progress.percent)}%`;
            }
            
            // Update speed and ETA
            const speedElement = element.querySelector('.speed');
            const etaElement = element.querySelector('.eta');
            
            if (speedElement && progress.bytesPerSecond) {
                speedElement.textContent = `Velocidad: ${this.formatBytes(progress.bytesPerSecond)}/s`;
            }
            
            if (etaElement && progress.bytesPerSecond && progress.total) {
                const remaining = progress.total - progress.transferred;
                const eta = remaining / progress.bytesPerSecond;
                etaElement.textContent = `Tiempo estimado: ${this.formatTime(eta)}`;
            }
        }
    }

    /**
     * Utility functions
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatTime(seconds) {
        if (seconds < 60) return `${Math.round(seconds)}s`;
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.round(seconds % 60);
        return `${minutes}m ${remainingSeconds}s`;
    }

    formatDate(dateString) {
        if (!dateString) return 'Desconocida';
        const date = new Date(dateString);
        return date.toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    dismissNotification() {
        // Dismiss current notification
    }
}

// Initialize and make available globally
window.updateDialogs = new UpdateDialogs();

// Export for module environments (only if module system is available)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UpdateDialogs;
}