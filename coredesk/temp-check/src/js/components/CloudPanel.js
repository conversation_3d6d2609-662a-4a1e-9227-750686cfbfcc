/**
 * Cloud Panel Component
 * Provides cloud storage browser and sync functionality
 */

class CloudPanel {
    constructor() {
        this.currentPath = '/';
        this.selectedFiles = new Set();
        this.isUploading = false;
        this.uploadProgress = {};
        this.syncStatus = 'disconnected'; // disconnected, connecting, connected, syncing, error
        this.cloudFiles = [];
        this.viewMode = 'grid'; // grid, list
        
        this.init();
    }
    
    init() {
        // Load initial data
        this.loadCloudFiles();
        
        // Set up event listeners when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }
    }
    
    setupEventListeners() {
        // File upload handling
        document.addEventListener('change', (e) => {
            if (e.target.id === 'cloud-file-input') {
                this.handleFileUpload(e.target.files);
            }
        });
        
        // Click handlers
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('cloud-upload-btn')) {
                this.triggerFileUpload();
            }
            
            if (e.target.classList.contains('cloud-refresh-btn')) {
                this.refreshCloudFiles();
            }
            
            if (e.target.classList.contains('cloud-sync-btn')) {
                this.toggleSync();
            }
            
            if (e.target.classList.contains('view-mode-btn')) {
                this.setViewMode(e.target.dataset.mode);
            }
            
            if (e.target.classList.contains('cloud-file-item')) {
                this.handleFileSelection(e.target);
            }
            
            if (e.target.classList.contains('file-action-download')) {
                this.downloadFile(e.target.dataset.fileId);
            }
            
            if (e.target.classList.contains('file-action-delete')) {
                this.deleteFile(e.target.dataset.fileId);
            }
            
            if (e.target.classList.contains('folder-item')) {
                this.navigateToFolder(e.target.dataset.path);
            }
            
            if (e.target.classList.contains('breadcrumb-item')) {
                this.navigateToFolder(e.target.dataset.path);
            }
        });
        
        // Drag and drop
        document.addEventListener('dragover', (e) => {
            if (e.target.closest('.cloud-drop-zone')) {
                e.preventDefault();
            }
        });
        
        document.addEventListener('drop', (e) => {
            if (e.target.closest('.cloud-drop-zone')) {
                e.preventDefault();
                this.handleFileUpload(e.dataTransfer.files);
            }
        });
    }
    
    /**
     * Render cloud panel content
     */
    renderCloudPanel() {
        return `
            <div class="cloud-panel">
                <!-- Cloud Header -->
                <div class="cloud-header">
                    <div class="cloud-status">
                        <div class="status-indicator ${this.syncStatus}"></div>
                        <span class="status-text">${this.getStatusText()}</span>
                    </div>
                    
                    <div class="cloud-actions">
                        <button class="cloud-action-btn cloud-sync-btn" title="Sincronizar">
                            <span class="action-icon">🔄</span>
                        </button>
                        <button class="cloud-action-btn cloud-refresh-btn" title="Actualizar">
                            <span class="action-icon">🔃</span>
                        </button>
                        <button class="cloud-action-btn cloud-upload-btn" title="Subir archivos">
                            <span class="action-icon">📤</span>
                        </button>
                    </div>
                </div>
                
                <!-- Storage Info -->
                <div class="storage-info">
                    <div class="storage-usage">
                        <div class="usage-bar">
                            <div class="usage-fill" style="width: 35%"></div>
                        </div>
                        <div class="usage-text">
                            <span>3.5 GB usado de 10 GB</span>
                            <span class="usage-percentage">35%</span>
                        </div>
                    </div>
                </div>
                
                <!-- Breadcrumb Navigation -->
                <div class="cloud-breadcrumb">
                    ${this.renderBreadcrumb()}
                </div>
                
                <!-- View Mode Toggle -->
                <div class="view-controls">
                    <div class="view-mode-toggle">
                        <button class="view-mode-btn ${this.viewMode === 'grid' ? 'active' : ''}" 
                                data-mode="grid" title="Vista de cuadrícula">
                            <span class="mode-icon">⊞</span>
                        </button>
                        <button class="view-mode-btn ${this.viewMode === 'list' ? 'active' : ''}" 
                                data-mode="list" title="Vista de lista">
                            <span class="mode-icon">☰</span>
                        </button>
                    </div>
                    
                    <div class="file-count">
                        ${this.cloudFiles.length} elemento(s)
                    </div>
                </div>
                
                <!-- File Upload Zone -->
                <div class="cloud-drop-zone">
                    <div class="drop-zone-content">
                        <div class="drop-icon">📁</div>
                        <p>Arrastra archivos aquí o <button class="upload-link cloud-upload-btn">selecciona archivos</button></p>
                        <small>Tamaño máximo: 100MB por archivo</small>
                    </div>
                </div>
                
                <!-- Files List -->
                <div class="cloud-files-container">
                    ${this.renderFilesList()}
                </div>
                
                <!-- Upload Progress -->
                ${Object.keys(this.uploadProgress).length > 0 ? this.renderUploadProgress() : ''}
                
                <!-- Hidden file input -->
                <input type="file" id="cloud-file-input" multiple hidden>
            </div>
        `;
    }
    
    /**
     * Render breadcrumb navigation
     */
    renderBreadcrumb() {
        const pathParts = this.currentPath.split('/').filter(part => part);
        const breadcrumbs = [
            { name: 'Root', path: '/' }
        ];
        
        let currentPath = '';
        pathParts.forEach(part => {
            currentPath += '/' + part;
            breadcrumbs.push({ name: part, path: currentPath });
        });
        
        return `
            <div class="breadcrumb-list">
                ${breadcrumbs.map((crumb, index) => `
                    <span class="breadcrumb-item ${index === breadcrumbs.length - 1 ? 'active' : ''}"
                          data-path="${crumb.path}">
                        ${crumb.name}
                    </span>
                    ${index < breadcrumbs.length - 1 ? '<span class="breadcrumb-separator">/</span>' : ''}
                `).join('')}
            </div>
        `;
    }
    
    /**
     * Render files list
     */
    renderFilesList() {
        if (this.cloudFiles.length === 0) {
            return this.renderEmptyState();
        }
        
        const filesHtml = this.cloudFiles.map(file => {
            if (this.viewMode === 'grid') {
                return this.renderFileCardGrid(file);
            } else {
                return this.renderFileCardList(file);
            }
        }).join('');
        
        return `
            <div class="files-list ${this.viewMode}-view">
                ${filesHtml}
            </div>
        `;
    }
    
    /**
     * Render file card in grid view
     */
    renderFileCardGrid(file) {
        const isSelected = this.selectedFiles.has(file.id);
        
        return `
            <div class="cloud-file-item grid-item ${isSelected ? 'selected' : ''} ${file.type === 'folder' ? 'folder-item' : ''}"
                 data-file-id="${file.id}" data-path="${file.path}">
                <div class="file-thumbnail">
                    <div class="file-icon">${this.getFileIcon(file)}</div>
                    ${file.type !== 'folder' ? `
                        <div class="file-overlay">
                            <button class="file-action file-action-download" 
                                    data-file-id="${file.id}" title="Descargar">📥</button>
                            <button class="file-action file-action-delete" 
                                    data-file-id="${file.id}" title="Eliminar">🗑️</button>
                        </div>
                    ` : ''}
                </div>
                
                <div class="file-info">
                    <div class="file-name" title="${file.name}">${file.name}</div>
                    ${file.type !== 'folder' ? `
                        <div class="file-meta">
                            <span class="file-size">${this.formatFileSize(file.size)}</span>
                            <span class="file-date">${this.formatDate(file.lastModified)}</span>
                        </div>
                    ` : ''}
                </div>
                
                ${file.syncStatus ? `
                    <div class="sync-status-indicator ${file.syncStatus}">
                        ${this.getSyncStatusIcon(file.syncStatus)}
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    /**
     * Render file card in list view
     */
    renderFileCardList(file) {
        const isSelected = this.selectedFiles.has(file.id);
        
        return `
            <div class="cloud-file-item list-item ${isSelected ? 'selected' : ''} ${file.type === 'folder' ? 'folder-item' : ''}"
                 data-file-id="${file.id}" data-path="${file.path}">
                <div class="file-icon">${this.getFileIcon(file)}</div>
                
                <div class="file-name" title="${file.name}">${file.name}</div>
                
                <div class="file-size">
                    ${file.type !== 'folder' ? this.formatFileSize(file.size) : '-'}
                </div>
                
                <div class="file-date">
                    ${this.formatDate(file.lastModified)}
                </div>
                
                <div class="file-actions">
                    ${file.type !== 'folder' ? `
                        <button class="file-action file-action-download" 
                                data-file-id="${file.id}" title="Descargar">📥</button>
                        <button class="file-action file-action-delete" 
                                data-file-id="${file.id}" title="Eliminar">🗑️</button>
                    ` : ''}
                </div>
                
                <div class="sync-status">
                    ${file.syncStatus ? this.getSyncStatusIcon(file.syncStatus) : ''}
                </div>
            </div>
        `;
    }
    
    /**
     * Render empty state
     */
    renderEmptyState() {
        return `
            <div class="cloud-empty-state">
                <div class="empty-icon">☁️</div>
                <h3>No hay archivos en esta carpeta</h3>
                <p>Sube archivos para comenzar a usar el almacenamiento en la nube</p>
                <button class="btn btn-primary cloud-upload-btn">
                    <span class="btn-icon">📤</span>
                    Subir archivos
                </button>
            </div>
        `;
    }
    
    /**
     * Render upload progress
     */
    renderUploadProgress() {
        const uploads = Object.entries(this.uploadProgress);
        
        return `
            <div class="upload-progress-container">
                <div class="upload-header">
                    <h4>Subiendo archivos (${uploads.length})</h4>
                    <button class="close-uploads">×</button>
                </div>
                <div class="upload-list">
                    ${uploads.map(([fileName, progress]) => `
                        <div class="upload-item">
                            <div class="upload-info">
                                <span class="upload-name">${fileName}</span>
                                <span class="upload-percentage">${progress.percentage}%</span>
                            </div>
                            <div class="upload-bar">
                                <div class="upload-fill" style="width: ${progress.percentage}%"></div>
                            </div>
                            <div class="upload-status">${progress.status}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    /**
     * Get file icon based on file type
     */
    getFileIcon(file) {
        if (file.type === 'folder') return '📁';
        
        const extension = file.name.split('.').pop().toLowerCase();
        const iconMap = {
            'pdf': '📄',
            'doc': '📝',
            'docx': '📝',
            'txt': '📄',
            'jpg': '🖼️',
            'jpeg': '🖼️',
            'png': '🖼️',
            'gif': '🖼️',
            'mp4': '🎥',
            'avi': '🎥',
            'mov': '🎥',
            'mp3': '🎵',
            'wav': '🎵',
            'zip': '🗜️',
            'rar': '🗜️',
            'xlsx': '📊',
            'xls': '📊',
            'ppt': '📊',
            'pptx': '📊'
        };
        
        return iconMap[extension] || '📄';
    }
    
    /**
     * Get sync status icon
     */
    getSyncStatusIcon(status) {
        const iconMap = {
            'synced': '✅',
            'syncing': '🔄',
            'pending': '⏳',
            'error': '❌',
            'conflict': '⚠️'
        };
        
        return iconMap[status] || '';
    }
    
    /**
     * Get status text for connection
     */
    getStatusText() {
        const statusMap = {
            'disconnected': 'Desconectado',
            'connecting': 'Conectando...',
            'connected': 'Conectado',
            'syncing': 'Sincronizando...',
            'error': 'Error de conexión'
        };
        
        return statusMap[this.syncStatus] || 'Desconocido';
    }
    
    /**
     * Load cloud files (mock data for now)
     */
    async loadCloudFiles() {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        this.cloudFiles = [
            {
                id: '1',
                name: 'Documentos',
                type: 'folder',
                path: '/Documentos',
                lastModified: new Date(Date.now() - 86400000)
            },
            {
                id: '2',
                name: 'Contratos',
                type: 'folder',
                path: '/Contratos',
                lastModified: new Date(Date.now() - 172800000)
            },
            {
                id: '3',
                name: 'Reporte_Q1_2024.pdf',
                type: 'file',
                size: 2048576,
                path: '/Reporte_Q1_2024.pdf',
                lastModified: new Date(Date.now() - 3600000),
                syncStatus: 'synced'
            },
            {
                id: '4',
                name: 'Contrato_Juan_Perez.docx',
                type: 'file',
                size: 1024000,
                path: '/Contrato_Juan_Perez.docx',
                lastModified: new Date(Date.now() - 7200000),
                syncStatus: 'syncing'
            },
            {
                id: '5',
                name: 'Presentacion_Empresa.pptx',
                type: 'file',
                size: 5242880,
                path: '/Presentacion_Empresa.pptx',
                lastModified: new Date(Date.now() - 10800000),
                syncStatus: 'synced'
            }
        ];
    }
    
    /**
     * Trigger file upload dialog
     */
    triggerFileUpload() {
        const fileInput = document.getElementById('cloud-file-input');
        if (fileInput) {
            fileInput.click();
        }
    }
    
    /**
     * Handle file upload
     */
    async handleFileUpload(files) {
        if (!files || files.length === 0) return;
        
        this.isUploading = true;
        
        for (const file of files) {
            // Initialize progress tracking
            this.uploadProgress[file.name] = {
                percentage: 0,
                status: 'Preparando...'
            };
            
            // Update UI
            this.updateCloudPanel();
            
            // Simulate upload progress
            await this.simulateFileUpload(file);
        }
        
        this.isUploading = false;
        
        // Clear progress after delay
        setTimeout(() => {
            this.uploadProgress = {};
            this.updateCloudPanel();
        }, 3000);
        
        // Refresh file list
        this.refreshCloudFiles();
    }
    
    /**
     * Simulate file upload with progress
     */
    async simulateFileUpload(file) {
        const fileName = file.name;
        const chunks = 20;
        
        for (let i = 0; i <= chunks; i++) {
            const percentage = Math.round((i / chunks) * 100);
            
            this.uploadProgress[fileName] = {
                percentage,
                status: i === chunks ? 'Completado' : 'Subiendo...'
            };
            
            this.updateCloudPanel();
            
            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // Add to cloud files
        this.cloudFiles.push({
            id: Date.now().toString(),
            name: fileName,
            type: 'file',
            size: file.size,
            path: this.currentPath + '/' + fileName,
            lastModified: new Date(),
            syncStatus: 'synced'
        });
    }
    
    /**
     * Refresh cloud files
     */
    async refreshCloudFiles() {
        this.syncStatus = 'syncing';
        this.updateCloudPanel();
        
        await this.loadCloudFiles();
        
        this.syncStatus = 'connected';
        this.updateCloudPanel();
    }
    
    /**
     * Toggle sync
     */
    async toggleSync() {
        if (this.syncStatus === 'disconnected') {
            this.syncStatus = 'connecting';
            this.updateCloudPanel();
            
            // Simulate connection
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            this.syncStatus = 'connected';
        } else if (this.syncStatus === 'connected') {
            this.syncStatus = 'syncing';
            this.updateCloudPanel();
            
            // Simulate sync
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            this.syncStatus = 'connected';
        }
        
        this.updateCloudPanel();
    }
    
    /**
     * Set view mode
     */
    setViewMode(mode) {
        if (mode !== this.viewMode) {
            this.viewMode = mode;
            this.updateCloudPanel();
        }
    }
    
    /**
     * Handle file selection
     */
    handleFileSelection(fileElement) {
        const fileId = fileElement.dataset.fileId;
        
        if (this.selectedFiles.has(fileId)) {
            this.selectedFiles.delete(fileId);
            fileElement.classList.remove('selected');
        } else {
            this.selectedFiles.add(fileId);
            fileElement.classList.add('selected');
        }
    }
    
    /**
     * Navigate to folder
     */
    navigateToFolder(path) {
        this.currentPath = path;
        this.selectedFiles.clear();
        
        // Load files for new path
        this.loadCloudFiles().then(() => {
            this.updateCloudPanel();
        });
    }
    
    /**
     * Download file
     */
    async downloadFile(fileId) {
        const file = this.cloudFiles.find(f => f.id === fileId);
        if (!file) return;
        
        // Simulate download
        console.log(`Downloading file: ${file.name}`);
        
        // In a real implementation, this would trigger the actual download
        alert(`Descargando ${file.name}...`);
    }
    
    /**
     * Delete file
     */
    async deleteFile(fileId) {
        const file = this.cloudFiles.find(f => f.id === fileId);
        if (!file) return;
        
        if (confirm(`¿Estás seguro de que quieres eliminar "${file.name}"?`)) {
            // Remove from list
            this.cloudFiles = this.cloudFiles.filter(f => f.id !== fileId);
            this.selectedFiles.delete(fileId);
            
            // Update UI
            this.updateCloudPanel();
            
            console.log(`Deleted file: ${file.name}`);
        }
    }
    
    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    /**
     * Format date
     */
    formatDate(date) {
        const now = new Date();
        const diff = now - date;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (days === 0) return 'Hoy';
        if (days === 1) return 'Ayer';
        if (days < 7) return `Hace ${days} días`;
        
        return date.toLocaleDateString();
    }
    
    /**
     * Update cloud panel content
     */
    updateCloudPanel() {
        const cloudContent = document.querySelector('.cloud-panel');
        if (cloudContent && cloudContent.parentElement) {
            cloudContent.parentElement.innerHTML = this.renderCloudPanel();
        }
    }
}

// Create global instance
const cloudPanel = new CloudPanel();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = cloudPanel;
}

// Make available globally
if (typeof window !== 'undefined') {
    window.cloudPanel = cloudPanel;
}