/**
 * ThemeManager - Complete Dark/Light Theme System
 * Manages theme switching, persistence, and system preferences
 */

class ThemeManager {
    constructor() {
        this.currentTheme = 'dark'; // Default theme
        this.storageKey = 'coredesk_theme';
        this.systemPrefersDark = false;
        this.listeners = [];
        
        this.initialize();
    }

    /**
     * Initialize the theme manager
     */
    initialize() {
        console.log('ThemeManager', '[ThemeManager] Initializing...', );
        
        // Detect system preference
        this.detectSystemPreference();
        
        // Load saved theme or use system preference
        this.loadTheme();
        
        // Set up system preference listener
        this.setupSystemPreferenceListener();
        
        // Set up UI controls
        this.setupUIControls();
        
        console.log('ThemeManager', '[ThemeManager] Initialized successfully', );
    }

    /**
     * Detect system color scheme preference
     */
    detectSystemPreference() {
        if (window.matchMedia) {
            this.systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            console.log('ThemeManager', `[ThemeManager] System prefers: ${this.systemPrefersDark ? 'dark' : 'light'}`, );
        }
    }

    /**
     * Set up listener for system preference changes
     */
    setupSystemPreferenceListener() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                this.systemPrefersDark = e.matches;
                console.log('ThemeManager', `[ThemeManager] System preference changed to: ${this.systemPrefersDark ? 'dark' : 'light'}`, );
                
                // If user hasn't manually set a theme, follow system preference
                const savedTheme = localStorage.getItem(this.storageKey);
                if (!savedTheme || savedTheme === 'auto') {
                    this.setTheme(this.systemPrefersDark ? 'dark' : 'light', false);
                }
            });
        }
    }

    /**
     * Load theme from storage or use system preference
     */
    loadTheme() {
        try {
            const savedTheme = localStorage.getItem(this.storageKey);
            
            if (savedTheme && ['dark', 'light', 'auto'].includes(savedTheme)) {
                if (savedTheme === 'auto') {
                    this.currentTheme = this.systemPrefersDark ? 'dark' : 'light';
                } else {
                    this.currentTheme = savedTheme;
                }
            } else {
                // No saved preference, use system preference or default to dark
                this.currentTheme = this.systemPrefersDark ? 'dark' : 'light';
            }
            
            this.applyTheme();
            console.log('ThemeManager', `[ThemeManager] Loaded theme: ${this.currentTheme}`, );
            
        } catch (error) {
            console.error('ThemeManager', '[ThemeManager] Error loading theme:', error);
            this.currentTheme = 'dark';
            this.applyTheme();
        }
    }

    /**
     * Set and apply a new theme
     * @param {string} theme - Theme name ('dark', 'light', or 'auto')
     * @param {boolean} save - Whether to save to localStorage
     */
    setTheme(theme, save = true) {
        if (!['dark', 'light', 'auto'].includes(theme)) {
            console.warn('ThemeManager', `[ThemeManager] Invalid theme: ${theme}`, );
            return;
        }

        let actualTheme = theme;
        if (theme === 'auto') {
            actualTheme = this.systemPrefersDark ? 'dark' : 'light';
        }

        if (this.currentTheme !== actualTheme) {
            console.log('ThemeManager', `[ThemeManager] Switching theme from ${this.currentTheme} to ${actualTheme}`, );
            
            this.currentTheme = actualTheme;
            this.applyTheme();
            
            // Notify listeners
            this.notifyListeners(actualTheme, theme);
        }

        // Save preference
        if (save) {
            try {
                localStorage.setItem(this.storageKey, theme);
                console.log('ThemeManager', `[ThemeManager] Saved theme preference: ${theme}`, );
            } catch (error) {
                console.error('ThemeManager', '[ThemeManager] Error saving theme:', error);
            }
        }
    }

    /**
     * Apply the current theme to the document
     */
    applyTheme() {
        const html = document.documentElement;
        
        // Remove existing theme classes
        html.classList.remove('theme-dark', 'theme-light');
        html.removeAttribute('data-theme');
        
        // Apply new theme
        html.classList.add(`theme-${this.currentTheme}`);
        html.setAttribute('data-theme', this.currentTheme);
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor();
        
        // Update favicon if theme-specific versions exist
        this.updateFavicon();
        
        console.log('ThemeManager', `[ThemeManager] Applied theme: ${this.currentTheme}`, );
    }

    /**
     * Update meta theme-color for mobile browsers
     */
    updateMetaThemeColor() {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        
        // Set color based on theme
        const color = this.currentTheme === 'dark' ? '#1e1e1e' : '#ffffff';
        metaThemeColor.content = color;
    }

    /**
     * Update favicon based on theme (if theme-specific favicons exist)
     */
    updateFavicon() {
        const favicon = document.querySelector('link[rel="icon"]');
        if (favicon) {
            const basePath = favicon.href.replace(/favicon.*\.ico$/, '');
            const themeFavicon = `${basePath}favicon-${this.currentTheme}.ico`;
            
            // Check if theme-specific favicon exists
            const img = new Image();
            img.onload = () => {
                favicon.href = themeFavicon;
            };
            img.onerror = () => {
                // Keep default favicon if theme-specific doesn't exist
            };
            img.src = themeFavicon;
        }
    }

    /**
     * Toggle between dark and light themes
     */
    toggleTheme() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
        return newTheme;
    }

    /**
     * Get current theme
     * @returns {string} Current theme name
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * Get saved theme preference (including 'auto')
     * @returns {string} Saved theme preference
     */
    getSavedThemePreference() {
        return localStorage.getItem(this.storageKey) || 'auto';
    }

    /**
     * Check if system prefers dark mode
     * @returns {boolean} True if system prefers dark mode
     */
    getSystemPreference() {
        return this.systemPrefersDark;
    }

    /**
     * Set up UI controls for theme switching
     */
    setupUIControls() {
        // Create theme toggle button in title bar if it doesn't exist
        this.createThemeToggleButton();
        
        // Set up settings panel theme selector
        this.setupSettingsThemeSelector();
        
        // Set up keyboard shortcut (Ctrl+Shift+T)
        this.setupKeyboardShortcut();
    }

    /**
     * Create theme toggle button in title bar
     */
    createThemeToggleButton() {
        const titlebarRight = document.querySelector('.titlebar-right');
        if (!titlebarRight) return;

        // Check if button already exists
        if (document.getElementById('theme-toggle-btn')) return;

        const themeButton = document.createElement('button');
        themeButton.id = 'theme-toggle-btn';
        themeButton.className = 'panel-toggle';
        themeButton.title = `Switch to ${this.currentTheme === 'dark' ? 'light' : 'dark'} theme`;
        themeButton.innerHTML = this.getThemeIcon();
        
        themeButton.addEventListener('click', () => {
            const newTheme = this.toggleTheme();
            themeButton.innerHTML = this.getThemeIcon();
            themeButton.title = `Switch to ${newTheme === 'dark' ? 'light' : 'dark'} theme`;
        });

        // Insert before window controls
        const windowControls = titlebarRight.querySelector('.window-controls');
        if (windowControls) {
            titlebarRight.insertBefore(themeButton, windowControls);
        } else {
            titlebarRight.appendChild(themeButton);
        }
    }

    /**
     * Get theme icon based on current theme
     * @returns {string} SVG icon HTML
     */
    getThemeIcon() {
        if (this.currentTheme === 'dark') {
            // Sun icon for switching to light theme
            return `
                <svg class="panel-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,2L14.39,5.42C13.65,5.15 12.84,5 12,5C11.16,5 10.35,5.15 9.61,5.42L12,2M3.34,7L7.5,6.65C6.9,7.16 6.36,7.78 5.94,8.5C5.5,9.24 5.25,10 5.11,10.79L3.34,7M3.36,17L5.12,13.23C5.26,14 5.53,14.78 5.95,15.5C6.37,16.24 6.91,16.86 7.5,17.37L3.36,17M20.65,7L18.88,10.79C18.74,10 18.47,9.23 18.05,8.5C17.63,7.78 17.1,7.15 16.5,6.64L20.65,7M20.64,17L16.5,17.36C17.09,16.85 17.62,16.22 18.04,15.5C18.46,14.77 18.73,14 18.87,13.21L20.64,17M12,22L9.59,18.56C10.33,18.83 11.14,19 12,19C12.82,19 13.63,18.83 14.37,18.56L12,22Z"/>
                </svg>
            `;
        } else {
            // Moon icon for switching to dark theme
            return `
                <svg class="panel-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95M17.33,17.97C14.5,17.81 11.7,16.64 9.53,14.5C7.36,12.31 6.2,9.5 6.04,6.68C3.23,9.82 3.34,14.4 6.35,17.41C9.37,20.43 14,20.54 17.33,17.97Z"/>
                </svg>
            `;
        }
    }

    /**
     * Set up theme selector in settings panel
     */
    setupSettingsThemeSelector() {
        // This will be called when the settings panel is created
        // For now, we'll set up a listener for when settings are opened
        document.addEventListener('settings:opened', () => {
            this.addThemeSelectorToSettings();
        });
    }

    /**
     * Add theme selector to settings panel
     */
    addThemeSelectorToSettings() {
        const settingsContent = document.querySelector('#settings-panel .panel-content');
        if (!settingsContent) return;

        // Check if already added
        if (settingsContent.querySelector('#theme-selector-section')) return;

        const themeSection = document.createElement('div');
        themeSection.id = 'theme-selector-section';
        themeSection.className = 'settings-section';
        themeSection.innerHTML = `
            <h3 class="settings-section-title">Apariencia</h3>
            <div class="settings-item">
                <label for="theme-selector" class="settings-label">Tema</label>
                <select id="theme-selector" class="settings-select">
                    <option value="auto">Automático (Sistema)</option>
                    <option value="light">Claro</option>
                    <option value="dark">Oscuro</option>
                </select>
                <p class="settings-description">
                    Cambia la apariencia de la aplicación. El modo automático sigue las preferencias del sistema.
                </p>
            </div>
        `;

        settingsContent.appendChild(themeSection);

        // Set current value
        const selector = themeSection.querySelector('#theme-selector');
        selector.value = this.getSavedThemePreference();

        // Set up change listener
        selector.addEventListener('change', (e) => {
            this.setTheme(e.target.value);
        });
    }

    /**
     * Set up keyboard shortcut for theme toggle
     */
    setupKeyboardShortcut() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+T (or Cmd+Shift+T on Mac)
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.toggleTheme();
                
                // Show toast notification
                this.showThemeChangeNotification();
            }
        });
    }

    /**
     * Show notification when theme changes
     */
    showThemeChangeNotification() {
        // Create temporary notification
        const notification = document.createElement('div');
        notification.className = 'theme-change-notification';
        notification.innerHTML = `
            <div class="notification-content">
                ${this.getThemeIcon()}
                <span>Tema cambiado a ${this.currentTheme === 'dark' ? 'oscuro' : 'claro'}</span>
            </div>
        `;
        
        // Add styles
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'var(--background-secondary)',
            color: 'var(--foreground-primary)',
            border: '1px solid var(--border-primary)',
            borderRadius: '8px',
            padding: '12px 16px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
            zIndex: '10000',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '14px',
            transition: 'all 0.3s ease',
            transform: 'translateX(100%)',
            opacity: '0'
        });

        document.body.appendChild(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        });

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * Add theme change listener
     * @param {Function} listener - Callback function that receives (newTheme, preference)
     */
    addThemeChangeListener(listener) {
        if (typeof listener === 'function') {
            this.listeners.push(listener);
        }
    }

    /**
     * Remove theme change listener
     * @param {Function} listener - Listener function to remove
     */
    removeThemeChangeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * Notify all listeners of theme change
     * @param {string} newTheme - New active theme
     * @param {string} preference - User preference (including 'auto')
     */
    notifyListeners(newTheme, preference) {
        this.listeners.forEach(listener => {
            try {
                listener(newTheme, preference || newTheme);
            } catch (error) {
                console.error('ThemeManager', '[ThemeManager] Error in theme change listener:', error);
            }
        });
    }

    /**
     * Get available themes
     * @returns {Array} Array of available theme objects
     */
    getAvailableThemes() {
        return [
            { value: 'auto', name: 'Automático (Sistema)', description: 'Sigue las preferencias del sistema' },
            { value: 'light', name: 'Claro', description: 'Tema claro con fondo blanco' },
            { value: 'dark', name: 'Oscuro', description: 'Tema oscuro con fondo negro' }
        ];
    }

    /**
     * Export current theme configuration
     * @returns {Object} Theme configuration object
     */
    exportThemeConfig() {
        return {
            currentTheme: this.currentTheme,
            savedPreference: this.getSavedThemePreference(),
            systemPreference: this.systemPrefersDark ? 'dark' : 'light',
            availableThemes: this.getAvailableThemes()
        };
    }
}

// Create and export global instance
window.themeManager = new ThemeManager();

console.log('ThemeManager', '[ThemeManager] ThemeManager class defined and global instance created', );

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}