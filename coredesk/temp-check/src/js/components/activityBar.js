/**
 * Activity Bar Component
 * Implements VS Code-inspired activity bar with module switching and panel controls
 * as specified in PRD section 3.4
 */

class ActivityBar {
    constructor() {
        this.currentActivePanel = null;
        this.panelButtons = new Map();
        this.currentModule = null;
        
        this.initialize();
    }

    /**
     * Initialize the activity bar
     */
    initialize() {
        console.log('UI', '[ActivityBar] Initializing...', );
        
        this.createActivityBarStructure();
        this.setupEventListeners();
        this.updateForCurrentModule();
        
        console.log('UI', '[ActivityBar] Initialized successfully', );
    }

    /**
     * Setup the existing activity bar structure (don't recreate)
     */
    createActivityBarStructure() {
        const activityBar = document.querySelector('.activity-bar');
        if (!activityBar) {
            console.error('[ActivityBar] Activity bar element not found');
            return;
        }
        
        // Just update data attributes for existing buttons to enable functionality
        this.setupExistingButtons();
    }

    /**
     * Setup existing buttons in HTML with proper data attributes and functionality
     */
    setupExistingButtons() {
        // Map HTML button IDs to panel types
        const buttonMappings = {
            'explorer-button': 'explorer',
            'cloud-button': 'cloud', 
            'search-button': 'search',
            'modules-button': 'modules',
            'extensions-button': 'extensions'
        };

        // Setup panel buttons
        Object.entries(buttonMappings).forEach(([buttonId, panelType]) => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.dataset.panel = panelType;
                button.classList.add('panel-btn');
                this.panelButtons.set(panelType, button);
            }
        });

        // Setup settings button
        const settingsButton = document.getElementById('settings-button');
        if (settingsButton) {
            settingsButton.dataset.action = 'settings';
            settingsButton.classList.add('settings-btn');
        }

        // Set initial active state for explorer
        const explorerBtn = document.getElementById('explorer-button');
        if (explorerBtn) {
            explorerBtn.classList.add('active');
            this.currentActivePanel = 'explorer';
        }
        
        // Force icon fallbacks for better visibility
        this.forceIconFallbacks();
    }

    /**
     * Force icon fallbacks to show
     */
    forceIconFallbacks() {
        const activityBar = document.querySelector('.activity-bar');
        if (activityBar) {
            activityBar.classList.add('force-fallback');
            console.log('UI', '[ActivityBar] Icon fallbacks activated for better visibility');
        }
    }


    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for button clicks using event delegation
        document.addEventListener('click', (e) => {
            const settingsBtn = e.target.closest('#settings-button');
            const activityBtn = e.target.closest('.activity-button[data-panel]');

            if (settingsBtn) {
                this.handleSettingsClick();
            } else if (activityBtn) {
                this.handlePanelClick(activityBtn);
            }
        });

        // Listen for module changes
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.MODULE_SWITCHED, (event) => {
            this.updateActiveModule(event.detail.to);
        });

        // Listen for panel changes
        window.addEventListener('panel:toggled', (event) => {
            this.updateActivePanel(event.detail.panel, event.detail.isOpen);
        });
    }


    /**
     * Handle panel button click
     */
    handlePanelClick(button) {
        const contentType = button.dataset.panel;
        if (!contentType) return;

        console.log('UI', `[ActivityBar] Panel content clicked: ${contentType}`, );

        // Handle left panel content switching
        if (window.panelManager) {
            const leftPanel = window.panelManager.panels.left;
            
            // If the same content is clicked and panel is open, close it
            if (leftPanel.isOpen && leftPanel.currentContent === contentType) {
                window.panelManager.closePanel('left');
            } else {
                // Open panel with new content type or switch content
                window.panelManager.setPanelContent('left', contentType);
                if (!leftPanel.isOpen) {
                    window.panelManager.openPanel('left');
                }
            }
        }

        // Update active state
        this.togglePanelButton(button, contentType);
    }

    /**
     * Handle settings button click
     */
    handleSettingsClick() {
        console.log('UI', '[ActivityBar] Settings clicked', );

        // Show configuration panel as modal
        if (window.configurationPanel) {
            console.log('UI', '[ActivityBar] ConfigurationPanel found, calling show()', );
            try {
                window.configurationPanel.show();
            } catch (error) {
                console.error('UI', '[ActivityBar] Error showing configuration panel:', error);
            }
        } else {
            console.warn('UI', '[ActivityBar] ConfigurationPanel not available', );
            // Try to wait a bit for initialization
            setTimeout(() => {
                if (window.configurationPanel) {
                    console.log('UI', '[ActivityBar] ConfigurationPanel available after delay', );
                    window.configurationPanel.show();
                } else {
                    console.error('UI', '[ActivityBar] ConfigurationPanel still not available', );
                    alert('Settings panel is not ready yet. Please try again in a moment.');
                }
            }, 100);
        }
    }

    /**
     * Toggle panel button active state
     */
    togglePanelButton(button, contentType) {
        // Remove active from all activity buttons
        document.querySelectorAll('.activity-button').forEach(btn => {
            btn.classList.remove('active');
        });

        // Check if left panel is open with this content type
        const leftPanel = window.panelManager?.panels?.left;
        const shouldBeActive = leftPanel?.isOpen && leftPanel?.currentContent === contentType;

        if (shouldBeActive) {
            button.classList.add('active');
            this.currentActivePanel = contentType;
        } else {
            this.currentActivePanel = null;
        }
    }

    /**
     * Update active module display (simplified for panel-only activity bar)
     */
    updateActiveModule(moduleCode) {
        this.currentModule = moduleCode;
        console.log('UI', `[ActivityBar] Active module updated: ${moduleCode}`, );
    }

    /**
     * Update active panel display
     */
    updateActivePanel(panelType, isOpen) {
        const panelBtn = document.querySelector(`[data-panel="${panelType}"]`);
        if (panelBtn) {
            panelBtn.classList.toggle('active', isOpen);
            
            if (isOpen) {
                this.currentActivePanel = panelType;
            } else if (this.currentActivePanel === panelType) {
                this.currentActivePanel = null;
            }
        }
    }

    /**
     * Update for current module on initialization
     */
    updateForCurrentModule() {
        if (window.exclusiveModuleController) {
            const currentModule = window.exclusiveModuleController.currentModule;
            if (currentModule) {
                this.updateActiveModule(currentModule);
            }
        }
    }


    /**
     * Get current status
     */
    getStatus() {
        return {
            currentModule: this.currentModule,
            currentActivePanel: this.currentActivePanel,
            initialized: true
        };
    }
}

// Create global instance
window.activityBar = new ActivityBar();
window.activityBarInitialized = true;

console.log('UI', '[ActivityBar] Global instance created successfully', );
