/**
 * TitleBar Component
 * Handles title bar functionality including account button, panel toggles, and window controls
 */

class TitleBar {
    constructor() {
        this.initialize();
    }

    /**
     * Initialize title bar component
     */
    initialize() {
        console.log('UI', '[TitleBar] Initializing...', );
        
        // Verify DOM elements exist
        const maximizeBtn = document.getElementById('maximize-btn');
        console.log('UI', '[TitleBar] Maximize button found:', !!maximizeBtn);
        
        this.setupEventListeners();
        this.updateAccountButton();
        
        console.log('UI', '[TitleBar] Initialized successfully', );
    }

    /**
     * Setup event listeners for title bar elements
     */
    setupEventListeners() {
        // Account button - toggle dropdown instead of direct modal
        const accountButton = document.getElementById('account-button');
        if (accountButton) {
            console.log('UI', '[TitleBar] Account button found, adding click listener');
            accountButton.addEventListener('click', (e) => {
                e.stopPropagation();
                console.log('UI', '[TitleBar] Account button clicked - toggling dropdown');
                
                // Check if modules are still loading
                if (this.isModuleLoadingInProgress()) {
                    console.log('UI', '[TitleBar] Modules still loading, deferring dropdown toggle');
                    this.deferDropdownToggle();
                } else {
                    this.toggleAccountDropdown();
                }
            });
        } else {
            console.error('UI', '[TitleBar] Account button not found in DOM');
        }
        
        // Initialize dropdown state
        const dropdown = document.getElementById('account-dropdown');
        if (dropdown) {
            dropdown.setAttribute('data-hidden', 'true');
            console.log('UI', '[TitleBar] Dropdown state initialized as hidden');
        }
        
        // Account dropdown options
        const myAccountOption = document.getElementById('my-account-option');
        if (myAccountOption) {
            console.log('UI', '[TitleBar] My Account option found, adding click listener');
            myAccountOption.addEventListener('click', () => {
                console.log('UI', '[TitleBar] My Account option clicked');
                this.hideAccountDropdown();
                this.handleAccountClick();
            });
        } else {
            console.error('UI', '[TitleBar] My Account option not found in DOM');
        }
        
        const logoutOption = document.getElementById('logout-option');
        if (logoutOption) {
            console.log('UI', '[TitleBar] Logout option found, adding click listener');
            logoutOption.addEventListener('click', () => {
                console.log('UI', '[TitleBar] Logout option clicked');
                this.hideAccountDropdown();
                this.handleLogout();
            });
        } else {
            console.error('UI', '[TitleBar] Logout option not found in DOM');
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            const accountMenu = document.querySelector('.account-menu');
            if (!accountMenu.contains(e.target)) {
                this.hideAccountDropdown();
            }
        });

        // Window control buttons
        this.setupWindowControls();
        
        // Window dragging functionality
        this.setupWindowDragging();
        
        // Panel toggle buttons are handled by panelManager
        
        // Listen for theme changes to update account icon
        window.addEventListener('coredesk:settings:applied', () => {
            this.updateAccountButton();
        });
        
        // Listen for license changes to update account status
        window.addEventListener('license:statusChanged', () => {
            this.updateAccountButton();
        });
        
        // Listen for all modules loaded event
        window.addEventListener('modules:allLoaded', () => {
            console.log('UI', '[TitleBar] All modules loaded - dropdown is now safe to use');
            this.modulesLoaded = true;
        });
    }

    /**
     * Setup window control button event listeners
     */
    setupWindowControls() {
        // Minimize button
        const minimizeBtn = document.getElementById('minimize-btn');
        if (minimizeBtn) {
            minimizeBtn.addEventListener('click', () => {
                if (window.electronAPI && window.electronAPI.window && window.electronAPI.window.minimize) {
                    window.electronAPI.window.minimize();
                }
            });
        }

        // Maximize/Restore button
        const maximizeBtn = document.getElementById('maximize-btn');
        if (maximizeBtn) {
            maximizeBtn.addEventListener('click', async () => {
                console.log('UI', '[TitleBar] Maximize button clicked');
                try {
                    if (window.electronAPI && window.electronAPI.window && window.electronAPI.window.maximize) {
                        console.log('UI', '[TitleBar] Calling maximize API');
                        const result = await window.electronAPI.window.maximize();
                        console.log('UI', '[TitleBar] Maximize result:', result);
                        if (result && !result.success) {
                            console.error('UI', '[TitleBar] Maximize failed:', result.error);
                        }
                    } else {
                        console.error('UI', '[TitleBar] electronAPI.window.maximize not available');
                    }
                } catch (error) {
                    console.error('UI', '[TitleBar] Error calling maximize:', error);
                }
            });
        } else {
            console.warn('UI', '[TitleBar] Maximize button not found');
        }

        // Close button
        const closeBtn = document.getElementById('close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                if (window.electronAPI && window.electronAPI.window && window.electronAPI.window.close) {
                    window.electronAPI.window.close();
                }
            });
        }
    }

    /**
     * Setup window dragging functionality (fallback for -webkit-app-region)
     */
    setupWindowDragging() {
        const titlebar = document.querySelector('.titlebar');
        if (!titlebar) return;

        let isDragging = false;
        let dragStartX = 0;
        let dragStartY = 0;

        titlebar.addEventListener('mousedown', (e) => {
            // Only start dragging if clicking on the titlebar itself, not on buttons
            if (e.target.closest('.window-controls') || 
                e.target.closest('.panel-toggles') || 
                e.target.closest('.account-menu') ||
                e.target.closest('button')) {
                return;
            }

            isDragging = true;
            dragStartX = e.clientX;
            dragStartY = e.clientY;
            titlebar.style.cursor = 'grabbing';
            
            console.log('UI', '[TitleBar] Started dragging window');
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const deltaX = e.clientX - dragStartX;
            const deltaY = e.clientY - dragStartY;

            // Use Electron API to move window
            if (window.electronAPI && window.electronAPI.window && window.electronAPI.window.move) {
                window.electronAPI.window.move(deltaX, deltaY).catch(error => {
                    console.error('UI', '[TitleBar] Error moving window:', error);
                });
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                titlebar.style.cursor = '';
                console.log('UI', '[TitleBar] Stopped dragging window');
            }
        });
    }

    /**
     * Toggle account dropdown visibility
     */
    toggleAccountDropdown() {
        const dropdown = document.getElementById('account-dropdown');
        if (dropdown) {
            console.log('UI', '[TitleBar] Dropdown element found:', dropdown);
            console.log('UI', '[TitleBar] Dropdown tagName:', dropdown.tagName);
            console.log('UI', '[TitleBar] Dropdown className:', dropdown.className);
            
            // Check current state
            const currentDisplay = dropdown.style.display;
            const computedStyle = window.getComputedStyle(dropdown);
            const computedDisplay = computedStyle.display;
            
            console.log('UI', '[TitleBar] Before toggle - style.display:', currentDisplay, 'computed.display:', computedDisplay);
            
            // Use a simple toggle based on a data attribute to track state
            const isHidden = dropdown.getAttribute('data-hidden') !== 'false';
            
            if (isHidden) {
                dropdown.style.display = 'block';
                dropdown.style.visibility = 'visible';
                dropdown.style.opacity = '1';
                dropdown.setAttribute('data-hidden', 'false');
                console.log('UI', '[TitleBar] Showing dropdown - set display to block');
            } else {
                dropdown.style.display = 'none';
                dropdown.setAttribute('data-hidden', 'true');
                console.log('UI', '[TitleBar] Hiding dropdown - set display to none');
            }
            
            // Verify the change
            setTimeout(() => {
                const newDisplay = dropdown.style.display;
                const newComputed = window.getComputedStyle(dropdown).display;
                console.log('UI', '[TitleBar] After toggle - style.display:', newDisplay, 'computed.display:', newComputed);
            }, 10);
            
        } else {
            console.error('UI', '[TitleBar] Account dropdown element not found');
        }
    }

    /**
     * Hide account dropdown
     */
    hideAccountDropdown() {
        const dropdown = document.getElementById('account-dropdown') || 
                        document.querySelector('.account-dropdown');
        if (dropdown) {
            dropdown.style.display = 'none';
            console.log('UI', '[TitleBar] Account dropdown forcibly hidden');
        }
    }

    /**
     * Handle account button click (now from dropdown)
     */
    handleAccountClick() {
        console.log('UI', '[TitleBar] My Account option clicked');
        
        if (window.accountModal) {
            window.accountModal.show();
        } else {
            console.warn('UI', '[TitleBar] AccountModal not available');
        }
    }

    /**
     * Handle logout option click
     */
    handleLogout() {
        console.log('UI', '[TitleBar] Logout option clicked');
        
        if (window.unifiedAuthManager) {
            window.unifiedAuthManager.logout().then(() => {
                console.log('UI', '[TitleBar] Logout completed');
            }).catch(error => {
                console.error('UI', '[TitleBar] Logout error:', error);
            });
        } else {
            console.warn('UI', '[TitleBar] UnifiedAuthManager not available');
        }
    }

    /**
     * Update account button appearance based on theme and user status
     */
    updateAccountButton() {
        const accountButton = document.getElementById('account-button');
        if (!accountButton) return;

        // Update icon based on theme
        const accountIcon = accountButton.querySelector('.account-icon');
        if (accountIcon) {
            // The icon is already using emoji, but we could update based on user status
            // For now, keep the simple user icon
            accountIcon.textContent = '👤';
        }

        // Update text based on license status
        const accountText = accountButton.querySelector('.account-text');
        if (accountText && window.licenseManager) {
            const licenseStatus = window.licenseManager.getLicenseStatus();
            if (licenseStatus.isActivated && licenseStatus.license && licenseStatus.license.email) {
                // Show first part of email if available
                const email = licenseStatus.license.email;
                const shortEmail = email.length > 15 ? email.substring(0, 12) + '...' : email;
                accountText.textContent = shortEmail;
            } else {
                accountText.textContent = 'Mi Cuenta';
            }
        }
    }

    /**
     * Check if module loading is in progress
     */
    isModuleLoadingInProgress() {
        // If we have the modules loaded flag, use it
        if (this.modulesLoaded === true) {
            return false;
        }
        
        // After 20 seconds, consider modules loaded to prevent infinite loops
        if (performance.now() > 20000) {
            console.log('UI', '[TitleBar] Module loading timeout reached, assuming modules are loaded');
            this.modulesLoaded = true;
            return false;
        }
        
        // Check for active installation logs in the last 15 seconds
        const recentInstallLogs = performance.now() < 15000;
        
        return recentInstallLogs;
    }

    /**
     * Defer dropdown toggle until modules are loaded
     */
    deferDropdownToggle() {
        // Wait for modules to finish loading, then toggle dropdown
        const checkAndToggle = () => {
            if (!this.isModuleLoadingInProgress()) {
                console.log('UI', '[TitleBar] Modules finished loading, now toggling dropdown');
                this.toggleAccountDropdown();
            } else {
                console.log('UI', '[TitleBar] Still waiting for modules to finish...');
                setTimeout(checkAndToggle, 500);
            }
        };
        
        setTimeout(checkAndToggle, 500);
    }

    /**
     * Get title bar status
     */
    getStatus() {
        return {
            initialized: true,
            accountButtonActive: !!document.getElementById('account-button')
        };
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.titleBar = new TitleBar();
        window.titleBarInitialized = true;
        console.log('UI', '[TitleBar] Global instance created successfully');
    });
} else {
    // DOM is already ready
    window.titleBar = new TitleBar();
    window.titleBarInitialized = true;
    console.log('UI', '[TitleBar] Global instance created successfully');
}
