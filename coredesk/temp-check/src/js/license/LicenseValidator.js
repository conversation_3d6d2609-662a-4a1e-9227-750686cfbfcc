/**
 * LicenseValidator.js
 * Validates license keys and manages license status
 * Implements the trial flow and license validation logic from PRD
 */

// licenseApiService will be available globally
// const { licenseApiService } = require('../services/api');

class LicenseValidator {
    constructor() {
        this.deviceFingerprint = null;
        this.validationCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        
        this.licenseTypes = {
            TRIAL: 'trial',
            STANDARD: 'standard',
            PROFESSIONAL: 'professional',
            ENTERPRISE: 'enterprise'
        };
        
        this.validationRules = {
            trial: {
                duration: 30, // days
                maxActivations: 1,
                requiresEmail: true,
                modules: ['lexflow', 'protocolx']
            },
            standard: {
                duration: 365, // days
                maxActivations: 1,
                requiresEmail: false,
                modules: ['lexflow', 'protocolx']
            },
            professional: {
                duration: 365, // days
                maxActivations: 3,
                requiresEmail: false,
                modules: ['lexflow', 'protocolx', 'auditpro']
            },
            enterprise: {
                duration: 365, // days
                maxActivations: 10,
                requiresEmail: false,
                modules: ['lexflow', 'protocolx', 'auditpro', 'finsync']
            }
        };
        
        this.initialize();
    }

    /**
     * Initialize the license validator
     */
    async initialize() {
        console.log('LicenseValidator', '[LicenseValidator] Initializing...', );
        
        try {
            // Initialize device fingerprint
            this.deviceFingerprint = new DeviceFingerprint();
            await this.deviceFingerprint.initialize();
            
            console.log('LicenseValidator', '[LicenseValidator] Initialized successfully', );
            
        } catch (error) {
            console.error('LicenseValidator', '[LicenseValidator] Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Validate a license key
     * @param {string} licenseKey - License key to validate
     * @param {Object} options - Validation options
     * @returns {Promise<Object>} Validation result
     */
    async validateLicense(licenseKey, options = {}) {
        const { useCache = true, userEmail = null } = options;
        
        console.log('LicenseValidator', '[LicenseValidator] Validating license key...', );
        
        try {
            // Check cache first
            if (useCache && this.validationCache.has(licenseKey)) {
                const cached = this.validationCache.get(licenseKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    console.log('LicenseValidator', '[LicenseValidator] Returning cached validation result', );
                    return cached.result;
                }
            }
            
            // Perform validation
            const result = await this.performValidation(licenseKey, userEmail);
            
            // Cache result
            if (useCache && result.isValid) {
                this.validationCache.set(licenseKey, {
                    result,
                    timestamp: Date.now()
                });
            }
            
            return result;
            
        } catch (error) {
            console.error('LicenseValidator', '[LicenseValidator] License validation failed:', error);
            return this.createErrorResult(error.message);
        }
    }

    /**
     * Perform the actual license validation
     * @param {string} licenseKey - License key
     * @param {string} userEmail - User email (for trial licenses)
     * @returns {Promise<Object>} Validation result
     * @private
     */
    async performValidation(licenseKey, userEmail) {
        // Parse license key
        const parsedLicense = this.parseLicenseKey(licenseKey);
        if (!parsedLicense.isValid) {
            return this.createErrorResult('Invalid license key format');
        }
        
        // Validate with API first
        const apiValidation = await licenseApiService.validateLicense(licenseKey);
        
        if (!apiValidation.success) {
            return this.createErrorResult(apiValidation.error || 'License validation failed');
        }
        
        if (!apiValidation.valid) {
            return this.createErrorResult('License key is not valid');
        }
        
        const licenseInfo = apiValidation.licenseInfo;
        
        // Check license status
        if (licenseInfo.status !== 'active') {
            return this.createErrorResult(`License is ${licenseInfo.status}`);
        }
        
        // Check expiration
        if (this.isLicenseExpired(licenseInfo)) {
            return this.createErrorResult('License has expired');
        }
        
        // Check device fingerprint for activation limit
        const fingerprintValid = await this.validateDeviceFingerprint(licenseInfo);
        if (!fingerprintValid.isValid) {
            return this.createErrorResult(fingerprintValid.reason);
        }
        
        // Validate trial-specific requirements
        if (licenseInfo.type === this.licenseTypes.TRIAL) {
            const trialValid = await this.validateTrialRequirements(licenseInfo, userEmail);
            if (!trialValid.isValid) {
                return this.createErrorResult(trialValid.reason);
            }
        }
        
        // Get license rules
        const rules = this.validationRules[licenseInfo.type] || licenseInfo.features;
        if (!rules) {
            return this.createErrorResult('Unknown license type');
        }
        
        // Create success result
        return {
            isValid: true,
            license: {
                key: licenseKey,
                type: licenseInfo.type,
                status: licenseInfo.status,
                email: licenseInfo.email,
                activatedAt: licenseInfo.activatedAt || licenseInfo.activated_at,
                expiresAt: licenseInfo.expiresAt || licenseInfo.expires_at,
                modules: rules.modules || licenseInfo.modules || [],
                deviceFingerprint: this.deviceFingerprint.getFingerprint(),
                activationCount: licenseInfo.activationCount || licenseInfo.activation_count || 0,
                maxActivations: rules.maxActivations || licenseInfo.maxActivations || 1
            },
            validation: {
                timestamp: new Date().toISOString(),
                deviceFingerprint: this.deviceFingerprint.getFingerprint(),
                remainingDays: this.calculateRemainingDays(licenseInfo.expiresAt || licenseInfo.expires_at)
            }
        };
    }

    /**
     * Parse and validate license key format
     * @param {string} licenseKey - License key to parse
     * @returns {Object} Parse result
     * @private
     */
    parseLicenseKey(licenseKey) {
        if (!licenseKey || typeof licenseKey !== 'string') {
            return { isValid: false, reason: 'License key must be a string' };
        }
        
        // Remove spaces and convert to uppercase
        const cleanKey = licenseKey.replace(/\s/g, '').toUpperCase();
        
        // Check format: XXXX-XXXX-XXXX-XXXX (16 characters + 3 hyphens)
        const licensePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
        
        if (!licensePattern.test(cleanKey)) {
            return { isValid: false, reason: 'Invalid license key format' };
        }
        
        return {
            isValid: true,
            cleanKey,
            segments: cleanKey.split('-')
        };
    }

    /**
     * Get license information from database
     * @param {string} licenseKey - License key
     * @returns {Promise<Object|null>} License info or null
     * @private
     */
    async getLicenseFromDatabase(licenseKey) {
        try {
            // Use Electron IPC to query the database
            if (window.electronAPI && window.electronAPI.executeQuery) {
                const result = await window.electronAPI.executeQuery(
                    'SELECT * FROM licenses WHERE license_key = ? AND status = "active"',
                    [licenseKey]
                );
                
                return result.length > 0 ? result[0] : null;
            }
            
            // Fallback for non-Electron environments
            console.warn('LicenseValidator', '[LicenseValidator] Database access not available', );
            return null;
            
        } catch (error) {
            console.error('LicenseValidator', '[LicenseValidator] Database query failed:', error);
            return null;
        }
    }

    /**
     * Check if license is expired
     * @param {Object} licenseInfo - License information
     * @returns {boolean} True if expired
     * @private
     */
    isLicenseExpired(licenseInfo) {
        if (!licenseInfo.expires_at) {
            return false; // Perpetual license
        }
        
        const expirationDate = new Date(licenseInfo.expires_at);
        const now = new Date();
        
        return now > expirationDate;
    }

    /**
     * Validate device fingerprint against activation limits
     * @param {Object} licenseInfo - License information
     * @returns {Promise<Object>} Validation result
     * @private
     */
    async validateDeviceFingerprint(licenseInfo) {
        const currentFingerprint = this.deviceFingerprint.getFingerprint();
        
        if (!currentFingerprint) {
            return { isValid: false, reason: 'Could not generate device fingerprint' };
        }
        
        try {
            // Get existing activations
            const activations = await this.getActivationsFromDatabase(licenseInfo.license_key);
            
            // Check if current device is already activated
            const existingActivation = activations.find(activation => 
                activation.device_fingerprint === currentFingerprint
            );
            
            if (existingActivation) {
                // Device already activated
                return { isValid: true, isExistingActivation: true };
            }
            
            // Check activation limit
            const rules = this.validationRules[licenseInfo.type];
            if (activations.length >= rules.maxActivations) {
                return { 
                    isValid: false, 
                    reason: `Maximum activations reached (${rules.maxActivations})` 
                };
            }
            
            // New activation allowed
            return { isValid: true, isNewActivation: true };
            
        } catch (error) {
            console.error('LicenseValidator', '[LicenseValidator] Device fingerprint validation failed:', error);
            return { isValid: false, reason: 'Device validation failed' };
        }
    }

    /**
     * Get activations from database
     * @param {string} licenseKey - License key
     * @returns {Promise<Array>} Array of activations
     * @private
     */
    async getActivationsFromDatabase(licenseKey) {
        try {
            if (window.electronAPI && window.electronAPI.executeQuery) {
                const result = await window.electronAPI.executeQuery(
                    'SELECT * FROM activations WHERE license_key = ? AND status = "active"',
                    [licenseKey]
                );
                
                return result || [];
            }
            
            return [];
            
        } catch (error) {
            console.error('LicenseValidator', '[LicenseValidator] Failed to get activations:', error);
            return [];
        }
    }

    /**
     * Validate trial-specific requirements
     * @param {Object} licenseInfo - License information
     * @param {string} userEmail - User email
     * @returns {Promise<Object>} Validation result
     * @private
     */
    async validateTrialRequirements(licenseInfo, userEmail) {
        // Check if email is required and provided
        if (this.validationRules.trial.requiresEmail && !userEmail) {
            return { isValid: false, reason: 'Email required for trial activation' };
        }
        
        // Validate email format if provided
        if (userEmail && !this.isValidEmail(userEmail)) {
            return { isValid: false, reason: 'Invalid email format' };
        }
        
        // Check if email matches license
        if (licenseInfo.email && userEmail && licenseInfo.email !== userEmail) {
            return { isValid: false, reason: 'Email does not match license' };
        }
        
        // Check trial eligibility (one trial per email)
        if (userEmail) {
            const existingTrial = await this.checkExistingTrial(userEmail);
            if (existingTrial && existingTrial.license_key !== licenseInfo.license_key) {
                return { isValid: false, reason: 'Trial already used for this email' };
            }
        }
        
        return { isValid: true };
    }

    /**
     * Check for existing trial with email
     * @param {string} email - Email to check
     * @returns {Promise<Object|null>} Existing trial or null
     * @private
     */
    async checkExistingTrial(email) {
        try {
            if (window.electronAPI && window.electronAPI.executeQuery) {
                const result = await window.electronAPI.executeQuery(
                    'SELECT * FROM licenses WHERE email = ? AND type = "trial"',
                    [email]
                );
                
                return result.length > 0 ? result[0] : null;
            }
            
            return null;
            
        } catch (error) {
            console.error('LicenseValidator', '[LicenseValidator] Failed to check existing trial:', error);
            return null;
        }
    }

    /**
     * Validate email format
     * @param {string} email - Email to validate
     * @returns {boolean} True if valid
     * @private
     */
    isValidEmail(email) {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailPattern.test(email);
    }

    /**
     * Calculate remaining days until expiration
     * @param {string} expirationDate - Expiration date string
     * @returns {number} Remaining days
     * @private
     */
    calculateRemainingDays(expirationDate) {
        if (!expirationDate) {
            return Infinity; // Perpetual license
        }
        
        const expiry = new Date(expirationDate);
        const now = new Date();
        const diffTime = expiry - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return Math.max(0, diffDays);
    }

    /**
     * Create error validation result
     * @param {string} reason - Error reason
     * @returns {Object} Error result
     * @private
     */
    createErrorResult(reason) {
        return {
            isValid: false,
            error: reason,
            validation: {
                timestamp: new Date().toISOString(),
                deviceFingerprint: this.deviceFingerprint?.getFingerprint() || null
            }
        };
    }

    /**
     * Activate a license for the current device
     * @param {string} licenseKey - License key
     * @param {Object} options - Activation options
     * @returns {Promise<Object>} Activation result
     */
    async activateLicense(licenseKey, options = {}) {
        const { userEmail = null, deviceName = null } = options;
        
        console.log('LicenseValidator', '[LicenseValidator] Activating license...', );
        
        try {
            // First validate the license
            const validation = await this.validateLicense(licenseKey, { userEmail, useCache: false });
            
            if (!validation.isValid) {
                return { success: false, error: validation.error };
            }
            
            // Check if already activated on this device
            if (validation.validation.isExistingActivation) {
                return { success: true, message: 'License already activated on this device' };
            }
            
            // Prepare device info
            const deviceInfo = {
                fingerprint: this.deviceFingerprint.getFingerprint(),
                hostname: deviceName || require('os').hostname(),
                platform: process.platform,
                arch: process.arch,
                cpuInfo: this.deviceFingerprint.getCPUInfo(),
                macAddresses: this.deviceFingerprint.getMACAddresses(),
                displayInfo: this.deviceFingerprint.getDisplayInfo()
            };
            
            // Activate with API
            const activationResult = await licenseApiService.activateLicense(licenseKey, deviceInfo);
            
            if (!activationResult.success) {
                return { 
                    success: false, 
                    error: activationResult.error || 'Activation failed' 
                };
            }
            
            // Store activation locally
            const activation = await this.createActivation(licenseKey, {
                deviceFingerprint: deviceInfo.fingerprint,
                deviceName: deviceInfo.hostname,
                userEmail,
                deviceSummary: this.deviceFingerprint.getDeviceSummary(),
                activationId: activationResult.activationId
            });
            
            if (!activation.success) {
                return { success: false, error: activation.error };
            }
            
            console.log('LicenseValidator', '[LicenseValidator] License activated successfully', );
            
            return {
                success: true,
                license: validation.license,
                activation: {
                    ...activationResult.data,
                    localId: activation.data?.id
                }
            };
            
        } catch (error) {
            console.error('LicenseValidator', '[LicenseValidator] License activation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Create activation record in database
     * @param {string} licenseKey - License key
     * @param {Object} activationData - Activation data
     * @returns {Promise<Object>} Creation result
     * @private
     */
    async createActivation(licenseKey, activationData) {
        try {
            if (window.electronAPI && window.electronAPI.executeQuery) {
                const result = await window.electronAPI.executeQuery(
                    `INSERT INTO activations (
                        license_key, device_fingerprint, device_name, 
                        device_info, activated_at, status, activation_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [
                        licenseKey,
                        activationData.deviceFingerprint,
                        activationData.deviceName,
                        JSON.stringify(activationData.deviceSummary),
                        new Date().toISOString(),
                        'active',
                        activationData.activationId || null
                    ]
                );
                
                return {
                    success: true,
                    data: {
                        id: result.lastID,
                        licenseKey,
                        deviceFingerprint: activationData.deviceFingerprint,
                        activatedAt: new Date().toISOString()
                    }
                };
            }
            
            return { success: false, error: 'Database not available' };
            
        } catch (error) {
            console.error('LicenseValidator', '[LicenseValidator] Failed to create activation:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get license rules for a license type
     * @param {string} licenseType - License type
     * @returns {Object|null} License rules
     */
    getLicenseRules(licenseType) {
        return this.validationRules[licenseType] || null;
    }

    /**
     * Clear validation cache
     */
    clearCache() {
        this.validationCache.clear();
        console.log('LicenseValidator', '[LicenseValidator] Validation cache cleared', );
    }

    /**
     * Get device fingerprint
     * @returns {string|null} Device fingerprint
     */
    getDeviceFingerprint() {
        return this.deviceFingerprint?.getFingerprint() || null;
    }

    /**
     * Get device summary
     * @returns {Object} Device summary
     */
    getDeviceSummary() {
        return this.deviceFingerprint?.getDeviceSummary() || {};
    }
}

console.log('LicenseValidator', '[LicenseValidator] Class defined successfully', );