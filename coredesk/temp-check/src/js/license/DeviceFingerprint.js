/**
 * DeviceFingerprint.js
 * Generates unique device fingerprints for license validation
 * Combines hardware and system information to create stable device IDs
 */

class DeviceFingerprint {
    constructor() {
        this.fingerprint = null;
        this.components = new Map();
        this.isElectron = typeof window !== 'undefined' && window.electronAPI;
        
        this.initialize();
    }

    /**
     * Initialize the device fingerprint generator
     */
    async initialize() {
        console.log('DeviceFingerprint', '[DeviceFingerprint] Initializing device fingerprint generator...', );
        
        try {
            await this.collectFingerprints();
            this.fingerprint = await this.generateFingerprint();
            
            console.log('DeviceFingerprint', '[DeviceFingerprint] Device fingerprint generated successfully', );
            
        } catch (error) {
            console.error('DeviceFingerprint', '[DeviceFingerprint] Error generating fingerprint:', error);
            
            // Fallback fingerprint
            this.fingerprint = this.generateFallbackFingerprint();
        }
    }

    /**
     * Collect various device fingerprint components
     * @private
     */
    async collectFingerprints() {
        console.log('DeviceFingerprint', '[DeviceFingerprint] Collecting fingerprint components...', );
        
        // Hardware information (Electron-specific)
        if (this.isElectron) {
            await this.collectElectronFingerprints();
        }
        
        // Browser/System information
        await this.collectBrowserFingerprints();
        
        // Screen information
        this.collectScreenFingerprints();
        
        // Timezone information
        this.collectTimezoneFingerprints();
        
        // Language information
        this.collectLanguageFingerprints();
        
        console.log("Component", `[DeviceFingerprint] Collected ${this.components.size} fingerprint components`);
    }

    /**
     * Collect Electron-specific fingerprints
     * @private
     */
    async collectElectronFingerprints() {
        try {
            // Get system info via Electron API
            const systemInfo = await window.electronAPI.getSystemInfo();
            
            if (systemInfo) {
                this.components.set('os_platform', systemInfo.platform);
                this.components.set('os_release', systemInfo.release);
                this.components.set('os_arch', systemInfo.arch);
                this.components.set('cpu_model', systemInfo.cpuModel);
                this.components.set('total_memory', systemInfo.totalMemory);
                this.components.set('home_dir', systemInfo.homeDir);
                this.components.set('machine_id', systemInfo.machineId);
            }
            
        } catch (error) {
            console.warn('DeviceFingerprint', '[DeviceFingerprint] Could not collect Electron fingerprints:', error);
        }
    }

    /**
     * Collect browser-based fingerprints
     * @private
     */
    async collectBrowserFingerprints() {
        try {
            // User Agent
            this.components.set('user_agent', navigator.userAgent);
            
            // Platform
            this.components.set('platform', navigator.platform);
            
            // Hardware concurrency (CPU cores)
            if ('hardwareConcurrency' in navigator) {
                this.components.set('cpu_cores', navigator.hardwareConcurrency);
            }
            
            // Device memory
            if ('deviceMemory' in navigator) {
                this.components.set('device_memory', navigator.deviceMemory);
            }
            
            // Canvas fingerprint
            const canvasFingerprint = this.generateCanvasFingerprint();
            if (canvasFingerprint) {
                this.components.set('canvas', canvasFingerprint);
            }
            
            // WebGL fingerprint
            const webglFingerprint = this.generateWebGLFingerprint();
            if (webglFingerprint) {
                this.components.set('webgl', webglFingerprint);
            }
            
        } catch (error) {
            console.warn('DeviceFingerprint', '[DeviceFingerprint] Error collecting browser fingerprints:', error);
        }
    }

    /**
     * Collect screen-based fingerprints
     * @private
     */
    collectScreenFingerprints() {
        try {
            this.components.set('screen_width', screen.width);
            this.components.set('screen_height', screen.height);
            this.components.set('screen_color_depth', screen.colorDepth);
            this.components.set('screen_pixel_depth', screen.pixelDepth);
            this.components.set('screen_available_width', screen.availWidth);
            this.components.set('screen_available_height', screen.availHeight);
            
        } catch (error) {
            console.warn('DeviceFingerprint', '[DeviceFingerprint] Error collecting screen fingerprints:', error);
        }
    }

    /**
     * Collect timezone fingerprints
     * @private
     */
    collectTimezoneFingerprints() {
        try {
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            this.components.set('timezone', timezone);
            
            const timezoneOffset = new Date().getTimezoneOffset();
            this.components.set('timezone_offset', timezoneOffset);
            
        } catch (error) {
            console.warn('DeviceFingerprint', '[DeviceFingerprint] Error collecting timezone fingerprints:', error);
        }
    }

    /**
     * Collect language fingerprints
     * @private
     */
    collectLanguageFingerprints() {
        try {
            this.components.set('language', navigator.language);
            this.components.set('languages', navigator.languages?.join(',') || '');
            
        } catch (error) {
            console.warn('DeviceFingerprint', '[DeviceFingerprint] Error collecting language fingerprints:', error);
        }
    }

    /**
     * Generate canvas fingerprint
     * @returns {string|null} Canvas fingerprint
     * @private
     */
    generateCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (!ctx) return null;
            
            canvas.width = 200;
            canvas.height = 50;
            
            // Draw some shapes and text
            ctx.textBaseline = 'alphabetic';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            
            ctx.fillStyle = '#069';
            ctx.font = '11pt Arial';
            ctx.fillText('CoreDesk Fingerprint', 2, 15);
            
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.font = '18pt Arial';
            ctx.fillText('Device ID', 4, 45);
            
            // Generate hash from canvas data
            const dataURL = canvas.toDataURL();
            return this.simpleHash(dataURL);
            
        } catch (error) {
            console.warn('DeviceFingerprint', '[DeviceFingerprint] Canvas fingerprint failed:', error);
            return null;
        }
    }

    /**
     * Generate WebGL fingerprint
     * @returns {string|null} WebGL fingerprint
     * @private
     */
    generateWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) return null;
            
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            if (!debugInfo) return null;
            
            const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
            
            return this.simpleHash(`${vendor}|${renderer}`);
            
        } catch (error) {
            console.warn('DeviceFingerprint', '[DeviceFingerprint] WebGL fingerprint failed:', error);
            return null;
        }
    }

    /**
     * Generate the final device fingerprint
     * @returns {Promise<string>} Device fingerprint
     * @private
     */
    async generateFingerprint() {
        // Combine all components into a single string
        const componentStrings = [];
        
        // Sort components by key for consistency
        const sortedComponents = Array.from(this.components.entries()).sort((a, b) => a[0].localeCompare(b[0]));
        
        for (const [key, value] of sortedComponents) {
            if (value !== null && value !== undefined) {
                componentStrings.push(`${key}:${String(value)}`);
            }
        }
        
        const combinedString = componentStrings.join('|');
        
        // Generate hash
        const fingerprint = await this.generateHash(combinedString);
        
        console.log("Component", `[DeviceFingerprint] Generated fingerprint: ${fingerprint.substring(0, 16)}...`);
        
        return fingerprint;
    }

    /**
     * Generate fallback fingerprint when normal generation fails
     * @returns {string} Fallback fingerprint
     * @private
     */
    generateFallbackFingerprint() {
        const fallbackData = [
            navigator.userAgent || 'unknown',
            navigator.platform || 'unknown',
            screen.width || 0,
            screen.height || 0,
            new Date().getTimezoneOffset() || 0,
            navigator.language || 'unknown'
        ].join('|');
        
        const fallbackFingerprint = this.simpleHash(fallbackData);
        
        console.warn(`[DeviceFingerprint] Using fallback fingerprint: ${fallbackFingerprint.substring(0, 16)}...`);
        
        return fallbackFingerprint;
    }

    /**
     * Generate SHA-256 hash (using Web Crypto API if available)
     * @param {string} data - Data to hash
     * @returns {Promise<string>} Hash string
     * @private
     */
    async generateHash(data) {
        if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
            try {
                const encoder = new TextEncoder();
                const dataBuffer = encoder.encode(data);
                const hashBuffer = await window.crypto.subtle.digest('SHA-256', dataBuffer);
                
                // Convert to hex string
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                
            } catch (error) {
                console.warn('DeviceFingerprint', '[DeviceFingerprint] Web Crypto API failed, using simple hash:', error);
            }
        }
        
        // Fallback to simple hash
        return this.simpleHash(data);
    }

    /**
     * Simple hash function for fallback
     * @param {string} str - String to hash
     * @returns {string} Hash string
     * @private
     */
    simpleHash(str) {
        let hash = 0;
        
        if (str.length === 0) return hash.toString();
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        
        // Convert to positive hex string
        return Math.abs(hash).toString(16).padStart(8, '0');
    }

    /**
     * Get the current device fingerprint
     * @returns {string|null} Device fingerprint
     */
    getFingerprint() {
        return this.fingerprint;
    }

    /**
     * Get fingerprint components for debugging
     * @returns {Object} Fingerprint components
     */
    getComponents() {
        return Object.fromEntries(this.components);
    }

    /**
     * Validate if a fingerprint matches the current device
     * @param {string} storedFingerprint - Previously stored fingerprint
     * @returns {boolean} True if fingerprints match
     */
    validateFingerprint(storedFingerprint) {
        if (!this.fingerprint || !storedFingerprint) {
            return false;
        }
        
        return this.fingerprint === storedFingerprint;
    }

    /**
     * Generate a device summary for display
     * @returns {Object} Device summary
     */
    getDeviceSummary() {
        return {
            fingerprint: this.fingerprint ? `${this.fingerprint.substring(0, 8)}...` : 'Not generated',
            platform: this.components.get('os_platform') || this.components.get('platform') || 'Unknown',
            architecture: this.components.get('os_arch') || 'Unknown',
            cpuCores: this.components.get('cpu_cores') || 'Unknown',
            screenResolution: `${this.components.get('screen_width')}x${this.components.get('screen_height')}`,
            timezone: this.components.get('timezone') || 'Unknown',
            language: this.components.get('language') || 'Unknown',
            componentsCount: this.components.size
        };
    }

    /**
     * Refresh the device fingerprint
     * @returns {Promise<string>} New fingerprint
     */
    async refresh() {
        console.log('DeviceFingerprint', '[DeviceFingerprint] Refreshing device fingerprint...', );
        
        this.components.clear();
        await this.initialize();
        
        return this.fingerprint;
    }
}

// Create global instance for browser environment
if (typeof window !== 'undefined') {
    window.DeviceFingerprint = DeviceFingerprint;
    window.deviceFingerprint = new DeviceFingerprint();
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DeviceFingerprint;
}

console.log('DeviceFingerprint', '[DeviceFingerprint] Class defined successfully', );