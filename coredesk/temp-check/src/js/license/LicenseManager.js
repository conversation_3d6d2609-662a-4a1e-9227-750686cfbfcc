/**
 * LicenseManager.js
 * Main license management system implementing the trial flow from PRD
 * Coordinates license validation, activation, and trial management
 */

// licenseApiService will be available globally
// const { licenseApiService } = require('../services/api');

class LicenseManager {
    constructor() {
        this.licenseValidator = null;
        this.currentLicense = null;
        this.activationStatus = {
            isActivated: false,
            isPending: false,
            lastChecked: null,
            error: null
        };
        
        this.trialFlow = {
            step: 0, // 0: not started, 1: email, 2: verification, 3: activation, 4: complete
            email: null,
            verificationCode: null,
            trialKey: null,
            isComplete: false
        };
        
        this.storageKeys = {
            license: window.COREDESK_CONSTANTS?.STORAGE_KEYS?.LICENSE || 'coredesk_license',
            activation: window.COREDESK_CONSTANTS?.STORAGE_KEYS?.ACTIVATION || 'coredesk_activation',
            trialFlow: window.COREDESK_CONSTANTS?.STORAGE_KEYS?.TRIAL_FLOW || 'coredesk_trial_flow'
        };
        
        this.apiEndpoints = {
            requestTrial: '/api/v1/licenses/trial/request',
            verifyEmail: '/api/v1/licenses/trial/verify',
            generateTrial: '/api/v1/licenses/trial/generate',
            validateLicense: '/api/v1/licenses/validate'
        };
        
        this.initialize();
    }

    /**
     * Initialize the license manager
     */
    async initialize() {
        console.log('License', '[LicenseManager] Initializing...', );
        
        try {
            // Initialize license validator
            this.licenseValidator = new LicenseValidator();
            await this.licenseValidator.initialize();
            
            // Restore previous state
            this.restoreState();
            
            // Check existing activation
            await this.checkExistingActivation();
            
            console.log('License', '[LicenseManager] Initialized successfully', );
            
            // Emit initialization event
            window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.LICENSE_MANAGER_READY, {
                isActivated: this.activationStatus.isActivated,
                hasLicense: !!this.currentLicense
            });
            
        } catch (error) {
            console.error('License', '[LicenseManager] Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Start the trial license flow - Step 1: Email Collection
     * @param {string} email - User email
     * @returns {Promise<Object>} Flow result
     */
    async startTrialFlow(email) {
        console.log('License', '[LicenseManager] Starting trial flow - Step 1: Email Collection', );
        
        try {
            // Validate email format
            if (!this.isValidEmail(email)) {
                return { success: false, error: 'Invalid email format' };
            }
            
            // Check if email already has a trial
            const existingTrial = await this.checkExistingTrialByEmail(email);
            if (existingTrial) {
                return { success: false, error: 'Trial already requested for this email' };
            }
            
            // Update trial flow state
            this.trialFlow = {
                step: 1,
                email: email,
                verificationCode: null,
                trialKey: null,
                isComplete: false,
                startedAt: new Date().toISOString()
            };
            
            this.saveTrialFlowState();
            
            // Request trial from API
            const trialRequest = await this.requestTrialFromAPI(email);
            
            if (!trialRequest.success) {
                this.trialFlow.step = 0; // Reset on failure
                this.saveTrialFlowState();
                return { success: false, error: trialRequest.error };
            }
            
            // Move to step 2: Email Verification
            this.trialFlow.step = 2;
            this.trialFlow.verificationSentAt = new Date().toISOString();
            this.saveTrialFlowState();
            
            console.log('License', '[LicenseManager] Trial flow - Step 1 completed, moving to Step 2', );
            
            return {
                success: true,
                nextStep: 2,
                message: 'Verification email sent. Please check your inbox.',
                email: email
            };
            
        } catch (error) {
            console.error('License', '[LicenseManager] Trial flow Step 1 failed:', error);
            this.trialFlow.step = 0;
            this.saveTrialFlowState();
            return { success: false, error: error.message };
        }
    }

    /**
     * Continue trial flow - Step 2: Email Verification
     * @param {string} verificationCode - Verification code from email
     * @returns {Promise<Object>} Flow result
     */
    async verifyTrialEmail(verificationCode) {
        console.log('License', '[LicenseManager] Trial flow - Step 2: Email Verification', );
        
        try {
            if (this.trialFlow.step !== 2) {
                return { success: false, error: 'Invalid trial flow state' };
            }
            
            if (!verificationCode) {
                return { success: false, error: 'Verification code is required' };
            }
            
            // Verify code with API
            const verification = await this.verifyEmailWithAPI(this.trialFlow.email, verificationCode);
            
            if (!verification.success) {
                return { success: false, error: verification.error };
            }
            
            // Update trial flow state
            this.trialFlow.step = 3;
            this.trialFlow.verificationCode = verificationCode;
            this.trialFlow.verifiedAt = new Date().toISOString();
            this.saveTrialFlowState();
            
            console.log('License', '[LicenseManager] Trial flow - Step 2 completed, moving to Step 3', );
            
            return {
                success: true,
                nextStep: 3,
                message: 'Email verified successfully. Ready to generate trial license.',
                email: this.trialFlow.email
            };
            
        } catch (error) {
            console.error('License', '[LicenseManager] Trial flow Step 2 failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Continue trial flow - Step 3: Trial License Generation
     * @returns {Promise<Object>} Flow result
     */
    async generateTrialLicense() {
        console.log('License', '[LicenseManager] Trial flow - Step 3: Trial License Generation', );
        
        try {
            if (this.trialFlow.step !== 3) {
                return { success: false, error: 'Invalid trial flow state' };
            }
            
            // Generate device fingerprint
            const deviceFingerprint = this.licenseValidator.getDeviceFingerprint();
            if (!deviceFingerprint) {
                return { success: false, error: 'Could not generate device fingerprint' };
            }
            
            // Request trial license generation
            const trialGeneration = await this.generateTrialWithAPI({
                email: this.trialFlow.email,
                verificationCode: this.trialFlow.verificationCode,
                deviceFingerprint: deviceFingerprint,
                deviceInfo: this.licenseValidator.getDeviceSummary()
            });
            
            if (!trialGeneration.success) {
                return { success: false, error: trialGeneration.error };
            }
            
            // Update trial flow state
            this.trialFlow.step = 4;
            this.trialFlow.trialKey = trialGeneration.licenseKey;
            this.trialFlow.generatedAt = new Date().toISOString();
            this.saveTrialFlowState();
            
            console.log('License', '[LicenseManager] Trial flow - Step 3 completed, moving to Step 4', );
            
            return {
                success: true,
                nextStep: 4,
                licenseKey: trialGeneration.licenseKey,
                message: 'Trial license generated successfully. Ready for activation.',
                email: this.trialFlow.email
            };
            
        } catch (error) {
            console.error('License', '[LicenseManager] Trial flow Step 3 failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Complete trial flow - Step 4: License Activation
     * @returns {Promise<Object>} Flow result
     */
    async activateTrialLicense() {
        console.log('License', '[LicenseManager] Trial flow - Step 4: License Activation', );
        
        try {
            if (this.trialFlow.step !== 4 || !this.trialFlow.trialKey) {
                return { success: false, error: 'Invalid trial flow state' };
            }
            
            // Activate the trial license
            const activation = await this.activateLicense(this.trialFlow.trialKey, {
                userEmail: this.trialFlow.email,
                deviceName: 'Trial Device'
            });
            
            if (!activation.success) {
                return { success: false, error: activation.error };
            }
            
            // Complete trial flow
            this.trialFlow.isComplete = true;
            this.trialFlow.activatedAt = new Date().toISOString();
            this.saveTrialFlowState();
            
            console.log('License', '[LicenseManager] Trial flow completed successfully', );
            
            // Emit trial completed event
            window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.TRIAL_COMPLETED, {
                email: this.trialFlow.email,
                licenseKey: this.trialFlow.trialKey,
                activatedAt: this.trialFlow.activatedAt
            });
            
            return {
                success: true,
                message: 'Trial license activated successfully. Welcome to CoreDesk!',
                license: activation.license,
                trialInfo: {
                    email: this.trialFlow.email,
                    licenseKey: this.trialFlow.trialKey,
                    remainingDays: 30
                }
            };
            
        } catch (error) {
            console.error('License', '[LicenseManager] Trial flow Step 4 failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Activate a license (trial or regular)
     * @param {string} licenseKey - License key
     * @param {Object} options - Activation options
     * @returns {Promise<Object>} Activation result
     */
    async activateLicense(licenseKey, options = {}) {
        console.log('License', '[LicenseManager] Activating license...', );
        
        try {
            this.activationStatus.isPending = true;
            
            // Validate and activate through validator
            const result = await this.licenseValidator.activateLicense(licenseKey, options);
            
            if (result.success) {
                // Store license information
                this.currentLicense = result.license;
                this.activationStatus = {
                    isActivated: true,
                    isPending: false,
                    lastChecked: new Date().toISOString(),
                    error: null
                };
                
                // Save to localStorage
                this.saveLicenseState();
                
                // Emit activation event
                window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.LICENSE_ACTIVATED, {
                    license: this.currentLicense,
                    activation: result.activation
                });
                
                console.log('License', '[LicenseManager] License activated successfully', );
            } else {
                this.activationStatus = {
                    isActivated: false,
                    isPending: false,
                    lastChecked: new Date().toISOString(),
                    error: result.error
                };
            }
            
            return result;
            
        } catch (error) {
            console.error('License', '[LicenseManager] License activation failed:', error);
            
            this.activationStatus = {
                isActivated: false,
                isPending: false,
                lastChecked: new Date().toISOString(),
                error: error.message
            };
            
            return { success: false, error: error.message };
        }
    }

    /**
     * Check existing license activation
     * @returns {Promise<boolean>} True if activated
     * @private
     */
    async checkExistingActivation() {
        console.log('License', '[LicenseManager] Checking existing activation...', );
        
        try {
            // Load stored license
            const storedLicense = localStorage.getItem(this.storageKeys.license);
            if (!storedLicense) {
                console.log('License', '[LicenseManager] No stored license found', );
                return false;
            }
            
            const licenseData = JSON.parse(storedLicense);
            
            // Validate stored license
            const validation = await this.licenseValidator.validateLicense(licenseData.key, {
                useCache: false
            });
            
            if (validation.isValid) {
                this.currentLicense = validation.license;
                this.activationStatus = {
                    isActivated: true,
                    isPending: false,
                    lastChecked: new Date().toISOString(),
                    error: null
                };
                
                console.log('License', '[LicenseManager] Existing license validated successfully', );
                return true;
            } else {
                // Remove invalid license
                localStorage.removeItem(this.storageKeys.license);
                console.log('License', '[LicenseManager] Stored license is invalid', );
                return false;
            }
            
        } catch (error) {
            console.error('License', '[LicenseManager] Error checking existing activation:', error);
            return false;
        }
    }

    /**
     * Request trial from API
     * @param {string} email - User email
     * @returns {Promise<Object>} API result
     * @private
     */
    async requestTrialFromAPI(email) {
        try {
            console.log("Component", `[LicenseManager] Requesting trial for email: ${email}`);
            
            // Get device fingerprint
            const deviceFingerprint = this.licenseValidator.getDeviceFingerprint();
            
            // Check eligibility first
            const eligibilityResult = await licenseApiService.checkTrialEligibility(deviceFingerprint);
            
            if (!eligibilityResult.success || !eligibilityResult.eligible) {
                return { 
                    success: false, 
                    error: eligibilityResult.reason || 'Not eligible for trial' 
                };
            }
            
            // Request trial
            const result = await licenseApiService.requestTrial(email, deviceFingerprint);
            
            if (!result.success) {
                return { 
                    success: false, 
                    error: result.error || 'Failed to request trial' 
                };
            }
            
            // Store request ID for next step
            this.trialFlow.requestId = result.requestId;
            
            return {
                success: true,
                message: result.message || 'Trial request submitted successfully',
                email: email,
                requestId: result.requestId
            };
            
        } catch (error) {
            console.error('License', '[LicenseManager] API request failed:', error);
            return { success: false, error: error.message || 'Network error' };
        }
    }

    /**
     * Verify email with API
     * @param {string} email - User email
     * @param {string} code - Verification code
     * @returns {Promise<Object>} API result
     * @private
     */
    async verifyEmailWithAPI(email, code) {
        try {
            console.log("Component", `[LicenseManager] Verifying email: ${email} with code: ${code}`);
            
            // Validate code format
            if (!/^\d{6}$/.test(code)) {
                return { success: false, error: 'Invalid verification code format' };
            }
            
            // Verify with API
            const result = await licenseApiService.verifyEmail(
                email, 
                code, 
                this.trialFlow.requestId
            );
            
            if (!result.success) {
                return { 
                    success: false, 
                    error: result.error || 'Verification failed' 
                };
            }
            
            // Store verification token for next step
            this.trialFlow.verificationToken = result.token;
            
            return {
                success: true,
                message: 'Email verified successfully',
                email: email,
                token: result.token
            };
            
        } catch (error) {
            console.error('License', '[LicenseManager] Email verification failed:', error);
            return { success: false, error: error.message || 'Verification failed' };
        }
    }

    /**
     * Generate trial license with API
     * @param {Object} trialData - Trial generation data
     * @returns {Promise<Object>} API result
     * @private
     */
    async generateTrialWithAPI(trialData) {
        try {
            console.log('License', '[LicenseManager] Generating trial license...', );
            
            // Add verification token from previous step
            const generationData = {
                email: trialData.email,
                token: this.trialFlow.verificationToken,
                deviceFingerprint: trialData.deviceFingerprint,
                requestId: this.trialFlow.requestId
            };
            
            // Generate trial with API
            const result = await licenseApiService.generateTrial(generationData);
            
            if (!result.success) {
                return { 
                    success: false, 
                    error: result.error || 'License generation failed' 
                };
            }
            
            // Store license locally
            const stored = await this.storeTrialLicense({
                licenseKey: result.licenseKey,
                email: trialData.email,
                deviceFingerprint: trialData.deviceFingerprint,
                deviceInfo: trialData.deviceInfo,
                expiresAt: result.expiresAt
            });
            
            if (!stored.success) {
                console.error('License', '[LicenseManager] Failed to store license locally:', stored.error);
            }
            
            return {
                success: true,
                licenseKey: result.licenseKey,
                expiresAt: result.expiresAt,
                message: 'Trial license generated successfully'
            };
            
        } catch (error) {
            console.error('License', '[LicenseManager] Trial generation failed:', error);
            return { success: false, error: error.message || 'License generation failed' };
        }
    }

    /**
     * Generate a trial license key (deprecated - now handled by backend)
     * @returns {string} Trial license key
     * @private
     * @deprecated
     */
    generateTrialLicenseKey() {
        // This method is kept for backward compatibility
        // License keys are now generated by the backend
        console.warn('[LicenseManager] generateTrialLicenseKey is deprecated');
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        const segments = [];
        
        for (let i = 0; i < 4; i++) {
            let segment = '';
            for (let j = 0; j < 4; j++) {
                segment += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            segments.push(segment);
        }
        
        return segments.join('-');
    }

    /**
     * Store trial license in database
     * @param {Object} licenseData - License data
     * @returns {Promise<Object>} Storage result
     * @private
     */
    async storeTrialLicense(licenseData) {
        try {
            if (window.electronAPI && window.electronAPI.executeQuery) {
                const expirationDate = new Date();
                const expiresAt = licenseData.expiresAt || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();
                
                await window.electronAPI.executeQuery(
                    `INSERT INTO licenses (
                        license_key, type, email, status, 
                        created_at, expires_at, activation_count, device_fingerprint
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        licenseData.licenseKey,
                        'trial',
                        licenseData.email,
                        'active',
                        new Date().toISOString(),
                        expiresAt,
                        0,
                        licenseData.deviceFingerprint
                    ]
                );
                
                return { success: true };
            }
            
            return { success: false, error: 'Database not available' };
            
        } catch (error) {
            console.error('License', '[LicenseManager] Failed to store trial license:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Check if email already has a trial
     * @param {string} email - Email to check
     * @returns {Promise<Object|null>} Existing trial or null
     * @private
     */
    async checkExistingTrialByEmail(email) {
        try {
            if (window.electronAPI && window.electronAPI.executeQuery) {
                const result = await window.electronAPI.executeQuery(
                    'SELECT * FROM licenses WHERE email = ? AND type = "trial"',
                    [email]
                );
                
                return result.length > 0 ? result[0] : null;
            }
            
            return null;
            
        } catch (error) {
            console.error('License', '[LicenseManager] Failed to check existing trial:', error);
            return null;
        }
    }

    /**
     * Validate email format
     * @param {string} email - Email to validate
     * @returns {boolean} True if valid
     * @private
     */
    isValidEmail(email) {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailPattern.test(email);
    }

    /**
     * Save license state to localStorage
     * @private
     */
    saveLicenseState() {
        if (this.currentLicense) {
            localStorage.setItem(this.storageKeys.license, JSON.stringify(this.currentLicense));
        }
        
        localStorage.setItem(this.storageKeys.activation, JSON.stringify(this.activationStatus));
    }

    /**
     * Save trial flow state to localStorage
     * @private
     */
    saveTrialFlowState() {
        localStorage.setItem(this.storageKeys.trialFlow, JSON.stringify(this.trialFlow));
    }

    /**
     * Restore state from localStorage
     * @private
     */
    restoreState() {
        try {
            // Restore activation status
            const storedActivation = localStorage.getItem(this.storageKeys.activation);
            if (storedActivation) {
                this.activationStatus = JSON.parse(storedActivation);
            }
            
            // Restore trial flow
            const storedTrialFlow = localStorage.getItem(this.storageKeys.trialFlow);
            if (storedTrialFlow) {
                this.trialFlow = JSON.parse(storedTrialFlow);
            }
            
        } catch (error) {
            console.error('License', '[LicenseManager] Error restoring state:', error);
        }
    }

    /**
     * Get current license status
     * @returns {Object} License status
     */
    getLicenseStatus() {
        return {
            isActivated: this.activationStatus.isActivated,
            isPending: this.activationStatus.isPending,
            license: this.currentLicense,
            activation: this.activationStatus,
            trialFlow: this.trialFlow,
            deviceFingerprint: this.licenseValidator?.getDeviceFingerprint(),
            deviceSummary: this.licenseValidator?.getDeviceSummary()
        };
    }

    /**
     * Get trial flow status
     * @returns {Object} Trial flow status
     */
    getTrialFlowStatus() {
        return {
            ...this.trialFlow,
            canContinue: this.canContinueTrialFlow(),
            nextAction: this.getNextTrialAction()
        };
    }

    /**
     * Check if trial flow can continue
     * @returns {boolean} True if can continue
     * @private
     */
    canContinueTrialFlow() {
        return this.trialFlow.step > 0 && this.trialFlow.step < 4 && !this.trialFlow.isComplete;
    }

    /**
     * Get next trial action
     * @returns {string} Next action
     * @private
     */
    getNextTrialAction() {
        switch (this.trialFlow.step) {
            case 0: return 'start';
            case 1: return 'verify-email';
            case 2: return 'enter-verification-code';
            case 3: return 'generate-license';
            case 4: return 'activate-license';
            default: return 'complete';
        }
    }

    /**
     * Reset trial flow
     */
    resetTrialFlow() {
        this.trialFlow = {
            step: 0,
            email: null,
            verificationCode: null,
            trialKey: null,
            isComplete: false
        };
        
        this.saveTrialFlowState();
        console.log('License', '[LicenseManager] Trial flow reset', );
    }

    /**
     * Deactivate current license
     * @returns {Promise<Object>} Deactivation result
     */
    async deactivateLicense() {
        try {
            // Clear current license
            this.currentLicense = null;
            this.activationStatus = {
                isActivated: false,
                isPending: false,
                lastChecked: new Date().toISOString(),
                error: null
            };
            
            // Clear storage
            localStorage.removeItem(this.storageKeys.license);
            localStorage.removeItem(this.storageKeys.activation);
            
            // Emit deactivation event
            window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.LICENSE_DEACTIVATED, {
                timestamp: new Date().toISOString()
            });
            
            console.log('License', '[LicenseManager] License deactivated', );
            
            return { success: true, message: 'License deactivated successfully' };
            
        } catch (error) {
            console.error('License', '[LicenseManager] License deactivation failed:', error);
            return { success: false, error: error.message };
        }
    }
}

// Create global instance
window.licenseManager = new LicenseManager();

console.log('License', '[LicenseManager] Class defined and global instance created successfully', );