/**
 * EventManager.js
 * Manages application-wide event handling and coordination
 */

class EventManager {
    constructor(logger) {
        this.logger = logger;
        this.eventListeners = new Map();
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.setupLicenseEvents();
        this.setupSyncEvents();
        this.setupAppEvents();
        this.setupUIEvents();
    }

    setupLicenseEvents() {
        // License activation events
        document.addEventListener('license:activation:requested', (event) => {
            this.handleLicenseActivationRequested(event.detail);
        });

        document.addEventListener('license:activated', (event) => {
            this.handleLicenseActivated(event.detail);
        });

        document.addEventListener('license:expired', (event) => {
            this.handleLicenseExpired(event.detail);
        });

        document.addEventListener('license:trial:started', (event) => {
            this.handleTrialStarted(event.detail);
        });
    }

    setupSyncEvents() {
        document.addEventListener('sync:started', (event) => {
            this.handleSyncStarted(event.detail);
        });

        document.addEventListener('sync:completed', (event) => {
            this.handleSyncCompleted(event.detail);
        });

        document.addEventListener('sync:error', (event) => {
            this.handleSyncError(event.detail);
        });

        document.addEventListener('sync:status:changed', (event) => {
            this.handleSyncStatusChanged(event.detail);
        });
    }

    setupAppEvents() {
        // Application lifecycle events
        document.addEventListener('app:ready', (event) => {
            this.handleAppReady(event.detail);
        });

        document.addEventListener('app:error', (event) => {
            this.handleAppError(event.detail);
        });

        // Module events
        document.addEventListener('module:activated', (event) => {
            this.handleModuleActivated(event.detail);
        });

        document.addEventListener('module:deactivated', (event) => {
            this.handleModuleDeactivated(event.detail);
        });
    }

    setupUIEvents() {
        // UI state events
        document.addEventListener('ui:loading:show', (event) => {
            this.handleShowLoading(event.detail);
        });

        document.addEventListener('ui:loading:hide', () => {
            this.handleHideLoading();
        });

        document.addEventListener('ui:notification:show', (event) => {
            this.handleShowNotification(event.detail);
        });
    }

    // License event handlers
    handleLicenseActivationRequested(data) {
        try {
            this.logger.info('EventManager', 'License activation requested', data);
            
            if (window.licenseManager) {
                window.licenseManager.activateLicense(data);
            }
        } catch (error) {
            this.logger.error('EventManager', 'Error handling license activation request', error);
        }
    }

    handleLicenseActivated(licenseData) {
        try {
            this.logger.info('EventManager', 'License activated', licenseData);
            
            // Update UI to show licensed state
            this.emitEvent('ui:notification:show', {
                message: 'Licencia activada exitosamente',
                type: 'success'
            });

            // Enable premium features
            if (window.coreDesk && window.coreDesk.licenseFlowManager) {
                window.coreDesk.licenseFlowManager.onLicenseActivated(licenseData);
            }

        } catch (error) {
            this.logger.error('EventManager', 'Error handling license activation', error);
        }
    }

    handleLicenseExpired(data) {
        try {
            this.logger.warn('EventManager', 'License expired', data);
            
            this.emitEvent('ui:notification:show', {
                message: 'Su licencia ha expirado. Por favor, renueve su licencia.',
                type: 'warning'
            });

        } catch (error) {
            this.logger.error('EventManager', 'Error handling license expiration', error);
        }
    }

    handleTrialStarted(data) {
        try {
            this.logger.info('EventManager', 'Trial started', data);
            
            this.emitEvent('ui:notification:show', {
                message: `Período de prueba iniciado. ${data.daysRemaining} días restantes.`,
                type: 'info'
            });

        } catch (error) {
            this.logger.error('EventManager', 'Error handling trial start', error);
        }
    }

    // Sync event handlers
    handleSyncStarted(data) {
        try {
            this.logger.info('EventManager', 'Sync started', data);
            
            if (window.syncStatusPanel) {
                window.syncStatusPanel.updateStatus('syncing');
            }

        } catch (error) {
            this.logger.error('EventManager', 'Error handling sync start', error);
        }
    }

    handleSyncCompleted(data) {
        try {
            this.logger.info('EventManager', 'Sync completed', data);
            
            if (window.syncStatusPanel) {
                window.syncStatusPanel.updateStatus('completed');
            }

            if (data.showNotification) {
                this.emitEvent('ui:notification:show', {
                    message: 'Sincronización completada exitosamente',
                    type: 'success'
                });
            }

        } catch (error) {
            this.logger.error('EventManager', 'Error handling sync completion', error);
        }
    }

    handleSyncError(error) {
        try {
            this.logger.error('EventManager', 'Sync error occurred', error);
            
            if (window.syncStatusPanel) {
                window.syncStatusPanel.updateStatus('error');
            }

            this.emitEvent('ui:notification:show', {
                message: `Error de sincronización: ${error.message}`,
                type: 'error'
            });

        } catch (err) {
            this.logger.error('EventManager', 'Error handling sync error', err);
        }
    }

    handleSyncStatusChanged(status) {
        try {
            this.logger.debug('EventManager', 'Sync status changed', status);
            
            if (window.syncStatusPanel) {
                window.syncStatusPanel.updateStatus(status.state, status.data);
            }

        } catch (error) {
            this.logger.error('EventManager', 'Error handling sync status change', error);
        }
    }

    // App event handlers
    handleAppReady(data) {
        try {
            this.logger.info('EventManager', 'App ready', data);
            
            this.emitEvent('ui:loading:hide');

        } catch (error) {
            this.logger.error('EventManager', 'Error handling app ready', error);
        }
    }

    handleAppError(error) {
        try {
            this.logger.error('EventManager', 'App error occurred', error);
            
            this.emitEvent('ui:notification:show', {
                message: `Error de aplicación: ${error.message}`,
                type: 'error'
            });

        } catch (err) {
            this.logger.error('EventManager', 'Error handling app error', err);
        }
    }

    handleModuleActivated(moduleData) {
        try {
            this.logger.info('EventManager', 'Module activated', moduleData);
            
            // Update UI to reflect active module
            if (window.coreDesk && window.coreDesk.uiManager) {
                window.coreDesk.uiManager.showNotification(
                    `Módulo ${moduleData.name} activado`,
                    'info',
                    3000
                );
            }

        } catch (error) {
            this.logger.error('EventManager', 'Error handling module activation', error);
        }
    }

    handleModuleDeactivated(moduleData) {
        try {
            this.logger.info('EventManager', 'Module deactivated', moduleData);

        } catch (error) {
            this.logger.error('EventManager', 'Error handling module deactivation', error);
        }
    }

    // UI event handlers
    handleShowLoading(data) {
        try {
            if (window.coreDesk && window.coreDesk.uiManager) {
                window.coreDesk.uiManager.showLoading(data.message);
            }
        } catch (error) {
            this.logger.error('EventManager', 'Error showing loading', error);
        }
    }

    handleHideLoading() {
        try {
            if (window.coreDesk && window.coreDesk.uiManager) {
                window.coreDesk.uiManager.hideLoading();
            }
        } catch (error) {
            this.logger.error('EventManager', 'Error hiding loading', error);
        }
    }

    handleShowNotification(data) {
        try {
            if (window.coreDesk && window.coreDesk.uiManager) {
                window.coreDesk.uiManager.showNotification(
                    data.message,
                    data.type || 'info',
                    data.duration || 5000
                );
            }
        } catch (error) {
            this.logger.error('EventManager', 'Error showing notification', error);
        }
    }

    // Utility methods
    emitEvent(eventName, data = null) {
        try {
            const event = new CustomEvent(eventName, { detail: data });
            document.dispatchEvent(event);
            this.logger.debug('EventManager', 'Event emitted', { eventName, data });
        } catch (error) {
            this.logger.error('EventManager', 'Error emitting event', error);
        }
    }

    addEventListener(eventName, handler) {
        try {
            document.addEventListener(eventName, handler);
            
            // Track for cleanup
            if (!this.eventListeners.has(eventName)) {
                this.eventListeners.set(eventName, new Set());
            }
            this.eventListeners.get(eventName).add(handler);

        } catch (error) {
            this.logger.error('EventManager', 'Error adding event listener', error);
        }
    }

    removeEventListener(eventName, handler) {
        try {
            document.removeEventListener(eventName, handler);
            
            // Remove from tracking
            if (this.eventListeners.has(eventName)) {
                this.eventListeners.get(eventName).delete(handler);
            }

        } catch (error) {
            this.logger.error('EventManager', 'Error removing event listener', error);
        }
    }

    cleanup() {
        try {
            // Remove all tracked event listeners
            for (const [eventName, handlers] of this.eventListeners) {
                for (const handler of handlers) {
                    document.removeEventListener(eventName, handler);
                }
            }
            
            this.eventListeners.clear();
            this.logger.info('EventManager', 'Event cleanup completed');

        } catch (error) {
            this.logger.error('EventManager', 'Error during cleanup', error);
        }
    }
}

module.exports = EventManager;