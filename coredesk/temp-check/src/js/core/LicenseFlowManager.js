/**
 * LicenseFlowManager.js
 * Manages license activation flow and related UI interactions
 */

class LicenseFlowManager {
    constructor(logger) {
        this.logger = logger;
        this.currentLicense = null;
        this.licenseCheckInterval = null;
    }

    async checkExistingLicense() {
        try {
            this.logger.info('LicenseFlowManager', 'Checking existing license...');

            if (!window.licenseManager) {
                throw new Error('License manager not available');
            }

            const licenseStatus = window.licenseManager.getLicenseStatus();
            
            if (licenseStatus.isActivated) {
                this.currentLicense = licenseStatus.license;
                this.logger.info('LicenseFlowManager', 'Valid license found');
                this.onLicenseValid(licenseStatus);
            } else {
                this.logger.info('LicenseFlowManager', 'No valid license found, starting trial flow');
                this.startTrialFlow();
            }

        } catch (error) {
            this.logger.error('LicenseFlowManager', 'Error checking license', error);
            this.startTrialFlow();
        }
    }

    startTrialFlow() {
        try {
            this.logger.info('LicenseFlowManager', 'Starting trial flow');

            if (window.licenseActivationModal) {
                window.licenseActivationModal.show('trial');
            } else {
                this.logger.error('LicenseFlowManager', 'License activation modal not available');
            }

        } catch (error) {
            this.logger.error('LicenseFlowManager', 'Error starting trial flow', error);
        }
    }

    onLicenseValid(licenseStatus) {
        try {
            this.currentLicense = licenseStatus.license;
            
            // Enable full application features
            this.enableFeatures(licenseStatus.features || []);
            
            // Update UI to show licensed state
            this.updateLicenseUI(licenseStatus);
            
            // Start license monitoring
            this.startLicenseMonitoring();

        } catch (error) {
            this.logger.error('LicenseFlowManager', 'Error handling valid license', error);
        }
    }

    onLicenseActivated(licenseData) {
        try {
            this.logger.info('LicenseFlowManager', 'License activated successfully');
            
            this.currentLicense = licenseData;
            this.enableFeatures(licenseData.features || []);
            this.updateLicenseUI(licenseData);
            this.startLicenseMonitoring();

        } catch (error) {
            this.logger.error('LicenseFlowManager', 'Error handling license activation', error);
        }
    }

    enableFeatures(features) {
        try {
            this.logger.info('LicenseFlowManager', 'Enabling features', { features });

            // Enable modules based on license features
            if (window.exclusiveModuleController) {
                features.forEach(feature => {
                    window.exclusiveModuleController.enableModule(feature);
                });
            }

            // Update sync service if premium features are available
            if (features.includes('sync') && window.dataSyncService) {
                window.dataSyncService.enableCloudSync();
            }

        } catch (error) {
            this.logger.error('LicenseFlowManager', 'Error enabling features', error);
        }
    }

    updateLicenseUI(licenseData) {
        try {
            // Update status display
            const statusElement = document.querySelector('.license-status');
            if (statusElement) {
                statusElement.textContent = this.formatLicenseStatus(licenseData);
                statusElement.className = `license-status ${licenseData.type}`;
            }

            // Update app title
            const titleElement = document.querySelector('.app-title');
            if (titleElement) {
                const suffix = licenseData.type === 'trial' ? ' (Trial)' : '';
                titleElement.textContent = `CoreDesk${suffix}`;
            }

        } catch (error) {
            this.logger.error('LicenseFlowManager', 'Error updating license UI', error);
        }
    }

    startLicenseMonitoring() {
        // Clear existing interval
        if (this.licenseCheckInterval) {
            clearInterval(this.licenseCheckInterval);
        }

        // Check license status every hour
        this.licenseCheckInterval = setInterval(() => {
            this.validateCurrentLicense();
        }, 3600000); // 1 hour
    }

    async validateCurrentLicense() {
        try {
            if (!this.currentLicense || !window.licenseManager) {
                return;
            }

            const isValid = await window.licenseManager.validateLicense(
                this.currentLicense.key,
                this.currentLicense.deviceFingerprint
            );

            if (!isValid) {
                this.handleLicenseExpired();
            }

        } catch (error) {
            this.logger.error('LicenseFlowManager', 'License validation failed', error);
        }
    }

    handleLicenseExpired() {
        try {
            this.logger.warn('LicenseFlowManager', 'License expired or invalid');

            // Clear current license
            this.currentLicense = null;

            // Show activation modal
            if (window.licenseActivationModal) {
                window.licenseActivationModal.show('expired');
            }

            // Disable premium features
            this.disablePremiumFeatures();

        } catch (error) {
            this.logger.error('LicenseFlowManager', 'Error handling expired license', error);
        }
    }

    disablePremiumFeatures() {
        try {
            // Disable cloud sync
            if (window.dataSyncService) {
                window.dataSyncService.disableCloudSync();
            }

            // Disable premium modules
            if (window.exclusiveModuleController) {
                window.exclusiveModuleController.disablePremiumModules();
            }

        } catch (error) {
            this.logger.error('LicenseFlowManager', 'Error disabling premium features', error);
        }
    }

    formatLicenseStatus(licenseData) {
        const typeNames = {
            trial: 'Versión de Prueba',
            standard: 'Licencia Estándar',
            professional: 'Licencia Profesional',
            enterprise: 'Licencia Empresarial'
        };

        return typeNames[licenseData.type] || 'Licencia Desconocida';
    }

    getCurrentLicense() {
        return this.currentLicense;
    }

    cleanup() {
        if (this.licenseCheckInterval) {
            clearInterval(this.licenseCheckInterval);
            this.licenseCheckInterval = null;
        }
    }
}

module.exports = LicenseFlowManager;