/**
 * SimplifiedTabSystem.js
 * Core tab management system for CoreDesk Framework
 * Handles tab creation, switching, and lifecycle management
 */

class SimplifiedTabSystem {
    constructor() {
        this.tabs = new Map();
        this.activeTabId = null;
        this.tabContainer = null;
        this.contentContainer = null;
        this.nextTabId = 1;
        this.maxTabs = 20;
        
        this.initialize();
    }

    /**
     * Initialize the tab system
     */
    initialize() {
        this.logger.info('TabSystem', '[SimplifiedTabSystem] Initializing...', );
        
        this.setupContainers();
        this.setupEventListeners();
        
        this.logger.info('TabSystem', '[SimplifiedTabSystem] Initialized successfully', );
    }

    /**
     * Setup tab containers
     */
    setupContainers() {
        this.tabContainer = document.querySelector('.tab-bar');
        this.contentContainer = document.querySelector('.tab-content-area');
        
        if (!this.tabContainer || !this.contentContainer) {
            this.logger.error('TabSystem', '[SimplifiedTabSystem] Required containers not found', );
            return;
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for tab events
        document.addEventListener('tab:create', (event) => {
            this.createTab(event.detail);
        });
        
        document.addEventListener('tab:close', (event) => {
            this.closeTab(event.detail.tabId);
        });
        
        document.addEventListener('tab:switch', (event) => {
            this.switchToTab(event.detail.tabId);
        });
    }

    /**
     * Create a new tab
     */
    createTab(options = {}) {
        if (this.tabs.size >= this.maxTabs) {
            this.logger.warn('TabSystem', '[SimplifiedTabSystem] Maximum tabs reached', );
            return null;
        }

        const tabId = `tab-${this.nextTabId++}`;
        const tab = {
            id: tabId,
            title: options.title || 'New Tab',
            content: options.content || '',
            module: options.module || null,
            closable: options.closable !== false,
            active: false,
            element: null,
            contentElement: null
        };

        this.tabs.set(tabId, tab);
        this.renderTab(tab);
        this.switchToTab(tabId);

        this.logger.info("Component", `[SimplifiedTabSystem] Created tab: ${tabId}`);
        return tabId;
    }

    /**
     * Render tab in UI
     */
    renderTab(tab) {
        // Create tab element
        const tabElement = document.createElement('div');
        tabElement.className = 'tab';
        tabElement.setAttribute('data-tab-id', tab.id);
        
        tabElement.innerHTML = `
            <span class="tab-title">${tab.title}</span>
            ${tab.closable ? '<button class="tab-close" aria-label="Close tab">×</button>' : ''}
        `;

        // Create content element
        const contentElement = document.createElement('div');
        contentElement.className = 'tab-content';
        contentElement.setAttribute('data-tab-id', tab.id);
        contentElement.innerHTML = tab.content;
        contentElement.style.display = 'none';

        // Add event listeners
        tabElement.addEventListener('click', (e) => {
            if (!e.target.classList.contains('tab-close')) {
                this.switchToTab(tab.id);
            }
        });

        if (tab.closable) {
            const closeButton = tabElement.querySelector('.tab-close');
            closeButton.addEventListener('click', (e) => {
                e.stopPropagation();
                this.closeTab(tab.id);
            });
        }

        // Add to containers
        this.tabContainer.appendChild(tabElement);
        this.contentContainer.appendChild(contentElement);

        // Store references
        tab.element = tabElement;
        tab.contentElement = contentElement;
    }

    /**
     * Switch to a specific tab
     */
    switchToTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) {
            console.warn(`[SimplifiedTabSystem] Tab not found: ${tabId}`);
            return false;
        }

        // Deactivate current tab
        if (this.activeTabId) {
            const currentTab = this.tabs.get(this.activeTabId);
            if (currentTab) {
                currentTab.active = false;
                currentTab.element?.classList.remove('active');
                if (currentTab.contentElement) {
                    currentTab.contentElement.style.display = 'none';
                }
            }
        }

        // Activate new tab
        tab.active = true;
        tab.element?.classList.add('active');
        if (tab.contentElement) {
            tab.contentElement.style.display = 'block';
        }

        this.activeTabId = tabId;

        // Emit event
        document.dispatchEvent(new CustomEvent('tab:activated', {
            detail: { tabId, tab }
        }));

        this.logger.info("Component", `[SimplifiedTabSystem] Switched to tab: ${tabId}`);
        return true;
    }

    /**
     * Close a tab
     */
    closeTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) {
            console.warn(`[SimplifiedTabSystem] Tab not found: ${tabId}`);
            return false;
        }

        if (!tab.closable) {
            console.warn(`[SimplifiedTabSystem] Tab not closable: ${tabId}`);
            return false;
        }

        // Remove from DOM
        tab.element?.remove();
        tab.contentElement?.remove();

        // Remove from memory
        this.tabs.delete(tabId);

        // Handle active tab closure
        if (this.activeTabId === tabId) {
            this.activeTabId = null;
            
            // Switch to another tab if available
            const remainingTabs = Array.from(this.tabs.keys());
            if (remainingTabs.length > 0) {
                this.switchToTab(remainingTabs[remainingTabs.length - 1]);
            }
        }

        // Emit event
        document.dispatchEvent(new CustomEvent('tab:closed', {
            detail: { tabId }
        }));

        this.logger.info("Component", `[SimplifiedTabSystem] Closed tab: ${tabId}`);
        return true;
    }

    /**
     * Update tab title
     */
    updateTabTitle(tabId, newTitle) {
        const tab = this.tabs.get(tabId);
        if (!tab) {
            console.warn(`[SimplifiedTabSystem] Tab not found: ${tabId}`);
            return false;
        }

        tab.title = newTitle;
        const titleElement = tab.element?.querySelector('.tab-title');
        if (titleElement) {
            titleElement.textContent = newTitle;
        }

        return true;
    }

    /**
     * Update tab content
     */
    updateTabContent(tabId, newContent) {
        const tab = this.tabs.get(tabId);
        if (!tab) {
            console.warn(`[SimplifiedTabSystem] Tab not found: ${tabId}`);
            return false;
        }

        tab.content = newContent;
        if (tab.contentElement) {
            tab.contentElement.innerHTML = newContent;
        }

        return true;
    }

    /**
     * Get active tab
     */
    getActiveTab() {
        return this.activeTabId ? this.tabs.get(this.activeTabId) : null;
    }

    /**
     * Get all tabs
     */
    getAllTabs() {
        return Array.from(this.tabs.values());
    }

    /**
     * Get tab by ID
     */
    getTab(tabId) {
        return this.tabs.get(tabId) || null;
    }

    /**
     * Close all tabs
     */
    closeAllTabs() {
        const tabIds = Array.from(this.tabs.keys());
        for (const tabId of tabIds) {
            const tab = this.tabs.get(tabId);
            if (tab && tab.closable) {
                this.closeTab(tabId);
            }
        }
    }

    /**
     * Get tab system status
     */
    getStatus() {
        return {
            totalTabs: this.tabs.size,
            activeTabId: this.activeTabId,
            maxTabs: this.maxTabs,
            tabs: this.getAllTabs().map(tab => ({
                id: tab.id,
                title: tab.title,
                active: tab.active,
                closable: tab.closable,
                module: tab.module
            }))
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimplifiedTabSystem;
} else {
    window.SimplifiedTabSystem = SimplifiedTabSystem;
}