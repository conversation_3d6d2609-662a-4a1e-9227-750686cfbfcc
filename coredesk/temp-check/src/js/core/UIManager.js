/**
 * UIManager.js
 * Manages UI states, loading overlays, and visual feedback
 */

class UIManager {
    constructor(logger) {
        this.logger = logger;
        this.loadingOverlay = null;
        this.notificationContainer = null;
        this.setupUI();
    }

    setupUI() {
        this.createLoadingOverlay();
        this.createNotificationContainer();
    }

    createLoadingOverlay() {
        // Remove existing overlay if any
        const existing = document.getElementById('loading-overlay');
        if (existing) {
            existing.remove();
        }

        this.loadingOverlay = document.createElement('div');
        this.loadingOverlay.id = 'loading-overlay';
        this.loadingOverlay.className = 'loading-overlay';
        this.loadingOverlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">Cargando...</div>
            </div>
        `;
        this.loadingOverlay.style.display = 'none';
        document.body.appendChild(this.loadingOverlay);
    }

    createNotificationContainer() {
        // Remove existing container if any
        const existing = document.getElementById('notification-container');
        if (existing) {
            existing.remove();
        }

        this.notificationContainer = document.createElement('div');
        this.notificationContainer.id = 'notification-container';
        this.notificationContainer.className = 'notification-container';
        document.body.appendChild(this.notificationContainer);
    }

    showLoading(message = 'Cargando...') {
        try {
            if (this.loadingOverlay) {
                const textElement = this.loadingOverlay.querySelector('.loading-text');
                if (textElement) {
                    textElement.textContent = message;
                }
                this.loadingOverlay.style.display = 'flex';
            }
            this.logger.debug('UIManager', 'Loading overlay shown', { message });
        } catch (error) {
            this.logger.error('UIManager', 'Error showing loading overlay', error);
        }
    }

    hideLoading() {
        try {
            if (this.loadingOverlay) {
                this.loadingOverlay.style.display = 'none';
            }
            this.logger.debug('UIManager', 'Loading overlay hidden');
        } catch (error) {
            this.logger.error('UIManager', 'Error hiding loading overlay', error);
        }
    }

    showNotification(message, type = 'info', duration = 5000) {
        try {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close">&times;</button>
                </div>
            `;

            // Add click handler for close button
            const closeButton = notification.querySelector('.notification-close');
            closeButton.addEventListener('click', () => {
                this.removeNotification(notification);
            });

            // Add to container
            this.notificationContainer.appendChild(notification);

            // Auto-remove after duration
            if (duration > 0) {
                setTimeout(() => {
                    this.removeNotification(notification);
                }, duration);
            }

            this.logger.debug('UIManager', 'Notification shown', { message, type });

        } catch (error) {
            this.logger.error('UIManager', 'Error showing notification', error);
        }
    }

    removeNotification(notification) {
        try {
            if (notification && notification.parentNode) {
                notification.style.animation = 'slideOut 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        } catch (error) {
            this.logger.error('UIManager', 'Error removing notification', error);
        }
    }

    showError(message) {
        this.showNotification(message, 'error', 10000);
        this.logger.error('UIManager', 'Error shown to user', { message });
    }

    showSuccess(message) {
        this.showNotification(message, 'success', 5000);
        this.logger.info('UIManager', 'Success shown to user', { message });
    }

    showWarning(message) {
        this.showNotification(message, 'warning', 7000);
        this.logger.warn('UIManager', 'Warning shown to user', { message });
    }

    showInfo(message) {
        this.showNotification(message, 'info', 5000);
        this.logger.info('UIManager', 'Info shown to user', { message });
    }

    updateProgress(progress, message = '') {
        try {
            const progressBar = document.querySelector('.loading-progress');
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
            }

            if (message) {
                const textElement = this.loadingOverlay?.querySelector('.loading-text');
                if (textElement) {
                    textElement.textContent = message;
                }
            }

            this.logger.debug('UIManager', 'Progress updated', { progress, message });

        } catch (error) {
            this.logger.error('UIManager', 'Error updating progress', error);
        }
    }

    enableElement(selector) {
        try {
            const element = document.querySelector(selector);
            if (element) {
                element.disabled = false;
                element.classList.remove('disabled');
            }
        } catch (error) {
            this.logger.error('UIManager', 'Error enabling element', error);
        }
    }

    disableElement(selector) {
        try {
            const element = document.querySelector(selector);
            if (element) {
                element.disabled = true;
                element.classList.add('disabled');
            }
        } catch (error) {
            this.logger.error('UIManager', 'Error disabling element', error);
        }
    }

    showElement(selector) {
        try {
            const element = document.querySelector(selector);
            if (element) {
                element.style.display = '';
                element.classList.remove('hidden');
            }
        } catch (error) {
            this.logger.error('UIManager', 'Error showing element', error);
        }
    }

    hideElement(selector) {
        try {
            const element = document.querySelector(selector);
            if (element) {
                element.style.display = 'none';
                element.classList.add('hidden');
            }
        } catch (error) {
            this.logger.error('UIManager', 'Error hiding element', error);
        }
    }

    clearNotifications() {
        try {
            const notifications = this.notificationContainer?.querySelectorAll('.notification');
            notifications?.forEach(notification => {
                this.removeNotification(notification);
            });
        } catch (error) {
            this.logger.error('UIManager', 'Error clearing notifications', error);
        }
    }

    cleanup() {
        try {
            this.clearNotifications();
            
            if (this.loadingOverlay) {
                this.loadingOverlay.remove();
                this.loadingOverlay = null;
            }

            if (this.notificationContainer) {
                this.notificationContainer.remove();
                this.notificationContainer = null;
            }

        } catch (error) {
            this.logger.error('UIManager', 'Error during cleanup', error);
        }
    }
}

module.exports = UIManager;