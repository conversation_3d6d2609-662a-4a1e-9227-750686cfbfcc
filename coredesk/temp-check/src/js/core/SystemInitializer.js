/**
 * SystemInitializer.js
 * Handles initialization and coordination of core systems
 */

class SystemInitializer {
    constructor(logger) {
        this.logger = logger;
        this.systemsReady = {
            tabSystem: false,
            moduleController: false,
            licenseManager: false,
            activationModal: false,
            dataSyncService: false,
            syncStatusPanel: false
        };
        this.maxWaitTime = 30000; // 30 seconds
        this.checkInterval = 100; // 100ms
    }

    async waitForSystems() {
        this.logger.info('SystemInitializer', 'Waiting for systems...');
        
        const systems = [
            { name: 'simplifiedTabSystem', key: 'tabSystem' },
            { name: 'exclusiveModuleController', key: 'moduleController' },
            { name: 'licenseManager', key: 'licenseManager' },
            { name: 'licenseActivationModal', key: 'activationModal' },
            { name: 'dataSyncService', key: 'dataSyncService' },
            { name: 'syncStatusPanel', key: 'syncStatusPanel' }
        ];

        for (const system of systems) {
            await this.waitForSystem(system.name, () => {
                this.systemsReady[system.key] = true;
                this.logger.info('SystemInitializer', `${system.name} ready`);
            });
        }

        this.logger.info('SystemInitializer', 'All systems ready');
    }

    async waitForSystem(systemName, callback) {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = this.maxWaitTime / this.checkInterval;

            const checkSystem = () => {
                attempts++;

                if (window[systemName]) {
                    callback();
                    resolve();
                } else if (attempts >= maxAttempts) {
                    const error = new Error(`Timeout waiting for system: ${systemName}`);
                    this.logger.error('SystemInitializer', 'System timeout', error);
                    reject(error);
                } else {
                    setTimeout(checkSystem, this.checkInterval);
                }
            };

            checkSystem();
        });
    }

    getSystemsStatus() {
        return { ...this.systemsReady };
    }

    areAllSystemsReady() {
        return Object.values(this.systemsReady).every(ready => ready);
    }

    reset() {
        Object.keys(this.systemsReady).forEach(key => {
            this.systemsReady[key] = false;
        });
    }
}

module.exports = SystemInitializer;