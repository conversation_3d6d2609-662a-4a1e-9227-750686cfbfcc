/**
 * DynamicModuleManager
 * Core component for managing dynamic module lifecycle in CoreDesk Framework v2.0.0
 * 
 * Features:
 * - Download and install modules from remote repository
 * - Load/unload modules dynamically
 * - License validation integration
 * - Cache management for performance
 * - Event-driven architecture for UI updates
 */

class DynamicModuleManager extends EventTarget {
    constructor() {
        super();
        
        // Core dependencies - will be injected
        this.registry = null;           // ModuleRegistry instance
        this.downloader = null;         // ModuleDownloader instance
        this.cache = null;              // ModuleCache instance
        this.styleLoader = null;        // DynamicStyleLoader instance
        this.persistenceService = null; // ModulePersistenceClient instance
        
        // State management
        this.installedModules = new Map();  // moduleId -> ModulePackage
        this.activeModule = null;           // Currently active module instance
        this.isInitialized = false;
        
        // Configuration - Use environment variable or fallback to remote
        this.config = {
            repositoryUrl: this.getModuleRepositoryUrl(),
            maxCacheSize: 100 * 1024 * 1024, // 100MB
            downloadTimeout: 30000,           // 30 seconds
            validateLicenses: true,
            autoUpdate: false
        };
        
        // Event bus for internal communication
        this.eventBus = window.CoreDeskEvents?.createNamespace('dynamicModules') || {
            emit: (...args) => this.dispatchEvent(new CustomEvent(args[0], { detail: args[1] })),
            on: (event, callback) => this.addEventListener(event, callback),
            off: (event, callback) => this.removeEventListener(event, callback)
        };
        
        // Logger
        this.logger = window.GlobalLogger || {
            info: (tag, message) => console.log(`[${tag}] ${message}`),
            error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
            warn: (tag, message) => console.warn(`[${tag}] ${message}`),
            debug: (tag, message) => console.debug(`[${tag}] ${message}`)
        };
        
        // Bind methods for callbacks
        this.onModuleInstalled = this.onModuleInstalled.bind(this);
        this.onModuleUninstalled = this.onModuleUninstalled.bind(this);
        this.onModuleLoaded = this.onModuleLoaded.bind(this);
        this.onModuleUnloaded = this.onModuleUnloaded.bind(this);
    }

    /**
     * Get the module repository URL based on configuration or environment
     */
    getModuleRepositoryUrl() {
        // Check if there's a configured URL in CoreDeskAuth
        if (window.CoreDeskAuth?.api?.moduleRepositoryUrl) {
            return window.CoreDeskAuth.api.moduleRepositoryUrl;
        }
        
        // FORCED PRODUCTION URL - Always use production server for module loading
        // This ensures consistent behavior regardless of environment
        console.log('[DynamicModuleManager] FORCE PRODUCTION: Using production module repository');
        return 'https://coredeskpro.com/api/modules';
    }

    /**
     * Initialize the Dynamic Module Manager
     * @param {Object} dependencies - Required dependencies
     * @returns {Promise<void>}
     */
    async initialize(dependencies = {}) {
        try {
            this.logger.info('DynamicModuleManager', 'Initializing...');

            // Inject dependencies
            this.registry = dependencies.registry || await this.createDefaultRegistry();
            this.downloader = dependencies.downloader || await this.createDefaultDownloader();
            this.cache = dependencies.cache || await this.createDefaultCache();
            this.styleLoader = dependencies.styleLoader || await this.createDefaultStyleLoader();
            this.persistenceService = dependencies.persistenceService || await this.createDefaultPersistenceService();

            // Initialize components
            await this.registry.initialize();
            await this.cache.initialize();

            // Initialize persistence service
            if (this.persistenceService) {
                await this.persistenceService.initialize();
                this.logger.info('DynamicModuleManager', 'Module persistence service initialized');
            }

            // Load installed modules from registry
            await this.loadInstalledModules();

            // Setup event listeners
            this.setupEventListeners();

            this.isInitialized = true;
            this.logger.info('DynamicModuleManager', 'Initialized successfully');
            
            this.eventBus.emit('managerInitialized', { 
                installedCount: this.installedModules.size 
            });

        } catch (error) {
            this.logger.error('DynamicModuleManager', 'Initialization failed:', error);
            throw new Error(`DynamicModuleManager initialization failed: ${error.message}`);
        }
    }

    /**
     * Install a pre-created module package
     * @param {ModulePackage} modulePackage - Pre-created module package
     * @returns {Promise<boolean>}
     */
    async installModulePackage(modulePackage) {
        try {
            const moduleId = modulePackage.id;
            this.logger.info('DynamicModuleManager', `Installing module package: ${moduleId}`);

            // Validate package manifest
            try {
                modulePackage.validateManifest();
            } catch (error) {
                throw new Error(`Invalid module package: ${moduleId} - ${error.message}`);
            }

            // Check if already installed
            if (this.isModuleInstalled(moduleId)) {
                this.logger.warn('DynamicModuleManager', `Module ${moduleId} is already installed`);
                return false;
            }

            // Install styles if available
            if (modulePackage.styles && this.styleLoader) {
                // Check if styles is a file path or CSS content
                let cssContent = modulePackage.styles;
                if (typeof cssContent === 'string' && cssContent.endsWith('.css')) {
                    // It's a file path, try to load from filesystem first, then fallback to fetch
                    try {
                        // Try to load from filesystem (for installed modules)
                        const modulesPathResult = await window.electronAPI.fileSystem.getModulesPath();
                        if (modulesPathResult.success) {
                            const cssFilePath = `${modulesPathResult.path}/${moduleId}/${moduleId}.css`;
                            const fileExists = await window.electronAPI.fileSystem.exists(cssFilePath);
                            if (fileExists.exists) {
                                const fileContent = await window.electronAPI.fileSystem.readFile(cssFilePath);
                                cssContent = fileContent.content;
                                this.logger.info('DynamicModuleManager', `CSS loaded from filesystem: ${cssFilePath}`);
                            } else {
                                // Fallback to fetch for local development files
                                const response = await fetch(cssContent);
                                if (response.ok) {
                                    cssContent = await response.text();
                                    this.logger.info('DynamicModuleManager', `CSS loaded via fetch: ${cssContent}`);
                                } else {
                                    this.logger.warn('DynamicModuleManager', `Failed to load CSS file: ${cssContent}`);
                                    cssContent = ''; // Use empty CSS if file not found
                                }
                            }
                        }
                    } catch (error) {
                        this.logger.error('DynamicModuleManager', `Error loading CSS file ${cssContent}:`, error);
                        cssContent = ''; // Use empty CSS on error
                    }
                }
                if (cssContent) {
                    await this.styleLoader.loadModuleStyles(moduleId, cssContent);
                }
            }

            // Initialize the package
            await modulePackage.initialize();

            // Install the package (this persists to localStorage)
            this.logger.info('DynamicModuleManager', `About to call install() for module: ${moduleId}`);
            await modulePackage.install();
            this.logger.info('DynamicModuleManager', `Install() completed for module: ${moduleId}, isInstalled: ${modulePackage.isInstalled}`);

            // Store in registry
            if (this.registry) {
                await this.registry.registerModule(modulePackage);
            }

            // Register in database for persistence
            if (this.persistenceService) {
                const moduleData = {
                    moduleId: modulePackage.id,
                    name: modulePackage.manifest?.name || modulePackage.id,
                    version: modulePackage.version,
                    installPath: modulePackage.installPath,
                    manifestData: modulePackage.manifest,
                    status: 'active'
                };

                const registered = await this.persistenceService.registerInstalledModule(moduleData);
                if (registered) {
                    this.logger.info('DynamicModuleManager', `Module ${moduleId} registered in database`);
                } else {
                    this.logger.warn('DynamicModuleManager', `Failed to register module ${moduleId} in database`);
                }
            }

            // Store locally
            this.installedModules.set(moduleId, modulePackage);

            // Emit event
            this.eventBus.emit('moduleInstalled', {
                moduleId,
                version: modulePackage.version,
                source: 'package'
            });

            this.logger.info('DynamicModuleManager', `Module package ${moduleId} installed successfully`);
            return true;

        } catch (error) {
            this.logger.error('DynamicModuleManager', `Failed to install module package:`, error);
            throw error;
        }
    }

    /**
     * Install a module from remote repository
     * @param {string} moduleId - Module identifier
     * @param {Object} options - Installation options
     * @returns {Promise<boolean>}
     */
    async installModule(moduleId, options = {}) {
        const {
            version = 'latest',
            force = false,
            skipLicenseValidation = false
        } = options;

        try {
            this.logger.info('DynamicModuleManager', `Installing module: ${moduleId}`);
            this.eventBus.emit('moduleInstallStarted', { moduleId, version });

            // 1. Check if already installed
            if (this.installedModules.has(moduleId) && !force) {
                throw new Error(`Module ${moduleId} is already installed. Use force=true to reinstall.`);
            }

            // 2. Validate license if required
            if (this.config.validateLicenses && !skipLicenseValidation) {
                await this.validateModuleLicense(moduleId);
            }

            // 3. Download module package
            const packageData = await this.downloader.fetchModule(moduleId, version);
            
            // 4. Validate package integrity
            await this.validatePackage(packageData);

            // 5. Resolve and install dependencies
            await this.resolveDependencies(packageData.manifest);

            // 6. Create ModulePackage instance
            const ModulePackageClass = await this.getModulePackageClass();
            const modulePackage = new ModulePackageClass(packageData);

            // 7. Install the package
            await modulePackage.install();

            // 8. Register in registry
            await this.registry.registerModule(modulePackage);

            // 9. Add to installed modules
            this.installedModules.set(moduleId, modulePackage);

            // 10. Cache for future use
            await this.cache.cacheModule(moduleId, packageData);

            this.logger.info('DynamicModuleManager', `Module ${moduleId} installed successfully`);
            this.eventBus.emit('moduleInstalled', { 
                moduleId, 
                version: packageData.manifest.version,
                package: modulePackage 
            });

            return true;

        } catch (error) {
            this.logger.error('DynamicModuleManager', `Failed to install module ${moduleId}:`, error);
            this.eventBus.emit('moduleInstallError', { moduleId, error: error.message });
            throw error;
        }
    }

    /**
     * Uninstall a module
     * @param {string} moduleId - Module identifier
     * @param {Object} options - Uninstall options
     * @returns {Promise<boolean>}
     */
    async uninstallModule(moduleId, options = {}) {
        const { cleanCache = true, force = false } = options;

        try {
            this.logger.info('DynamicModuleManager', `Uninstalling module: ${moduleId}`);
            this.eventBus.emit('moduleUninstallStarted', { moduleId });

            // 1. Check if module is installed
            const modulePackage = this.installedModules.get(moduleId);
            if (!modulePackage) {
                throw new Error(`Module ${moduleId} is not installed`);
            }

            // 2. Check if module is currently active
            if (this.activeModule && this.activeModule.moduleCode === moduleId) {
                if (!force) {
                    throw new Error(`Cannot uninstall active module ${moduleId}. Deactivate first or use force=true.`);
                }
                await this.unloadModule(moduleId);
            }

            // 3. Check dependencies (other modules that depend on this one)
            const dependentModules = await this.registry.getModulesDependingOn(moduleId);
            if (dependentModules.length > 0 && !force) {
                throw new Error(`Cannot uninstall ${moduleId}. Required by: ${dependentModules.join(', ')}`);
            }

            // 4. Uninstall the package
            await modulePackage.uninstall();

            // 5. Remove from registry
            await this.registry.unregisterModule(moduleId);

            // 6. Remove from installed modules
            this.installedModules.delete(moduleId);

            // 7. Clean cache if requested
            if (cleanCache) {
                await this.cache.invalidateModule(moduleId);
            }

            this.logger.info('DynamicModuleManager', `Module ${moduleId} uninstalled successfully`);
            this.eventBus.emit('moduleUninstalled', { moduleId });

            return true;

        } catch (error) {
            this.logger.error('DynamicModuleManager', `Failed to uninstall module ${moduleId}:`, error);
            this.eventBus.emit('moduleUninstallError', { moduleId, error: error.message });
            throw error;
        }
    }

    /**
     * Load and activate a module
     * @param {string} moduleId - Module identifier
     * @param {Object} options - Load options
     * @returns {Promise<Object>} Module instance
     */
    async loadModule(moduleId, options = {}) {
        try {
            this.logger.info('DynamicModuleManager', `Loading module: ${moduleId}`);
            this.eventBus.emit('moduleLoadStarted', { moduleId });

            // 1. Check if module is installed
            const modulePackage = this.installedModules.get(moduleId);
            if (!modulePackage) {
                throw new Error(`Module ${moduleId} is not installed`);
            }

            // 2. Skip license validation for already installed modules (validated during installation)
            // License validation is only needed for modules being installed, not loaded
            this.logger.debug('DynamicModuleManager', `Skipping license validation for installed module: ${moduleId}`);

            // 3. Create module instance
            const ModuleClass = modulePackage.getModuleClass();

            // Pass the original package data to the module constructor
            const packageData = {
                manifest: modulePackage.manifest,
                moduleCode: modulePackage.moduleCode,
                styles: modulePackage.styles,
                assets: modulePackage.assets || {}
            };

            const moduleInstance = new ModuleClass(packageData);

            // 4. Initialize module
            if (typeof moduleInstance.initialize === 'function') {
                await moduleInstance.initialize();
            }

            // 5. Activate module (this creates the DOM elements)
            if (typeof moduleInstance.activate === 'function') {
                await moduleInstance.activate();
            }

            // 6. Load module styles AFTER activation (so DOM elements exist)
            if (modulePackage.styles) {
                // Check if styles is a file path or CSS content
                let cssContent = modulePackage.styles;
                if (typeof cssContent === 'string' && cssContent.endsWith('.css')) {
                    // It's a file path, try to load from filesystem first, then fallback to fetch
                    try {
                        // Try to load from filesystem (for installed modules)
                        const modulesPathResult = await window.electronAPI.fileSystem.getModulesPath();
                        if (modulesPathResult.success) {
                            const cssFilePath = `${modulesPathResult.path}/${moduleId}/${moduleId}.css`;
                            const fileExists = await window.electronAPI.fileSystem.exists(cssFilePath);
                            if (fileExists.exists) {
                                const fileContent = await window.electronAPI.fileSystem.readFile(cssFilePath);
                                cssContent = fileContent.content;
                                this.logger.info('DynamicModuleManager', `CSS loaded from filesystem: ${cssFilePath}`);
                            } else {
                                // Fallback to fetch for local development files
                                const response = await fetch(cssContent);
                                if (response.ok) {
                                    cssContent = await response.text();
                                    this.logger.info('DynamicModuleManager', `CSS loaded via fetch: ${cssContent}`);
                                } else {
                                    this.logger.warn('DynamicModuleManager', `Failed to load CSS file: ${cssContent}`);
                                    cssContent = ''; // Use empty CSS if file not found
                                }
                            }
                        }
                    } catch (error) {
                        this.logger.error('DynamicModuleManager', `Error loading CSS file ${cssContent}:`, error);
                        cssContent = ''; // Use empty CSS on error
                    }
                }
                if (cssContent) {
                    await this.styleLoader.loadModuleStyles(moduleId, cssContent);
                    this.logger.info('DynamicModuleManager', `Styles applied after module activation for ${moduleId}`);
                }
            }

            // 7. Set as active module (following exclusive pattern)
            this.activeModule = moduleInstance;
            this.activeModule.moduleCode = moduleId;
            this.activeModule.package = modulePackage;

            this.logger.info('DynamicModuleManager', `Module ${moduleId} loaded successfully`);
            this.eventBus.emit('moduleLoaded', { 
                moduleId, 
                instance: moduleInstance 
            });

            return moduleInstance;

        } catch (error) {
            this.logger.error('DynamicModuleManager', `Failed to load module ${moduleId}:`, error);
            this.eventBus.emit('moduleLoadError', { moduleId, error: error.message });
            throw error;
        }
    }

    /**
     * Unload and deactivate a module
     * @param {string} moduleId - Module identifier
     * @returns {Promise<boolean>}
     */
    async unloadModule(moduleId) {
        try {
            this.logger.info('DynamicModuleManager', `Unloading module: ${moduleId}`);
            this.eventBus.emit('moduleUnloadStarted', { moduleId });

            // 1. Check if module is currently active
            if (!this.activeModule || this.activeModule.moduleCode !== moduleId) {
                throw new Error(`Module ${moduleId} is not currently active`);
            }

            // 2. Deactivate module
            if (typeof this.activeModule.deactivate === 'function') {
                await this.activeModule.deactivate();
            }

            // 3. Cleanup module resources
            if (typeof this.activeModule.cleanup === 'function') {
                await this.activeModule.cleanup();
            }

            // 4. Unload module styles
            await this.styleLoader.unloadModuleStyles(moduleId);

            // 5. Clear active module reference
            this.activeModule = null;

            this.logger.info('DynamicModuleManager', `Module ${moduleId} unloaded successfully`);
            this.eventBus.emit('moduleUnloaded', { moduleId });

            return true;

        } catch (error) {
            this.logger.error('DynamicModuleManager', `Failed to unload module ${moduleId}:`, error);
            this.eventBus.emit('moduleUnloadError', { moduleId, error: error.message });
            throw error;
        }
    }

    /**
     * Get list of installed modules
     * @returns {Array<string>} Array of module IDs
     */
    getInstalledModules() {
        return Array.from(this.installedModules.keys());
    }

    /**
     * Get list of available modules from repository
     * @returns {Promise<Array<Object>>} Array of module metadata
     */
    async getAvailableModules() {
        try {
            return await this.downloader.fetchAvailableModules();
        } catch (error) {
            this.logger.error('DynamicModuleManager', 'Failed to fetch available modules:', error);
            return [];
        }
    }

    /**
     * Check if a module is installed
     * @param {string} moduleId - Module identifier
     * @returns {boolean}
     */
    isModuleInstalled(moduleId) {
        return this.installedModules.has(moduleId);
    }

    /**
     * Activate a module (mark as active in database)
     * @param {string} moduleId - Module ID to activate
     * @returns {Promise<boolean>}
     */
    async activateModule(moduleId) {
        try {
            if (!this.persistenceService) {
                this.logger.warn('DynamicModuleManager', 'Persistence service not available');
                return false;
            }

            this.logger.info('DynamicModuleManager', `Activating module: ${moduleId}`);

            const success = await this.persistenceService.updateModuleStatus(moduleId, 'active');

            if (success) {
                this.logger.info('DynamicModuleManager', `Module ${moduleId} activated successfully`);

                // Emit event
                this.eventBus.emit('moduleActivated', { moduleId });

                return true;
            } else {
                this.logger.warn('DynamicModuleManager', `Failed to activate module ${moduleId}`);
                return false;
            }
        } catch (error) {
            this.logger.error('DynamicModuleManager', `Error activating module ${moduleId}:`, error);
            return false;
        }
    }

    /**
     * Deactivate a module (mark as inactive in database)
     * @param {string} moduleId - Module ID to deactivate
     * @returns {Promise<boolean>}
     */
    async deactivateModule(moduleId) {
        try {
            if (!this.persistenceService) {
                this.logger.warn('DynamicModuleManager', 'Persistence service not available');
                return false;
            }

            this.logger.info('DynamicModuleManager', `Deactivating module: ${moduleId}`);

            // Unload module if currently loaded
            if (this.isModuleLoaded(moduleId)) {
                await this.unloadModule(moduleId);
            }

            const success = await this.persistenceService.updateModuleStatus(moduleId, 'inactive');

            if (success) {
                this.logger.info('DynamicModuleManager', `Module ${moduleId} deactivated successfully`);

                // Emit event
                this.eventBus.emit('moduleDeactivated', { moduleId });

                return true;
            } else {
                this.logger.warn('DynamicModuleManager', `Failed to deactivate module ${moduleId}`);
                return false;
            }
        } catch (error) {
            this.logger.error('DynamicModuleManager', `Error deactivating module ${moduleId}:`, error);
            return false;
        }
    }

    /**
     * Get module status from database
     * @param {string} moduleId - Module ID
     * @returns {Promise<string|null>} Module status ('active', 'inactive') or null if not found
     */
    async getModuleStatus(moduleId) {
        try {
            if (!this.persistenceService) {
                return null;
            }

            const module = await this.persistenceService.getInstalledModule(moduleId);
            return module ? module.status : null;
        } catch (error) {
            this.logger.error('DynamicModuleManager', `Error getting module status for ${moduleId}:`, error);
            return null;
        }
    }

    /**
     * Get all active modules from database
     * @returns {Promise<Array>}
     */
    async getActiveModulesFromDatabase() {
        try {
            if (!this.persistenceService) {
                return [];
            }

            return await this.persistenceService.getActiveModules();
        } catch (error) {
            this.logger.error('DynamicModuleManager', 'Error getting active modules:', error);
            return [];
        }
    }

    /**
     * Check if a module is currently active
     * @param {string} moduleId - Module identifier
     * @returns {boolean}
     */
    isModuleActive(moduleId) {
        return this.activeModule && this.activeModule.moduleCode === moduleId;
    }

    /**
     * Check if a module is currently loaded (same as active in exclusive system)
     * @param {string} moduleId - Module identifier
     * @returns {boolean}
     */
    isModuleLoaded(moduleId) {
        // In an exclusive module system, loaded === active
        return this.isModuleActive(moduleId);
    }

    /**
     * Get the active module instance
     * @param {string} moduleId - Module identifier
     * @returns {Object|null} Module instance or null if not loaded/active
     */
    getModuleInstance(moduleId) {
        // In exclusive system, only return instance if this module is currently active
        if (this.isModuleActive(moduleId)) {
            return this.activeModule;
        }
        return null;
    }

    /**
     * Get module package information
     * @param {string} moduleId - Module identifier
     * @returns {Object|null} Module package or null if not installed
     */
    getModulePackage(moduleId) {
        return this.installedModules.get(moduleId) || null;
    }

    /**
     * Update configuration
     * @param {Object} newConfig - Configuration updates
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.logger.info('DynamicModuleManager', 'Configuration updated');
    }

    // Private Methods

    /**
     * Load installed modules from registry on initialization
     * @private
     */
    async loadInstalledModules() {
        try {
            const installedModuleIds = await this.registry.getInstalledModuleIds();

            for (const moduleId of installedModuleIds) {
                const modulePackage = await this.registry.getModule(moduleId);
                if (modulePackage) {
                    this.installedModules.set(moduleId, modulePackage);
                }
            }

            this.logger.info('DynamicModuleManager', `Loaded ${this.installedModules.size} installed modules`);
        } catch (error) {
            this.logger.error('DynamicModuleManager', 'Failed to load installed modules:', error);
        }
    }

    /**
     * Refresh the registry and reload installed modules
     * @returns {Promise<void>}
     */
    async refreshRegistry() {
        try {
            this.logger.info('DynamicModuleManager', 'Refreshing registry...');

            // Reinitialize the registry to pick up new modules
            await this.registry.initialize();

            // Reload installed modules
            await this.loadInstalledModules();

            this.logger.info('DynamicModuleManager', 'Registry refreshed successfully');
        } catch (error) {
            this.logger.error('DynamicModuleManager', 'Failed to refresh registry:', error);
            throw error;
        }
    }

    /**
     * Validate module license
     * @private
     * @param {string} moduleId - Module identifier
     */
    async validateModuleLicense(moduleId) {
        const licenseManager = window.licenseManager;
        if (!licenseManager) {
            throw new Error('License manager not available');
        }

        // Get module info to check required license
        const moduleInfo = await this.downloader.getModuleInfo(moduleId);
        const requiredLicense = moduleInfo.manifest?.requiredLicense;
        
        if (requiredLicense) {
            const isValid = await licenseManager.validateModuleLicense(requiredLicense);
            if (!isValid) {
                throw new Error(`License required for module ${moduleId}: ${requiredLicense}`);
            }
        }
    }

    /**
     * Validate package integrity and compatibility
     * @private
     * @param {Object} packageData - Package data
     */
    async validatePackage(packageData) {
        // Validate manifest
        if (!packageData.manifest) {
            throw new Error('Package manifest is missing');
        }

        const manifest = packageData.manifest;
        const requiredFields = ['id', 'name', 'version', 'main'];
        
        for (const field of requiredFields) {
            if (!manifest[field]) {
                throw new Error(`Package manifest missing required field: ${field}`);
            }
        }

        // Validate CoreDesk compatibility
        if (manifest.dependencies?.coredesk) {
            const currentVersion = window.COREDESK_CONSTANTS?.VERSION || '2.0.0';
            // Simple version check - could be enhanced with semver
            if (!this.isCompatibleVersion(manifest.dependencies.coredesk, currentVersion)) {
                throw new Error(`Module requires CoreDesk ${manifest.dependencies.coredesk}, current: ${currentVersion}`);
            }
        }

        // Validate checksum if provided
        if (packageData.checksum && packageData.expectedChecksum) {
            if (packageData.checksum !== packageData.expectedChecksum) {
                throw new Error('Package checksum validation failed');
            }
        }
    }

    /**
     * Resolve and install module dependencies
     * @private
     * @param {Object} manifest - Module manifest
     */
    async resolveDependencies(manifest) {
        if (!manifest.dependencies) {
            return;
        }

        const moduleDependencies = Object.keys(manifest.dependencies)
            .filter(dep => dep !== 'coredesk'); // Exclude core dependency

        for (const depModuleId of moduleDependencies) {
            if (!this.isModuleInstalled(depModuleId)) {
                this.logger.info('DynamicModuleManager', `Installing dependency: ${depModuleId}`);
                await this.installModule(depModuleId, { 
                    version: manifest.dependencies[depModuleId] 
                });
            }
        }
    }

    /**
     * Check version compatibility
     * @private
     * @param {string} required - Required version or range
     * @param {string} current - Current version
     * @returns {boolean}
     */
    isCompatibleVersion(required, current) {
        // Simple implementation - could be enhanced with semver library
        if (required.startsWith('>=')) {
            const minVersion = required.slice(2);
            return current >= minVersion;
        }
        
        if (required.startsWith('^')) {
            const baseVersion = required.slice(1);
            const [reqMajor] = baseVersion.split('.');
            const [curMajor] = current.split('.');
            return curMajor === reqMajor && current >= baseVersion;
        }
        
        return current === required;
    }

    /**
     * Setup event listeners
     * @private
     */
    setupEventListeners() {
        this.addEventListener('moduleInstalled', this.onModuleInstalled);
        this.addEventListener('moduleUninstalled', this.onModuleUninstalled);
        this.addEventListener('moduleLoaded', this.onModuleLoaded);
        this.addEventListener('moduleUnloaded', this.onModuleUnloaded);
    }

    /**
     * Event handlers
     * @private
     */
    onModuleInstalled(event) {
        // Update UI, notify other components
        this.logger.debug('DynamicModuleManager', 'Module installed event handled');
    }

    onModuleUninstalled(event) {
        // Update UI, notify other components
        this.logger.debug('DynamicModuleManager', 'Module uninstalled event handled');
    }

    onModuleLoaded(event) {
        // Update activity bar, status bar, etc.
        this.logger.debug('DynamicModuleManager', 'Module loaded event handled');
    }

    onModuleUnloaded(event) {
        // Clean up UI state
        this.logger.debug('DynamicModuleManager', 'Module unloaded event handled');
    }

    /**
     * Default factory methods for dependencies
     * @private
     */
    async createDefaultRegistry() {
        // Create a real ModuleRegistry instance
        if (window.ModuleRegistry) {
            const registry = new window.ModuleRegistry();
            await registry.initialize();
            return registry;
        } else {
            // Fallback if ModuleRegistry not available
            return {
                initialize: async () => {},
                registerModule: async () => {},
                unregisterModule: async () => {},
                getModule: async () => null,
                getInstalledModuleIds: async () => [],
                getModulesDependingOn: async () => [],
                getInstalledModules: () => [],
                getAllModules: () => [],
                getModuleInfo: () => null,
                isModuleInstalled: () => false
            };
        }
    }

    async createDefaultDownloader() {
        // Create a real ModuleDownloader instance
        if (window.ModuleDownloader) {
            this.logger.info('DynamicModuleManager', 'Creating ModuleDownloader instance');
            return new window.ModuleDownloader({
                baseUrl: this.getModuleRepositoryUrl()
            });
        } else {
            this.logger.warn('DynamicModuleManager', 'ModuleDownloader class not available, using fallback');
            return {
                fetchModule: async () => { throw new Error('ModuleDownloader not available'); },
                fetchAvailableModules: async () => [],
                getModuleInfo: async () => ({ manifest: {} })
            };
        }
    }

    async createDefaultCache() {
        // Will be implemented when ModuleCache is created
        return {
            initialize: async () => {},
            cacheModule: async () => {},
            invalidateModule: async () => {}
        };
    }

    async createDefaultStyleLoader() {
        // Will be implemented when DynamicStyleLoader is created
        return {
            loadModuleStyles: async () => {},
            unloadModuleStyles: async () => {}
        };
    }

    async createDefaultPersistenceService() {
        // Create a real ModulePersistenceClient instance
        if (window.ModulePersistenceClient) {
            this.logger.info('DynamicModuleManager', 'Creating ModulePersistenceClient instance');
            const persistenceService = new window.ModulePersistenceClient(this.logger);
            return persistenceService;
        } else {
            this.logger.warn('DynamicModuleManager', 'ModulePersistenceClient not available, using mock');
            // Return mock implementation
            return {
                initialize: async () => true,
                registerInstalledModule: async () => true,
                unregisterModule: async () => true,
                getInstalledModules: async () => [],
                getInstalledModule: async () => null,
                isModuleInstalled: async () => false,
                updateModuleStatus: async () => true,
                getActiveModules: async () => []
            };
        }
    }

    async getModulePackageClass() {
        // Will return ModulePackage class when implemented
        // For now, return a minimal implementation
        return class ModulePackage {
            constructor(packageData) {
                this.manifest = packageData.manifest;
                this.moduleCode = packageData.moduleCode;
                this.styles = packageData.styles;
                this.assets = packageData.assets;
            }

            async install() {
                // Minimal implementation
            }

            async uninstall() {
                // Minimal implementation
            }

            getModuleClass() {
                // Return the module class
                // This will be properly implemented in ModulePackage
                return class DynamicModule {
                    constructor() {
                        this.isInitialized = false;
                        this.isActive = false;
                    }

                    async initialize() {
                        this.isInitialized = true;
                    }

                    async activate() {
                        this.isActive = true;
                    }

                    async deactivate() {
                        this.isActive = false;
                    }

                    async cleanup() {
                        this.isInitialized = false;
                    }
                };
            }
        };
    }
}

// Make available globally
window.DynamicModuleManager = DynamicModuleManager;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicModuleManager;
}

console.log('DynamicModuleManager', '[DynamicModuleManager] Class loaded successfully');