/**
 * test-database-connection.js
 * Script para probar la conexión y funcionalidad de la base de datos
 */

// Función para probar la conexión a la base de datos
async function testDatabaseConnection() {
    console.log('🔍 Iniciando prueba de conexión a la base de datos...');
    
    try {
        // Verificar que electronAPI esté disponible
        if (!window.electronAPI) {
            throw new Error('electronAPI no está disponible');
        }
        
        if (!window.electronAPI.database) {
            throw new Error('electronAPI.database no está disponible');
        }
        
        if (!window.electronAPI.database.execute) {
            throw new Error('electronAPI.database.execute no está disponible');
        }
        
        console.log('✅ electronAPI.database está disponible');
        
        // Probar consulta simple para verificar que la tabla existe
        console.log('🔍 Probando consulta para verificar tabla installed_modules...');
        
        const testQuery = `
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='installed_modules'
        `;
        
        const result = await window.electronAPI.database.execute(testQuery, []);
        console.log('📊 Resultado de la consulta:', result);
        
        if (result.success && result.data.length > 0) {
            console.log('✅ Tabla installed_modules existe');
            
            // Probar consulta de módulos instalados
            console.log('🔍 Consultando módulos instalados...');
            const modulesQuery = 'SELECT * FROM installed_modules';
            const modulesResult = await window.electronAPI.database.execute(modulesQuery, []);
            console.log('📊 Módulos instalados:', modulesResult);
            
            if (modulesResult.success) {
                console.log(`✅ Encontrados ${modulesResult.data.length} módulos instalados`);
                if (modulesResult.data.length > 0) {
                    console.log('📋 Módulos:', modulesResult.data);
                }
            } else {
                console.log('❌ Error consultando módulos:', modulesResult.error);
            }
            
        } else {
            console.log('❌ Tabla installed_modules no existe o consulta falló');
        }
        
        // Probar inserción de un módulo de prueba
        console.log('🔍 Probando inserción de módulo de prueba...');
        
        const insertQuery = `
            INSERT OR REPLACE INTO installed_modules 
            (module_id, name, version, status, install_path, manifest_data)
            VALUES (?, ?, ?, ?, ?, ?)
        `;
        
        const testModuleData = [
            'test-persistence-module',
            'Test Persistence Module',
            '1.0.0',
            'active',
            '/test/path',
            JSON.stringify({
                name: 'Test Persistence Module',
                version: '1.0.0',
                description: 'Módulo de prueba para verificar persistencia'
            })
        ];
        
        const insertResult = await window.electronAPI.database.execute(insertQuery, testModuleData);
        console.log('📊 Resultado de inserción:', insertResult);
        
        if (insertResult.success) {
            console.log('✅ Módulo de prueba insertado correctamente');
            
            // Verificar que se insertó correctamente
            const verifyQuery = 'SELECT * FROM installed_modules WHERE module_id = ?';
            const verifyResult = await window.electronAPI.database.execute(verifyQuery, ['test-persistence-module']);
            console.log('📊 Verificación de inserción:', verifyResult);
            
            if (verifyResult.success && verifyResult.data.length > 0) {
                console.log('✅ Módulo de prueba verificado en la base de datos');
                console.log('📋 Datos del módulo:', verifyResult.data[0]);
            } else {
                console.log('❌ No se pudo verificar el módulo insertado');
            }
        } else {
            console.log('❌ Error insertando módulo de prueba:', insertResult.error);
        }
        
        console.log('🎉 Prueba de base de datos completada');
        
    } catch (error) {
        console.error('❌ Error en la prueba de base de datos:', error);
    }
}

// Función para limpiar datos de prueba
async function cleanupTestData() {
    console.log('🧹 Limpiando datos de prueba...');
    
    try {
        const deleteQuery = 'DELETE FROM installed_modules WHERE module_id = ?';
        const result = await window.electronAPI.database.execute(deleteQuery, ['test-persistence-module']);
        
        if (result.success) {
            console.log('✅ Datos de prueba eliminados');
        } else {
            console.log('❌ Error eliminando datos de prueba:', result.error);
        }
    } catch (error) {
        console.error('❌ Error limpiando datos de prueba:', error);
    }
}

// Exponer funciones globalmente para uso en la consola del navegador
window.testDatabaseConnection = testDatabaseConnection;
window.cleanupTestData = cleanupTestData;

console.log('🧪 Script de prueba de base de datos cargado!');
console.log('📋 Comandos disponibles:');
console.log('   testDatabaseConnection() - Probar conexión y funcionalidad de la base de datos');
console.log('   cleanupTestData() - Limpiar datos de prueba');

// Ejecutar automáticamente la prueba después de un breve delay
setTimeout(() => {
    console.log('🚀 Ejecutando prueba automática de base de datos...');
    testDatabaseConnection();
}, 3000);
