/**
 * Token Persistence Debug Script
 * Helps debug token storage and retrieval issues
 */

class TokenPersistenceDebugger {
    constructor() {
        this.testResults = [];
    }

    async runPersistenceTest() {
        console.log('🔍 Running Token Persistence Test...');
        console.log('='.repeat(50));
        
        try {
            // Step 1: Check initial state
            await this.checkInitialState();
            
            // Step 2: Perform login
            await this.performTestLogin();
            
            // Step 3: Verify immediate persistence
            await this.verifyImmediatePersistence();
            
            // Step 4: Simulate page reload check
            await this.simulatePageReloadCheck();
            
            // Step 5: Show results
            this.showTestResults();
            
        } catch (error) {
            console.error('❌ Test failed:', error);
        }
    }

    async checkInitialState() {
        console.log('📋 Step 1: Checking initial authentication state...');
        
        const initialAuth = window.CoreDeskAuth?.utils?.isAuthenticated();
        const initialUser = window.CoreDeskAuth?.utils?.getCurrentUser();
        const initialToken = localStorage.getItem('coredesk_token');
        const initialExpiry = localStorage.getItem('coredesk_token_expiry');
        
        console.log('   - Initial authenticated:', initialAuth);
        console.log('   - Initial user:', !!initialUser);
        console.log('   - Initial token in storage:', !!initialToken);
        console.log('   - Initial expiry in storage:', !!initialExpiry);
        
        this.addResult('Initial State', 'Check', initialAuth ? 'AUTHENTICATED' : 'NOT_AUTHENTICATED');
    }

    async performTestLogin() {
        console.log('\n📋 Step 2: Performing test login...');
        
        if (!window.unifiedAuthManager) {
            throw new Error('UnifiedAuthManager not available');
        }
        
        const loginResult = await window.unifiedAuthManager.login(
            '<EMAIL>',
            'Qazwsx123!',
            false
        );
        
        console.log('   - Login result:', loginResult.success ? 'SUCCESS' : 'FAILED');
        if (!loginResult.success) {
            throw new Error('Login failed: ' + loginResult.error);
        }
        
        this.addResult('Login', 'Test Login', 'SUCCESS');
    }

    async verifyImmediatePersistence() {
        console.log('\n📋 Step 3: Verifying immediate token persistence...');
        
        // Check authentication state immediately after login
        const immediateAuth = window.CoreDeskAuth?.utils?.isAuthenticated();
        const immediateUser = window.CoreDeskAuth?.utils?.getCurrentUser();
        
        // Check raw localStorage
        const storedToken = localStorage.getItem('coredesk_token');
        const storedExpiry = localStorage.getItem('coredesk_token_expiry');
        const storedUser = localStorage.getItem('coredesk_user');
        
        console.log('   - Authenticated immediately after login:', immediateAuth);
        console.log('   - User data available:', !!immediateUser);
        console.log('   - Token stored in localStorage:', !!storedToken);
        console.log('   - Expiry stored in localStorage:', !!storedExpiry);
        console.log('   - User stored in localStorage:', !!storedUser);
        
        if (immediateAuth && storedToken && storedExpiry) {
            this.addResult('Immediate Persistence', 'Storage Check', 'SUCCESS');
        } else {
            this.addResult('Immediate Persistence', 'Storage Check', 'FAILED');
        }
        
        // Test token decryption
        if (window.tokenManager && storedToken) {
            try {
                const decryptedToken = window.tokenManager.decrypt(storedToken);
                console.log('   - Token decryption successful:', !!decryptedToken);
                this.addResult('Immediate Persistence', 'Token Decryption', 'SUCCESS');
            } catch (error) {
                console.log('   - Token decryption failed:', error.message);
                this.addResult('Immediate Persistence', 'Token Decryption', 'FAILED');
            }
        }
    }

    async simulatePageReloadCheck() {
        console.log('\n📋 Step 4: Simulating page reload authentication check...');
        
        // Simulate what happens on page reload by clearing in-memory state
        // and checking if authentication can be restored from storage
        
        // Clear any in-memory user state (simulating page reload)
        if (window.unifiedAuthManager?.authenticationHandler) {
            window.unifiedAuthManager.authenticationHandler.currentUser = null;
        }
        
        // Wait a moment to simulate load time
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Re-initialize dependencies (simulating page load)
        if (window.unifiedAuthManager) {
            window.unifiedAuthManager.initializeDependencies();
        }
        
        // Check if authentication is still valid
        const reloadAuth = window.CoreDeskAuth?.utils?.isAuthenticated();
        const reloadUser = window.CoreDeskAuth?.utils?.getCurrentUser();
        
        // Check TokenManager validation
        const tokenValid = window.tokenManager?.isTokenValid();
        
        console.log('   - Authentication after simulated reload:', reloadAuth);
        console.log('   - User data after simulated reload:', !!reloadUser);
        console.log('   - TokenManager validation after reload:', tokenValid);
        
        // Test session restoration
        if (window.unifiedAuthManager?.authenticationHandler) {
            await window.unifiedAuthManager.authenticationHandler.restoreSession();
            const restoredUser = window.unifiedAuthManager.authenticationHandler.currentUser;
            console.log('   - Session restoration successful:', !!restoredUser);
            
            if (reloadAuth && tokenValid && restoredUser) {
                this.addResult('Simulated Reload', 'Persistence Check', 'SUCCESS');
            } else {
                this.addResult('Simulated Reload', 'Persistence Check', 'FAILED');
                console.log('   - Debug info:');
                console.log('     - reloadAuth:', reloadAuth);
                console.log('     - tokenValid:', tokenValid);
                console.log('     - restoredUser:', !!restoredUser);
            }
        }
    }

    addResult(category, test, result) {
        this.testResults.push({
            category,
            test,
            result,
            timestamp: new Date().toISOString()
        });
    }

    showTestResults() {
        console.log('\n📊 TOKEN PERSISTENCE TEST RESULTS');
        console.log('='.repeat(50));
        
        this.testResults.forEach(result => {
            const icon = result.result === 'SUCCESS' ? '✅' : 
                        result.result === 'FAILED' ? '❌' : '📋';
            console.log(`${icon} ${result.category} - ${result.test}: ${result.result}`);
        });
        
        const successCount = this.testResults.filter(r => r.result === 'SUCCESS').length;
        const totalCount = this.testResults.length;
        
        console.log('\n📈 Overall Result:');
        console.log(`   ${successCount}/${totalCount} tests passed`);
        
        if (successCount === totalCount) {
            console.log('🎉 All tests passed! Token persistence is working correctly.');
        } else {
            console.log('⚠️  Some tests failed. Check the logs above for details.');
        }
    }

    // Quick diagnostic method
    showCurrentState() {
        console.log('🔍 CURRENT AUTHENTICATION STATE');
        console.log('='.repeat(40));
        
        // Check various authentication indicators
        const indicators = [
            ['CoreDeskAuth.isAuthenticated()', window.CoreDeskAuth?.utils?.isAuthenticated()],
            ['TokenManager.isTokenValid()', window.tokenManager?.isTokenValid()],
            ['UnifiedAuthManager available', !!window.unifiedAuthManager],
            ['Current user exists', !!window.CoreDeskAuth?.utils?.getCurrentUser()],
            ['Token in localStorage', !!localStorage.getItem('coredesk_token')],
            ['Expiry in localStorage', !!localStorage.getItem('coredesk_token_expiry')],
            ['User in localStorage', !!localStorage.getItem('coredesk_user')]
        ];
        
        indicators.forEach(([label, value]) => {
            const icon = value ? '✅' : '❌';
            console.log(`${icon} ${label}: ${value}`);
        });
        
        // Show raw localStorage values (truncated)
        console.log('\n📄 Raw localStorage values:');
        const authKeys = ['coredesk_token', 'coredesk_token_expiry', 'coredesk_user'];
        authKeys.forEach(key => {
            const value = localStorage.getItem(key);
            if (value) {
                const display = value.length > 50 ? value.substring(0, 50) + '...' : value;
                console.log(`   ${key}: ${display}`);
            } else {
                console.log(`   ${key}: (empty)`);
            }
        });
    }

    // Test logout specifically
    async testLogout() {
        console.log('🚪 Testing logout functionality...');
        console.log('='.repeat(40));
        
        // Show state before logout
        console.log('📋 State before logout:');
        this.showCurrentState();
        
        // Perform logout
        console.log('\n🔄 Performing logout...');
        const logoutResult = await window.unifiedAuthManager?.logout();
        console.log('   - Logout method result:', logoutResult);
        
        // Wait a moment for cleanup to complete
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Show state after logout
        console.log('\n📋 State after logout:');
        this.showCurrentState();
        
        // Test authentication check after logout
        console.log('\n🔍 Testing authentication check after logout:');
        const isAuthenticated = window.CoreDeskAuth?.utils?.isAuthenticated();
        console.log('   - isAuthenticated():', isAuthenticated);
        
        if (!isAuthenticated) {
            console.log('✅ Logout test PASSED - user is no longer authenticated');
        } else {
            console.log('❌ Logout test FAILED - user is still authenticated');
            console.log('   This indicates tokens were not properly cleared');
        }
        
        return !isAuthenticated;
    }

    // Force clear all authentication data (for testing/debugging)
    forceCleanup() {
        console.log('🧹 FORCE CLEANUP - Manually clearing all auth data...');
        
        const authKeys = [
            'coredesk_token',
            'coredesk_refresh_token',
            'coredesk_token_expiry',
            'coredesk_user',
            'coredesk_encryption_key',
            'auth_token',
            'auth_user'
        ];
        
        authKeys.forEach(key => {
            const existed = !!localStorage.getItem(key);
            if (existed) {
                localStorage.removeItem(key);
                console.log(`   ✅ Removed ${key}`);
            } else {
                console.log(`   ⏭️ ${key} (not present)`);
            }
        });
        
        // Clear session user if available
        if (window.unifiedAuthManager?.authenticationHandler) {
            window.unifiedAuthManager.authenticationHandler.currentUser = null;
            console.log('   ✅ Cleared current user');
        }
        
        console.log('🔍 Verifying cleanup...');
        this.showCurrentState();
        
        const isAuthenticated = window.CoreDeskAuth?.utils?.isAuthenticated();
        if (!isAuthenticated) {
            console.log('✅ Force cleanup SUCCESSFUL - no authentication data remains');
        } else {
            console.log('❌ Force cleanup FAILED - authentication data still present');
        }
    }

    // Test the ENHANCED comprehensive logout fix with persistent protection
    async testEnhancedLogout() {
        console.log('🧪 Testing ENHANCED COMPREHENSIVE LOGOUT FIX...');
        console.log('='.repeat(60));
        
        try {
            // Step 1: Ensure we start with a clean slate
            console.log('📋 Step 1: Starting with clean authentication state...');
            this.forceCleanup();
            
            // Step 2: Login
            console.log('\\n📋 Step 2: Performing login...');
            const loginResult = await window.unifiedAuthManager?.login('<EMAIL>', 'Qazwsx123!', false);
            if (!loginResult?.success) {
                throw new Error('Login failed: ' + loginResult?.error);
            }
            console.log('✅ Login successful');
            
            // Step 3: Verify authentication
            console.log('\\n📋 Step 3: Verifying authentication state after login...');
            this.showCurrentState();
            const isAuthenticatedAfterLogin = window.CoreDeskAuth?.utils?.isAuthenticated();
            if (!isAuthenticatedAfterLogin) {
                throw new Error('User not authenticated after login');
            }
            console.log('✅ User is authenticated after login');
            
            // Step 4: Perform ENHANCED logout with persistent protection
            console.log('\\n📋 Step 4: Performing ENHANCED logout with persistent protection...');
            const logoutResult = await window.unifiedAuthManager?.logout();
            console.log('   - Logout result:', logoutResult);
            
            // Step 5: Verify persistent logout flags were set
            console.log('\\n📋 Step 5: Verifying persistent logout protection flags...');
            const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
            const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
            const sessionFlag = sessionStorage.getItem('_justLoggedOut');
            
            console.log('   - _forceLogoutActive flag:', persistentLogoutFlag);
            console.log('   - _logoutTimestamp:', logoutTimestamp);
            console.log('   - _justLoggedOut flag:', sessionFlag);
            
            if (persistentLogoutFlag !== 'true' || !logoutTimestamp) {
                console.log('❌ WARNING: Persistent logout flags not properly set');
            } else {
                console.log('✅ Persistent logout protection flags properly set');
            }
            
            // Step 6: Wait and verify cleanup
            await new Promise(resolve => setTimeout(resolve, 200));
            console.log('\\n📋 Step 6: Verifying immediate cleanup...');
            this.showCurrentState();
            
            // Step 7: Test authentication check with persistent protection
            const isAuthenticatedAfterLogout = window.CoreDeskAuth?.utils?.isAuthenticated();
            console.log('\\n📋 Step 7: Testing authentication check with persistent protection...');
            console.log('   - isAuthenticated() after logout:', isAuthenticatedAfterLogout);
            
            // Step 8: Simulate page reload by testing authConfig logic
            console.log('\\n📋 Step 8: Simulating page reload authentication check...');
            console.log('   - Testing if authConfig would block automatic re-authentication...');
            
            const now = Date.now();
            const timestampAge = logoutTimestamp ? (now - parseInt(logoutTimestamp)) : 0;
            const wouldBeBlocked = persistentLogoutFlag === 'true' || timestampAge < 10000;
            
            console.log('   - Time since logout:', timestampAge + 'ms');
            console.log('   - Would automatic re-auth be blocked?:', wouldBeBlocked);
            
            // Step 9: Test TokenManager protection
            console.log('\\n📋 Step 9: Testing TokenManager persistent protection...');
            const mockTokenData = { token: 'test-token', refreshToken: 'test-refresh', expiresIn: 3600 };
            
            console.log('   - Attempting to store tokens (should be blocked)...');
            const tokensBefore = !!localStorage.getItem('coredesk_token');
            window.tokenManager?.storeTokens(mockTokenData);
            const tokensAfter = !!localStorage.getItem('coredesk_token');
            
            console.log('   - Tokens before attempt:', tokensBefore);
            console.log('   - Tokens after attempt:', tokensAfter);
            console.log('   - Token storage blocked?:', tokensBefore === tokensAfter);
            
            // Step 10: Wait 11 seconds to test automatic flag clearing
            console.log('\\n📋 Step 10: Testing automatic flag clearing (waiting 11 seconds)...');
            console.log('   - This tests if protection flags are automatically cleared after 10 seconds...');
            
            // Speed up the test by manually advancing time
            const futureTimestamp = (parseInt(logoutTimestamp) - 11000).toString();
            localStorage.setItem('_logoutTimestamp', futureTimestamp);
            
            // Simulate authConfig check after time has passed
            const newTimestampAge = now - parseInt(futureTimestamp);
            const shouldClearFlags = newTimestampAge >= 10000;
            
            console.log('   - Simulated time passed:', newTimestampAge + 'ms');
            console.log('   - Should clear flags now?:', shouldClearFlags);
            
            if (shouldClearFlags) {
                localStorage.removeItem('_forceLogoutActive');
                localStorage.removeItem('_logoutTimestamp');
                console.log('   ✅ Flags would be automatically cleared by authConfig');
            }
            
            // Final verification
            const finalCheck = {
                token: !!localStorage.getItem('coredesk_token'),
                refreshToken: !!localStorage.getItem('coredesk_refresh_token'),
                expiry: !!localStorage.getItem('coredesk_token_expiry'),
                user: !!localStorage.getItem('coredesk_user'),
                authenticated: window.CoreDeskAuth?.utils?.isAuthenticated(),
                persistentProtection: !!localStorage.getItem('_forceLogoutActive')
            };
            
            console.log('\\n📋 Final Enhanced Logout Test Results:');
            console.log('   - Authentication tokens cleared:', !finalCheck.token && !finalCheck.refreshToken);
            console.log('   - User data cleared:', !finalCheck.user && !finalCheck.expiry);
            console.log('   - Authentication state cleared:', !finalCheck.authenticated);
            console.log('   - Persistent protection expired:', !finalCheck.persistentProtection);
            
            // Result
            const success = !finalCheck.token && !finalCheck.refreshToken && 
                          !finalCheck.expiry && !finalCheck.user && 
                          !finalCheck.authenticated && !finalCheck.persistentProtection;
            
            if (success) {
                console.log('\\n🎉 ENHANCED COMPREHENSIVE LOGOUT TEST PASSED!');
                console.log('✅ All tokens cleared and stayed cleared');
                console.log('✅ Persistent logout protection worked correctly');
                console.log('✅ No automatic re-authentication occurred');
                console.log('✅ Authentication bypass prevention working');
                console.log('✅ Protection flags automatically expire after 10 seconds');
            } else {
                console.log('\\n❌ ENHANCED COMPREHENSIVE LOGOUT TEST FAILED!');
                console.log('🐛 Authentication data still present or protection failed');
                console.log('📊 Final state:', finalCheck);
            }
            
            return success;
            
        } catch (error) {
            console.error('❌ Enhanced logout test failed:', error);
            return false;
        }
    }

    // Test the comprehensive logout fix
    async testComprehensiveLogout() {
        console.log('🧪 Testing COMPREHENSIVE LOGOUT FIX...');
        console.log('='.repeat(50));
        
        try {
            // Step 1: Ensure we start with a clean slate
            console.log('📋 Step 1: Starting with clean authentication state...');
            this.forceCleanup();
            
            // Step 2: Login
            console.log('\n📋 Step 2: Performing login...');
            const loginResult = await window.unifiedAuthManager?.login('<EMAIL>', 'Qazwsx123!', false);
            if (!loginResult?.success) {
                throw new Error('Login failed: ' + loginResult?.error);
            }
            console.log('✅ Login successful');
            
            // Step 3: Verify authentication
            console.log('\n📋 Step 3: Verifying authentication state after login...');
            this.showCurrentState();
            const isAuthenticatedAfterLogin = window.CoreDeskAuth?.utils?.isAuthenticated();
            if (!isAuthenticatedAfterLogin) {
                throw new Error('User not authenticated after login');
            }
            console.log('✅ User is authenticated after login');
            
            // Step 4: Perform logout with comprehensive fix
            console.log('\n📋 Step 4: Performing COMPREHENSIVE logout...');
            const logoutResult = await window.unifiedAuthManager?.logout();
            console.log('   - Logout result:', logoutResult);
            
            // Step 5: Wait and verify cleanup
            await new Promise(resolve => setTimeout(resolve, 200));
            console.log('\n📋 Step 5: Verifying comprehensive cleanup...');
            this.showCurrentState();
            
            // Step 6: Test authentication check
            const isAuthenticatedAfterLogout = window.CoreDeskAuth?.utils?.isAuthenticated();
            console.log('\n📋 Step 6: Final authentication check...');
            console.log('   - isAuthenticated() after logout:', isAuthenticatedAfterLogout);
            
            // Step 7: Test protection against re-authentication
            console.log('\n📋 Step 7: Testing protection against immediate re-authentication...');
            console.log('   - _isLoggingOut flag:', window._isLoggingOut);
            
            if (window._isLoggingOut) {
                console.log('⚠️  Warning: Logout flag still set - this should clear automatically');
            }
            
            // Step 8: Wait a moment and verify tokens stay cleared
            console.log('\n📋 Step 8: Waiting 3 seconds to verify tokens remain cleared...');
            console.log('   - This tests if automatic re-authentication is prevented');
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const finalCheck = {
                token: !!localStorage.getItem('coredesk_token'),
                refreshToken: !!localStorage.getItem('coredesk_refresh_token'),
                expiry: !!localStorage.getItem('coredesk_token_expiry'),
                user: !!localStorage.getItem('coredesk_user'),
                authenticated: window.CoreDeskAuth?.utils?.isAuthenticated()
            };
            
            console.log('📋 Final state check:', finalCheck);
            
            // Result
            const success = !finalCheck.token && !finalCheck.refreshToken && !finalCheck.expiry && !finalCheck.user && !finalCheck.authenticated;
            
            if (success) {
                console.log('\n🎉 COMPREHENSIVE LOGOUT TEST PASSED!');
                console.log('✅ All tokens cleared and stayed cleared');
                console.log('✅ No automatic re-authentication occurred');
                console.log('✅ Authentication bypass prevention working');
            } else {
                console.log('\n❌ COMPREHENSIVE LOGOUT TEST FAILED!');
                console.log('🐛 Authentication data still present or re-authentication occurred');
            }
            
            return success;
            
        } catch (error) {
            console.error('❌ Comprehensive logout test failed:', error);
            return false;
        }
    }
}

// Create global instance
window.tokenPersistenceDebugger = new TokenPersistenceDebugger();

console.log('🔧 Token Persistence Debugger loaded!');
console.log('📋 Available commands:');
console.log('   tokenPersistenceDebugger.runPersistenceTest() - Full persistence test');
console.log('   tokenPersistenceDebugger.showCurrentState() - Show current auth state');
console.log('   tokenPersistenceDebugger.testLogout() - Test logout functionality specifically');
console.log('   tokenPersistenceDebugger.testComprehensiveLogout() - Test the COMPREHENSIVE logout fix');
console.log('   tokenPersistenceDebugger.testEnhancedLogout() - Test the ENHANCED logout fix with persistent protection');
console.log('   tokenPersistenceDebugger.forceCleanup() - Force clear all auth data (manual cleanup)');