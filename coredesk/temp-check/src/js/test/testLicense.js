/**
 * Test License Integration
 * Simple test file to verify license API integration works correctly
 */

const { licenseApiService } = require('../services/api');

// Test configuration
const testConfig = {
    email: '<EMAIL>',
    licenseKey: 'TEST-1234-5678-9ABC',
    deviceFingerprint: 'test-device-fingerprint-12345'
};

async function testTrialFlow() {
    console.log('\n=== Testing Complete Trial Flow ===');
    
    try {
        // Step 1: Check eligibility
        console.log('\n--- Step 1: Check Eligibility ---');
        const eligibility = await licenseApiService.checkTrialEligibility(testConfig.deviceFingerprint);
        console.log('Eligibility result:', eligibility);
        
        if (!eligibility.success || !eligibility.eligible) {
            console.log('❌ Not eligible for trial:', eligibility.reason);
            return;
        }
        
        // Step 2: Request trial
        console.log('\n--- Step 2: Request Trial ---');
        const trialRequest = await licenseApiService.requestTrial(
            testConfig.email,
            testConfig.deviceFingerprint
        );
        console.log('Trial request result:', trialRequest);
        
        if (!trialRequest.success) {
            console.log('❌ Trial request failed:', trialRequest.error);
            return;
        }
        
        const requestId = trialRequest.requestId;
        console.log('✅ Trial requested successfully. Request ID:', requestId);
        
        // Step 3: Verify email (would need actual code from email)
        console.log('\n--- Step 3: Verify Email ---');
        const verificationCode = '123456'; // In real scenario, user enters this
        const verification = await licenseApiService.verifyEmail(
            testConfig.email,
            verificationCode,
            requestId
        );
        console.log('Verification result:', verification);
        
        if (!verification.success) {
            console.log('❌ Email verification failed:', verification.error);
            return;
        }
        
        const verificationToken = verification.token;
        console.log('✅ Email verified successfully. Token received.');
        
        // Step 4: Generate trial license
        console.log('\n--- Step 4: Generate Trial License ---');
        const generation = await licenseApiService.generateTrial({
            email: testConfig.email,
            token: verificationToken,
            deviceFingerprint: testConfig.deviceFingerprint,
            requestId: requestId
        });
        console.log('Generation result:', generation);
        
        if (!generation.success) {
            console.log('❌ License generation failed:', generation.error);
            return;
        }
        
        console.log('✅ Trial license generated successfully!');
        console.log('License Key:', generation.licenseKey);
        console.log('Expires At:', generation.expiresAt);
        
        return generation.licenseKey;
        
    } catch (error) {
        console.error('❌ Trial flow error:', error);
    }
}

async function testLicenseValidation(licenseKey) {
    console.log('\n=== Testing License Validation ===');
    
    try {
        const result = await licenseApiService.validateLicense(licenseKey || testConfig.licenseKey);
        
        if (result.success) {
            console.log('✅ License validation successful');
            console.log('Valid:', result.valid);
            console.log('License Info:', result.licenseInfo);
        } else {
            console.log('❌ License validation failed:', result.error);
        }
        
        return result;
    } catch (error) {
        console.error('❌ Validation error:', error);
    }
}

async function testLicenseActivation(licenseKey) {
    console.log('\n=== Testing License Activation ===');
    
    try {
        const deviceInfo = {
            fingerprint: testConfig.deviceFingerprint,
            hostname: 'test-machine',
            platform: process.platform,
            arch: process.arch,
            cpuInfo: { cores: 4, model: 'Test CPU' },
            macAddresses: ['00:00:00:00:00:00'],
            displayInfo: { width: 1920, height: 1080 }
        };
        
        const result = await licenseApiService.activateLicense(
            licenseKey || testConfig.licenseKey,
            deviceInfo
        );
        
        if (result.success) {
            console.log('✅ License activation successful');
            console.log('Activation ID:', result.activationId);
            console.log('Features:', result.features);
            console.log('Expires At:', result.expiresAt);
        } else {
            console.log('❌ License activation failed:', result.error);
        }
        
        return result;
    } catch (error) {
        console.error('❌ Activation error:', error);
    }
}

async function testLicenseInfo(licenseKey) {
    console.log('\n=== Testing License Info ===');
    
    try {
        const result = await licenseApiService.getLicenseInfo(licenseKey || testConfig.licenseKey);
        
        if (result.success) {
            console.log('✅ License info retrieved successfully');
            console.log('License Info:', result.licenseInfo);
            
            // Format expiry info
            if (result.licenseInfo.expiresAt) {
                const expiryInfo = licenseApiService.formatExpiryInfo(result.licenseInfo.expiresAt);
                console.log('Expiry Info:', expiryInfo);
            }
        } else {
            console.log('❌ Failed to get license info:', result.error);
        }
    } catch (error) {
        console.error('❌ License info error:', error);
    }
}

// Run all tests
async function runTests() {
    console.log('Starting License Integration Tests...');
    console.log('API Base URL:', process.env.API_BASE_URL || 'Not set');
    
    // Test complete trial flow
    const licenseKey = await testTrialFlow();
    
    if (licenseKey) {
        // Test validation with generated key
        await testLicenseValidation(licenseKey);
        
        // Test activation with generated key
        await testLicenseActivation(licenseKey);
        
        // Test license info
        await testLicenseInfo(licenseKey);
    } else {
        // Test with hardcoded key
        console.log('\n--- Testing with hardcoded license key ---');
        await testLicenseValidation();
        await testLicenseActivation();
        await testLicenseInfo();
    }
    
    console.log('\n✅ All tests completed');
}

// Export for use in Electron dev tools console
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testTrialFlow,
        testLicenseValidation,
        testLicenseActivation,
        testLicenseInfo,
        runTests
    };
}

// Run tests if called directly
if (require.main === module) {
    runTests().catch(console.error);
}