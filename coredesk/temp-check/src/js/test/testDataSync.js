/**
 * Test Data Synchronization
 * Simple test file to verify data sync integration works correctly
 */

const DataSyncService = require('../services/DataSyncService');

async function testSyncStatus() {
    console.log('\n=== Testing Sync Status ===');
    
    const status = window.dataSyncService.getSyncStatus();
    console.log('Sync Status:', status);
    
    console.log('Details:');
    console.log(`- Initialized: ${status.isInitialized}`);
    console.log(`- Sync Enabled: ${status.syncEnabled}`);
    console.log(`- Cloud Enabled: ${status.cloudSyncEnabled}`);
    console.log(`- Online: ${status.isOnline}`);
    console.log(`- Currently Syncing: ${status.isSyncing}`);
    console.log(`- Firebase Connected: ${status.firebaseConnected}`);
    console.log(`- Queue Size: ${status.queueSize}`);
    console.log(`- Conflicts: ${status.conflictCount}`);
    console.log(`- Last Sync: ${status.lastSyncTimestamp || 'Never'}`);
    
    return status;
}

async function testCloudAccess() {
    console.log('\n=== Testing Cloud Access ===');
    
    const hasAccess = await window.dataSyncService.hasCloudAccess();
    console.log(`Cloud Access: ${hasAccess ? '✅ Enabled' : '❌ Disabled'}`);
    
    if (!hasAccess) {
        console.log('Note: Cloud sync requires Professional or Enterprise license');
    }
    
    return hasAccess;
}

async function testEnableSync() {
    console.log('\n=== Testing Enable Sync ===');
    
    try {
        await window.dataSyncService.enableSync();
        console.log('✅ Sync enabled successfully');
        
        const status = window.dataSyncService.getSyncStatus();
        console.log('New status:', {
            syncEnabled: status.syncEnabled,
            cloudEnabled: status.cloudSyncEnabled,
            firebaseConnected: status.firebaseConnected
        });
        
    } catch (error) {
        console.error('❌ Failed to enable sync:', error.message);
    }
}

async function testAddToSyncQueue() {
    console.log('\n=== Testing Add to Sync Queue ===');
    
    // Simulate a data change
    const testData = {
        type: 'data-change',
        table: 'cases',
        action: 'UPDATE',
        recordId: `test_case_${Date.now()}`,
        data: {
            title: 'Test Case',
            description: 'Testing sync functionality',
            status: 'active',
            updatedAt: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
    };
    
    window.dataSyncService.addToSyncQueue(testData);
    console.log('✅ Added item to sync queue');
    
    const status = window.dataSyncService.getSyncStatus();
    console.log(`Queue size: ${status.queueSize}`);
}

async function testForceSync() {
    console.log('\n=== Testing Force Sync ===');
    
    console.log('Forcing immediate sync...');
    const result = await window.dataSyncService.forceSync();
    
    if (result.success) {
        console.log('✅ Sync completed successfully');
        console.log('Results:', result.operations);
    } else {
        console.log('❌ Sync failed:', result.reason || result.error);
    }
    
    return result;
}

async function testUpdateConfig() {
    console.log('\n=== Testing Update Configuration ===');
    
    const newConfig = {
        syncIntervalMs: 60000, // 1 minute
        enableRealtime: true,
        conflictResolutionStrategy: 'server-wins'
    };
    
    await window.dataSyncService.updateConfig(newConfig);
    console.log('✅ Configuration updated');
    
    const status = window.dataSyncService.getSyncStatus();
    console.log('New config:', status.config);
}

async function testNetworkHandling() {
    console.log('\n=== Testing Network Handling ===');
    
    // Listen for network changes
    window.dataSyncService.on('network-changed', (data) => {
        console.log(`Network status changed: ${data.isOnline ? 'Online' : 'Offline'}`);
    });
    
    // Test offline simulation (requires manual network disconnect)
    console.log('Current network status:', navigator.onLine ? 'Online' : 'Offline');
    console.log('To test offline handling, disconnect your network connection');
}

async function testSyncEvents() {
    console.log('\n=== Testing Sync Events ===');
    
    // Subscribe to sync events
    const events = [
        'sync-started',
        'sync-completed',
        'sync-error',
        'remote-change',
        'conflict-requires-resolution'
    ];
    
    events.forEach(event => {
        window.dataSyncService.on(event, (data) => {
            console.log(`📡 Event: ${event}`, data);
        });
    });
    
    console.log('✅ Subscribed to sync events');
    console.log('Events will be logged as they occur during sync operations');
}

async function testConflictResolution() {
    console.log('\n=== Testing Conflict Resolution ===');
    
    const status = window.dataSyncService.getSyncStatus();
    console.log(`Current strategy: ${status.config.conflictResolutionStrategy}`);
    console.log(`Pending conflicts: ${status.conflictCount}`);
    
    // Listen for conflicts
    window.dataSyncService.on('conflict-requires-resolution', (conflict) => {
        console.log('⚠️ Conflict detected:', conflict);
        console.log('Manual resolution required');
    });
}

// Run all tests
async function runTests() {
    console.log('Starting Data Sync Integration Tests...');
    console.log('Note: Sync requires valid authentication and appropriate license');
    
    // Test sequence
    const status = await testSyncStatus();
    
    if (!status.isInitialized) {
        console.log('\n⚠️ DataSyncService not initialized properly');
        return;
    }
    
    const hasCloudAccess = await testCloudAccess();
    
    if (hasCloudAccess) {
        await testSyncEvents();
        await testEnableSync();
        await testAddToSyncQueue();
        await testForceSync();
        await testUpdateConfig();
        await testConflictResolution();
    } else {
        console.log('\n⚠️ Cloud sync tests skipped - no cloud access');
    }
    
    await testNetworkHandling();
    
    console.log('\n✅ All tests completed');
}

// Export for use in Electron dev tools console
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testSyncStatus,
        testCloudAccess,
        testEnableSync,
        testAddToSyncQueue,
        testForceSync,
        testUpdateConfig,
        testNetworkHandling,
        testSyncEvents,
        testConflictResolution,
        runTests
    };
}

// Run tests if called directly
if (require.main === module) {
    runTests().catch(console.error);
}