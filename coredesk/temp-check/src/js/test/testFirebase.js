/**
 * Test Firebase Integration
 * Simple test file to verify Firebase integration works correctly
 */

const FirebaseConnector = require('../services/FirebaseConnector');

// Test configuration
const testConfig = {
    email: '<EMAIL>',
    password: 'testPassword123',
    customToken: null // Will be set from backend auth
};

let firebaseConnector;

async function initializeFirebase() {
    console.log('\n=== Initializing Firebase ===');
    
    try {
        firebaseConnector = new FirebaseConnector({
            apiKey: process.env.FIREBASE_API_KEY,
            authDomain: process.env.FIREBASE_AUTH_DOMAIN,
            projectId: process.env.FIREBASE_PROJECT_ID,
            storageBucket: process.env.FIREBASE_STORAGE_BUCKET
        });
        
        // Wait for initialization
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const status = firebaseConnector.getConnectionStatus();
        console.log('✅ Firebase initialized');
        console.log('Status:', status);
        
        return firebaseConnector;
    } catch (error) {
        console.error('❌ Firebase initialization failed:', error);
        throw error;
    }
}

async function testAuthentication() {
    console.log('\n=== Testing Firebase Authentication ===');
    
    try {
        // Try to authenticate with email/password
        const result = await firebaseConnector.authenticate({
            email: testConfig.email,
            password: testConfig.password
        });
        
        if (result.success) {
            console.log('✅ Authentication successful');
            console.log('User:', result.user);
        } else {
            console.log('❌ Authentication failed:', result.error);
            console.log('Note: You may need to create a user in Firebase Console first');
        }
        
        return result;
    } catch (error) {
        console.error('❌ Authentication error:', error);
        return { success: false, error: error.message };
    }
}

async function testFirestoreOperations() {
    console.log('\n=== Testing Firestore Operations ===');
    
    if (!firebaseConnector.isAuthenticated) {
        console.log('⚠️ Not authenticated, skipping Firestore tests');
        return;
    }
    
    try {
        const docId = `test_doc_${Date.now()}`;
        const testData = {
            name: 'Test Document',
            type: 'test',
            createdAt: new Date().toISOString(),
            metadata: {
                source: 'test-script',
                version: 1
            }
        };
        
        // Test 1: Save document
        console.log('\n--- Test 1: Save Document ---');
        const saveResult = await firebaseConnector.saveDocument('test', docId, testData);
        console.log('✅ Document saved:', saveResult);
        
        // Test 2: Get document
        console.log('\n--- Test 2: Get Document ---');
        const getResult = await firebaseConnector.getDocument('test', docId);
        console.log('✅ Document retrieved:', getResult);
        
        // Test 3: Update document
        console.log('\n--- Test 3: Update Document ---');
        const updateResult = await firebaseConnector.updateDocument('test', docId, {
            name: 'Updated Test Document',
            lastModified: new Date().toISOString()
        });
        console.log('✅ Document updated:', updateResult);
        
        // Test 4: Query documents
        console.log('\n--- Test 4: Query Documents ---');
        const queryResult = await firebaseConnector.queryDocuments('test', {
            where: [['type', '==', 'test']],
            limit: 10
        });
        console.log(`✅ Query returned ${queryResult.length} documents`);
        
        // Test 5: Delete document
        console.log('\n--- Test 5: Delete Document ---');
        const deleteResult = await firebaseConnector.deleteDocument('test', docId);
        console.log('✅ Document deleted:', deleteResult);
        
    } catch (error) {
        console.error('❌ Firestore operation failed:', error);
    }
}

async function testRealtimeListener() {
    console.log('\n=== Testing Realtime Listener ===');
    
    if (!firebaseConnector.isAuthenticated) {
        console.log('⚠️ Not authenticated, skipping realtime tests');
        return;
    }
    
    try {
        // Setup listener
        const listenerId = firebaseConnector.setupRealtimeListener(
            'test',
            { where: [['type', '==', 'test']] },
            (change) => {
                console.log('📡 Realtime change:', change);
            }
        );
        
        console.log(`✅ Listener setup with ID: ${listenerId}`);
        
        // Create a document to trigger the listener
        const testDocId = `realtime_test_${Date.now()}`;
        await firebaseConnector.saveDocument('test', testDocId, {
            name: 'Realtime Test',
            type: 'test',
            timestamp: new Date().toISOString()
        });
        
        // Wait for listener to process
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Clean up
        firebaseConnector.removeRealtimeListener(listenerId);
        await firebaseConnector.deleteDocument('test', testDocId);
        
        console.log('✅ Realtime listener test completed');
        
    } catch (error) {
        console.error('❌ Realtime listener test failed:', error);
    }
}

async function testBatchOperations() {
    console.log('\n=== Testing Batch Operations ===');
    
    if (!firebaseConnector.isAuthenticated) {
        console.log('⚠️ Not authenticated, skipping batch tests');
        return;
    }
    
    try {
        const operations = [
            {
                type: 'set',
                collection: 'test',
                docId: `batch_doc_1_${Date.now()}`,
                data: { name: 'Batch Doc 1', type: 'batch-test' }
            },
            {
                type: 'set',
                collection: 'test',
                docId: `batch_doc_2_${Date.now()}`,
                data: { name: 'Batch Doc 2', type: 'batch-test' }
            },
            {
                type: 'update',
                collection: 'test',
                docId: `batch_doc_1_${Date.now()}`,
                data: { updated: true }
            }
        ];
        
        const batchResult = await firebaseConnector.batchWrite(operations);
        console.log('✅ Batch operations completed:', batchResult);
        
        // Clean up
        for (const op of operations) {
            if (op.type !== 'delete') {
                try {
                    await firebaseConnector.deleteDocument(op.collection, op.docId);
                } catch (e) {
                    // Ignore cleanup errors
                }
            }
        }
        
    } catch (error) {
        console.error('❌ Batch operations failed:', error);
    }
}

async function testStorageOperations() {
    console.log('\n=== Testing Storage Operations ===');
    
    if (!firebaseConnector.isAuthenticated) {
        console.log('⚠️ Not authenticated, skipping storage tests');
        return;
    }
    
    try {
        // Create a test file (Blob)
        const testContent = 'This is a test file for Firebase Storage';
        const blob = new Blob([testContent], { type: 'text/plain' });
        const fileName = `test_${Date.now()}.txt`;
        const storagePath = `test-uploads/${fileName}`;
        
        // Test upload
        console.log('\n--- Test Upload ---');
        const uploadResult = await firebaseConnector.uploadFile(
            storagePath,
            blob,
            (progress) => {
                console.log(`Upload progress: ${progress.toFixed(2)}%`);
            }
        );
        console.log('✅ File uploaded:', uploadResult);
        
        // Test download URL
        console.log('\n--- Test Download URL ---');
        const downloadURL = await firebaseConnector.getFileDownloadURL(storagePath);
        console.log('✅ Download URL:', downloadURL);
        
        // Test delete
        console.log('\n--- Test Delete ---');
        const deleteResult = await firebaseConnector.deleteFile(storagePath);
        console.log('✅ File deleted:', deleteResult);
        
    } catch (error) {
        console.error('❌ Storage operation failed:', error);
    }
}

async function testOfflineMode() {
    console.log('\n=== Testing Offline Mode ===');
    
    if (!firebaseConnector.isAuthenticated) {
        console.log('⚠️ Not authenticated, skipping offline tests');
        return;
    }
    
    try {
        // Disable network
        await firebaseConnector.setNetworkEnabled(false);
        console.log('✅ Network disabled');
        
        // Try to save a document (should work offline)
        const offlineDoc = {
            name: 'Offline Test',
            type: 'offline-test',
            createdOffline: true
        };
        
        const saveResult = await firebaseConnector.saveDocument(
            'test', 
            `offline_doc_${Date.now()}`, 
            offlineDoc
        );
        console.log('✅ Document saved offline:', saveResult);
        
        // Re-enable network
        await firebaseConnector.setNetworkEnabled(true);
        console.log('✅ Network re-enabled');
        
    } catch (error) {
        console.error('❌ Offline mode test failed:', error);
    }
}

async function cleanup() {
    console.log('\n=== Cleanup ===');
    
    if (firebaseConnector && firebaseConnector.isAuthenticated) {
        // Remove all listeners
        firebaseConnector.removeAllListeners();
        
        // Sign out
        await firebaseConnector.signOut();
        console.log('✅ Signed out from Firebase');
    }
}

// Run all tests
async function runTests() {
    console.log('Starting Firebase Integration Tests...');
    console.log('Firebase Config:', {
        apiKey: process.env.FIREBASE_API_KEY ? '***' : 'Not set',
        authDomain: process.env.FIREBASE_AUTH_DOMAIN || 'Not set',
        projectId: process.env.FIREBASE_PROJECT_ID || 'Not set',
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET || 'Not set'
    });
    
    try {
        // Initialize Firebase
        await initializeFirebase();
        
        // Run authentication test
        const authResult = await testAuthentication();
        
        // Only run other tests if authenticated
        if (authResult.success) {
            await testFirestoreOperations();
            await testRealtimeListener();
            await testBatchOperations();
            await testStorageOperations();
            await testOfflineMode();
        }
        
    } catch (error) {
        console.error('Test suite error:', error);
    } finally {
        // Cleanup
        await cleanup();
    }
    
    console.log('\n✅ All tests completed');
}

// Export for use in Electron dev tools console
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeFirebase,
        testAuthentication,
        testFirestoreOperations,
        testRealtimeListener,
        testBatchOperations,
        testStorageOperations,
        testOfflineMode,
        runTests
    };
}

// Run tests if called directly
if (require.main === module) {
    runTests().catch(console.error);
}