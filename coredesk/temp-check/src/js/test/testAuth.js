/**
 * Authentication Test Script
 * Tests authentication system with mock data to verify functionality
 */

class AuthTester {
    constructor() {
        // SECURITY: Test credentials should be provided via environment variables or test config
        // Never hardcode real credentials in source code
        
        // Check if we're in a Node.js environment where process is available
        const hasProcess = typeof process !== 'undefined' && process.env;
        
        this.testCredentials = {
            email: hasProcess && process.env.TEST_EMAIL ? process.env.TEST_EMAIL : '<EMAIL>',
            password: hasProcess && process.env.TEST_PASSWORD ? process.env.TEST_PASSWORD : 'TestPassword123!'
        };
        this.testResults = [];
        
        // Warn if using default test credentials
        if (!hasProcess || !process.env.TEST_EMAIL || !process.env.TEST_PASSWORD) {
            console.warn('⚠️ AuthTester: Using default test credentials. In Electron, environment variables are not available in renderer process.');
        }
    }

    async runAllTests() {
        console.log('🧪 Starting Authentication Tests...\n');
        
        try {
            await this.testDependencies();
            await this.testMockLogin();
            await this.testTokenManagement();
            await this.testAuthState();
            await this.testCleanup();
            
            this.showResults();
        } catch (error) {
            console.error('❌ Test suite failed:', error);
        }
    }

    async testDependencies() {
        console.log('📦 Testing Dependencies...');
        
        // Check if required objects exist
        const checks = [
            { name: 'window.unifiedAuthManager', exists: !!window.unifiedAuthManager },
            { name: 'window.authApiService', exists: !!window.authApiService },
            { name: 'window.tokenManager', exists: !!window.tokenManager },
            { name: 'window.CoreDeskAuth', exists: !!window.CoreDeskAuth }
        ];

        checks.forEach(check => {
            if (check.exists) {
                console.log(`✅ ${check.name} - Available`);
                this.addResult('Dependencies', check.name, 'PASS', 'Available');
            } else {
                console.log(`❌ ${check.name} - Missing`);
                this.addResult('Dependencies', check.name, 'FAIL', 'Missing');
            }
        });

        // Initialize dependencies if needed
        if (window.unifiedAuthManager) {
            window.unifiedAuthManager.initializeDependencies();
            console.log('🔄 Dependencies initialized');
        }
    }

    async testMockLogin() {
        console.log('\n🔐 Testing Mock Authentication...');
        
        try {
            // Test direct API service mock login
            const apiResult = await window.authApiService.mockLogin(
                this.testCredentials.email, 
                this.testCredentials.password, 
                false
            );

            if (apiResult.success) {
                console.log('✅ AuthApiService mock login - SUCCESS');
                console.log('📊 User data:', apiResult.data.user);
                this.addResult('Mock Login', 'AuthApiService', 'PASS', 'Login successful');
            } else {
                console.log('❌ AuthApiService mock login - FAILED');
                console.log('💡 Error:', apiResult.error);
                this.addResult('Mock Login', 'AuthApiService', 'FAIL', apiResult.error);
            }

            // Test through UnifiedAuthManager
            const authResult = await window.unifiedAuthManager.login(
                this.testCredentials.email,
                this.testCredentials.password,
                false
            );

            if (authResult.success) {
                console.log('✅ UnifiedAuthManager login - SUCCESS');
                console.log('👤 Current user:', authResult.user);
                this.addResult('Mock Login', 'UnifiedAuthManager', 'PASS', 'Login successful');
            } else {
                console.log('❌ UnifiedAuthManager login - FAILED');
                console.log('💡 Error:', authResult.error);
                this.addResult('Mock Login', 'UnifiedAuthManager', 'FAIL', authResult.error);
            }

        } catch (error) {
            console.log('❌ Mock login test failed:', error.message);
            this.addResult('Mock Login', 'Exception', 'FAIL', error.message);
        }
    }

    async testTokenManagement() {
        console.log('\n🎫 Testing Token Management...');
        
        try {
            // Check if token was stored
            const token = window.tokenManager.getToken();
            if (token) {
                console.log('✅ Token stored successfully');
                console.log('🔍 Token preview:', token.substring(0, 50) + '...');
                this.addResult('Token Management', 'Storage', 'PASS', 'Token stored');
            } else {
                console.log('❌ No token found in storage');
                this.addResult('Token Management', 'Storage', 'FAIL', 'No token stored');
            }

            // Test token validation
            const isValid = window.tokenManager.isTokenValid();
            if (isValid) {
                console.log('✅ Token is valid');
                this.addResult('Token Management', 'Validation', 'PASS', 'Token valid');
            } else {
                console.log('❌ Token is invalid or expired');
                this.addResult('Token Management', 'Validation', 'FAIL', 'Token invalid');
            }

            // Test token decoding
            const decoded = window.tokenManager.decodeToken();
            if (decoded) {
                console.log('✅ Token decoded successfully');
                console.log('📋 Token claims:', {
                    email: decoded.email,
                    role: decoded.role,
                    exp: new Date(decoded.exp * 1000)
                });
                this.addResult('Token Management', 'Decoding', 'PASS', 'Token decoded');
            } else {
                console.log('❌ Failed to decode token');
                this.addResult('Token Management', 'Decoding', 'FAIL', 'Decode failed');
            }

        } catch (error) {
            console.log('❌ Token management test failed:', error.message);
            this.addResult('Token Management', 'Exception', 'FAIL', error.message);
        }
    }

    async testAuthState() {
        console.log('\n🏠 Testing Authentication State...');
        
        try {
            // Test CoreDeskAuth utilities
            const isAuthenticated = window.CoreDeskAuth.utils.isAuthenticated();
            if (isAuthenticated) {
                console.log('✅ User is authenticated (CoreDeskAuth)');
                this.addResult('Auth State', 'CoreDeskAuth', 'PASS', 'Authenticated');
            } else {
                console.log('❌ User not authenticated (CoreDeskAuth)');
                this.addResult('Auth State', 'CoreDeskAuth', 'FAIL', 'Not authenticated');
            }

            // Test current user retrieval
            const currentUser = window.CoreDeskAuth.utils.getCurrentUser();
            if (currentUser) {
                console.log('✅ Current user retrieved');
                console.log('👤 User info:', {
                    email: currentUser.email,
                    name: currentUser.name,
                    role: currentUser.role
                });
                this.addResult('Auth State', 'Current User', 'PASS', 'User retrieved');
            } else {
                console.log('❌ No current user found');
                this.addResult('Auth State', 'Current User', 'FAIL', 'No user found');
            }

            // Test auth status from UnifiedAuthManager
            const status = window.unifiedAuthManager.getStatus();
            console.log('📊 Auth Manager Status:', status);
            this.addResult('Auth State', 'Manager Status', 'INFO', JSON.stringify(status, null, 2));

        } catch (error) {
            console.log('❌ Auth state test failed:', error.message);
            this.addResult('Auth State', 'Exception', 'FAIL', error.message);
        }
    }

    async testCleanup() {
        console.log('\n🧹 Testing Cleanup...');
        
        try {
            // Test logout functionality
            const logoutResult = await window.unifiedAuthManager.logout();
            if (logoutResult) {
                console.log('✅ Logout successful');
                this.addResult('Cleanup', 'Logout', 'PASS', 'Logout successful');
            } else {
                console.log('❌ Logout failed');
                this.addResult('Cleanup', 'Logout', 'FAIL', 'Logout failed');
            }

            // Verify cleanup
            const tokenAfterLogout = window.tokenManager.getToken();
            const userAfterLogout = window.CoreDeskAuth.utils.getCurrentUser();
            
            if (!tokenAfterLogout && !userAfterLogout) {
                console.log('✅ Cleanup successful - all data cleared');
                this.addResult('Cleanup', 'Data Clearing', 'PASS', 'All data cleared');
            } else {
                console.log('❌ Cleanup incomplete - some data remains');
                this.addResult('Cleanup', 'Data Clearing', 'FAIL', 'Data remains');
            }

        } catch (error) {
            console.log('❌ Cleanup test failed:', error.message);
            this.addResult('Cleanup', 'Exception', 'FAIL', error.message);
        }
    }

    addResult(category, test, status, details) {
        this.testResults.push({
            category,
            test,
            status,
            details,
            timestamp: new Date().toISOString()
        });
    }

    showResults() {
        console.log('\n📋 TEST RESULTS SUMMARY');
        console.log('='.repeat(50));
        
        const categories = [...new Set(this.testResults.map(r => r.category))];
        
        categories.forEach(category => {
            console.log(`\n📁 ${category}:`);
            const categoryResults = this.testResults.filter(r => r.category === category);
            
            categoryResults.forEach(result => {
                const icon = result.status === 'PASS' ? '✅' : 
                           result.status === 'FAIL' ? '❌' : '📋';
                console.log(`   ${icon} ${result.test}: ${result.details}`);
            });
        });

        const passCount = this.testResults.filter(r => r.status === 'PASS').length;
        const failCount = this.testResults.filter(r => r.status === 'FAIL').length;
        const totalCount = this.testResults.filter(r => r.status !== 'INFO').length;

        console.log('\n📊 OVERALL RESULTS:');
        console.log(`   ✅ Passed: ${passCount}`);
        console.log(`   ❌ Failed: ${failCount}`);
        console.log(`   📋 Total: ${totalCount}`);
        console.log(`   🎯 Success Rate: ${Math.round((passCount / totalCount) * 100)}%`);
    }

    // Quick test method for immediate verification
    async quickTest() {
        console.log('⚡ Quick Authentication Test...\n');
        
        // CRITICAL: Check for logout protection before running test
        const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
        const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
        const now = Date.now();
        
        if (persistentLogoutFlag === 'true' || (logoutTimestamp && (now - parseInt(logoutTimestamp)) < 10000)) {
            console.log('❌ QUICK TEST BLOCKED - Logout protection active');
            return false;
        }
        
        if (window._isLoggingOut) {
            console.log('❌ QUICK TEST BLOCKED - Logout in progress');
            return false;
        }
        
        try {
            const result = await window.unifiedAuthManager.login(
                this.testCredentials.email,
                this.testCredentials.password,
                false
            );

            if (result.success) {
                console.log('✅ QUICK TEST PASSED');
                console.log('👤 Logged in as:', result.user.email);
                console.log('🎭 Role:', result.user.role);
                return true;
            } else {
                console.log('❌ QUICK TEST FAILED');
                console.log('💡 Error:', result.error);
                return false;
            }
        } catch (error) {
            console.log('❌ QUICK TEST ERROR:', error.message);
            return false;
        }
    }

    // Method to test specific credentials
    async testCredentials(email, password) {
        console.log(`🔍 Testing credentials: ${email}`);
        
        try {
            const result = await window.unifiedAuthManager.login(email, password, false);
            
            if (result.success) {
                console.log('✅ Credentials work!');
                console.log('👤 User:', result.user);
                return result;
            } else {
                console.log('❌ Credentials failed:', result.error);
                return result;
            }
        } catch (error) {
            console.log('❌ Credential test error:', error.message);
            return { success: false, error: error.message };
        }
    }

    // Debug localStorage contents
    debugStorage() {
        console.log('🗄️ LOCAL STORAGE DEBUG:');
        console.log('='.repeat(30));
        
        const authKeys = [
            'coredesk_token',
            'coredesk_refresh_token', 
            'coredesk_token_expiry',
            'coredesk_user',
            'coredesk_encryption_key'
        ];

        authKeys.forEach(key => {
            const value = localStorage.getItem(key);
            if (value) {
                console.log(`📄 ${key}:`, value.substring(0, 100) + (value.length > 100 ? '...' : ''));
            } else {
                console.log(`📄 ${key}: (empty)`);
            }
        });
    }

    // Clear all auth data for fresh start
    clearAuthData() {
        console.log('🧹 Clearing all authentication data...');
        
        if (window.tokenManager) {
            window.tokenManager.clearTokens();
        }
        
        if (window.CoreDeskAuth) {
            window.CoreDeskAuth.utils.clearAuthData();
        }
        
        // Manual cleanup as fallback
        const authKeys = [
            'coredesk_token',
            'coredesk_refresh_token', 
            'coredesk_token_expiry',
            'coredesk_user',
            'coredesk_encryption_key',
            'auth_token',
            'auth_user'
        ];
        
        authKeys.forEach(key => localStorage.removeItem(key));
        
        console.log('✅ Authentication data cleared');
    }

    // Test logout functionality
    async testLogout() {
        console.log('🚪 Testing logout functionality...');
        
        try {
            if (!window.unifiedAuthManager) {
                console.log('❌ UnifiedAuthManager not available');
                return false;
            }

            console.log('📋 Auth state before logout:');
            console.log('   - Token:', !!window.tokenManager?.getToken());
            console.log('   - User:', !!window.CoreDeskAuth?.utils?.getCurrentUser());
            console.log('   - Authenticated:', !!window.CoreDeskAuth?.utils?.isAuthenticated());

            const result = await window.unifiedAuthManager.logout();
            
            console.log('📋 Logout result:', result);
            console.log('📋 Auth state after logout:');
            console.log('   - Token:', !!window.tokenManager?.getToken());
            console.log('   - User:', !!window.CoreDeskAuth?.utils?.getCurrentUser());
            console.log('   - Authenticated:', !!window.CoreDeskAuth?.utils?.isAuthenticated());

            if (result !== false) {
                console.log('✅ Logout test passed');
                return true;
            } else {
                console.log('❌ Logout test failed');
                return false;
            }
        } catch (error) {
            console.log('❌ Logout test error:', error);
            return false;
        }
    }

    // Test token encryption/decryption
    testTokenEncryption() {
        console.log('🔐 Testing token encryption...');
        
        try {
            if (!window.tokenManager) {
                console.log('❌ TokenManager not available');
                return false;
            }

            const testToken = 'test-token-123';
            console.log('📝 Original token:', testToken);
            
            const encrypted = window.tokenManager.encrypt(testToken);
            console.log('🔒 Encrypted token:', encrypted);
            
            const decrypted = window.tokenManager.decrypt(encrypted);
            console.log('🔓 Decrypted token:', decrypted);
            
            if (testToken === decrypted) {
                console.log('✅ Token encryption test passed');
                return true;
            } else {
                console.log('❌ Token encryption test failed - tokens don\'t match');
                return false;
            }
        } catch (error) {
            console.log('❌ Token encryption test error:', error);
            return false;
        }
    }

    // Test complete authentication persistence
    async testPersistence() {
        console.log('💾 Testing authentication persistence...');
        
        try {
            console.log('📋 Step 1: Clear any existing auth data');
            this.clearAuthData();
            
            console.log('📋 Step 2: Verify we start unauthenticated');
            const beforeAuth = window.CoreDeskAuth?.utils?.isAuthenticated();
            console.log('   - Authenticated before login:', beforeAuth);
            
            console.log('📋 Step 3: Perform mock login');
            const loginResult = await window.unifiedAuthManager.login(
                this.testCredentials.email,
                this.testCredentials.password,
                false
            );
            
            if (!loginResult.success) {
                console.log('❌ Login failed:', loginResult.error);
                return false;
            }
            
            console.log('✅ Login successful');
            
            console.log('📋 Step 4: Check immediate auth state');
            const immediateAuth = window.CoreDeskAuth?.utils?.isAuthenticated();
            const immediateToken = window.tokenManager?.getToken();
            const immediateExpiry = window.tokenManager?.getTokenExpiry();
            
            console.log('   - Authenticated immediately after login:', immediateAuth);
            console.log('   - Token exists:', !!immediateToken);
            console.log('   - Token expiry:', immediateExpiry);
            console.log('   - Raw localStorage token:', localStorage.getItem('coredesk_token'));
            console.log('   - Raw localStorage expiry:', localStorage.getItem('coredesk_token_expiry'));
            
            if (!immediateAuth || !immediateToken || !immediateExpiry) {
                console.log('❌ Authentication not properly set immediately after login');
                return false;
            }
            
            console.log('📋 Step 5: Simulate page reload by re-checking auth state');
            
            // Create a fresh check without relying on in-memory state
            const tokenFromStorage = localStorage.getItem('coredesk_token');
            const expiryFromStorage = localStorage.getItem('coredesk_token_expiry');
            
            console.log('   - Token in localStorage:', !!tokenFromStorage);
            console.log('   - Expiry in localStorage:', !!expiryFromStorage);
            
            if (tokenFromStorage && expiryFromStorage) {
                const decryptedToken = window.tokenManager?.decrypt(tokenFromStorage);
                const expiryDate = new Date(expiryFromStorage);
                const isValid = new Date() < expiryDate;
                
                console.log('   - Decrypted token:', !!decryptedToken);
                console.log('   - Expiry date:', expiryDate);
                console.log('   - Is token still valid:', isValid);
                
                // Test the isAuthenticated method again
                const persistentAuth = window.CoreDeskAuth?.utils?.isAuthenticated();
                console.log('   - isAuthenticated() after storage check:', persistentAuth);
                
                if (persistentAuth && isValid) {
                    console.log('✅ Authentication persistence test passed');
                    return true;
                } else {
                    console.log('❌ Authentication not persistent - token exists but isAuthenticated() returns false');
                    return false;
                }
            } else {
                console.log('❌ Token or expiry not found in localStorage');
                return false;
            }
            
        } catch (error) {
            console.log('❌ Persistence test error:', error);
            return false;
        }
    }
}

// Create global tester instance
window.authTester = new AuthTester();

// Auto-run quick test if requested (but only if not in logout protection mode)
if (window.location.search.includes('autotest=true')) {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            // Check for logout protection before running auto-test
            const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
            const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
            const now = Date.now();
            
            if (persistentLogoutFlag === 'true' || (logoutTimestamp && (now - parseInt(logoutTimestamp)) < 10000)) {
                console.log('testAuth: Auto-test BLOCKED - logout protection active');
                return;
            }
            
            if (window._isLoggingOut) {
                console.log('testAuth: Auto-test BLOCKED - logout in progress');
                return;
            }
            
            window.authTester.quickTest();
        }, 1000);
    });
}

console.log('🧪 Authentication Tester loaded!');
console.log('📋 Available commands:');
console.log('   authTester.runAllTests() - Run complete test suite');
console.log('   authTester.quickTest() - Quick authentication test');
console.log('   authTester.testCredentials(email, password) - Test specific credentials');
console.log('   authTester.testLogout() - Test logout functionality');
console.log('   authTester.testTokenEncryption() - Test token encryption/decryption');
console.log('   authTester.debugStorage() - Show localStorage contents');
console.log('   authTester.clearAuthData() - Clear all auth data');