/**
 * DynamicStyleLoader
 * Service for dynamically loading and unloading CSS styles for modules in CoreDesk Framework v2.0.0
 * 
 * Features:
 * - Load module CSS dynamically without page reload
 * - Unload module CSS cleanly
 * - CSS scoping and isolation
 * - Style conflict detection and resolution
 * - Theme integration and CSS variable support
 * - Performance optimization with style caching
 * - Hot reload support for development
 */

class DynamicStyleLoader extends EventTarget {
    constructor(options = {}) {
        super();
        
        // Configuration
        this.config = {
            scopeStyles: true,                // Scope styles to prevent conflicts
            enableCaching: true,              // Cache parsed styles
            enableHotReload: false,           // Hot reload for development
            validateCSS: true,                // Basic CSS validation
            prefixClasses: true,              // Add module prefix to classes
            maxCacheSize: 50,                 // Maximum cached stylesheets
            insertionPoint: 'head',           // Where to insert style elements
            ...options
        };
        
        // State management
        this.loadedStyles = new Map();        // moduleId -> style element
        this.styleCache = new Map();          // moduleId -> processed CSS
        this.moduleScopes = new Map();        // moduleId -> scope selector
        this.styleDependencies = new Map();   // moduleId -> dependencies
        
        // DOM references
        this.insertionPoint = this.getInsertionPoint();
        this.styleContainer = this.createStyleContainer();
        
        // CSS processing utilities
        this.cssProcessor = new CSSProcessor(this.config);
        
        // Logger
        this.logger = window.GlobalLogger || {
            info: (tag, message) => console.log(`[${tag}] ${message}`),
            error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
            warn: (tag, message) => console.warn(`[${tag}] ${message}`),
            debug: (tag, message) => console.debug(`[${tag}] ${message}`)
        };
        
        // Initialize
        this.initialize();
    }

    /**
     * Initialize the style loader
     * @private
     */
    initialize() {
        // Setup theme integration
        this.setupThemeIntegration();
        
        // Setup hot reload if enabled
        if (this.config.enableHotReload) {
            this.setupHotReload();
        }
        
        // Setup cache cleanup
        this.setupCacheCleanup();
        
        this.logger.info('DynamicStyleLoader', 'Style loader initialized');
    }

    /**
     * Load module styles
     * @param {string} moduleId - Module identifier
     * @param {string} cssContent - CSS content to load
     * @param {Object} options - Loading options
     * @returns {Promise<void>}
     */
    async loadModuleStyles(moduleId, cssContent, options = {}) {
        try {
            this.logger.info('DynamicStyleLoader', `Loading styles for module: ${moduleId}`);
            
            if (!moduleId || !cssContent) {
                throw new Error('Module ID and CSS content are required');
            }
            
            // Check if already loaded
            if (this.isStyleLoaded(moduleId) && !options.force) {
                this.logger.debug('DynamicStyleLoader', `Styles already loaded for ${moduleId}`);
                return;
            }
            
            // Unload existing styles if reloading
            if (this.isStyleLoaded(moduleId)) {
                await this.unloadModuleStyles(moduleId);
            }
            
            // Process CSS content
            const processedCSS = await this.processCSS(moduleId, cssContent, options);
            
            // Create and insert style element
            const styleElement = await this.createStyleElement(moduleId, processedCSS, options);
            
            // Store references
            this.loadedStyles.set(moduleId, styleElement);
            
            // Cache processed CSS
            if (this.config.enableCaching) {
                this.styleCache.set(moduleId, processedCSS);
            }
            
            // Emit events
            this.dispatchEvent(new CustomEvent('stylesLoaded', {
                detail: { moduleId, element: styleElement }
            }));
            
            this.logger.info('DynamicStyleLoader', `Styles loaded successfully for ${moduleId}`);
            
        } catch (error) {
            this.logger.error('DynamicStyleLoader', `Failed to load styles for ${moduleId}:`, error);
            
            this.dispatchEvent(new CustomEvent('stylesLoadError', {
                detail: { moduleId, error: error.message }
            }));
            
            throw error;
        }
    }

    /**
     * Unload module styles
     * @param {string} moduleId - Module identifier
     * @returns {Promise<void>}
     */
    async unloadModuleStyles(moduleId) {
        try {
            this.logger.info('DynamicStyleLoader', `Unloading styles for module: ${moduleId}`);
            
            const styleElement = this.loadedStyles.get(moduleId);
            
            if (styleElement) {
                // Remove from DOM
                if (styleElement.parentNode) {
                    styleElement.parentNode.removeChild(styleElement);
                }
                
                // Clean up references
                this.loadedStyles.delete(moduleId);
                this.moduleScopes.delete(moduleId);
                
                // Emit event
                this.dispatchEvent(new CustomEvent('stylesUnloaded', {
                    detail: { moduleId }
                }));
                
                this.logger.info('DynamicStyleLoader', `Styles unloaded successfully for ${moduleId}`);
            } else {
                this.logger.warn('DynamicStyleLoader', `No styles found for module: ${moduleId}`);
            }
            
        } catch (error) {
            this.logger.error('DynamicStyleLoader', `Failed to unload styles for ${moduleId}:`, error);
            throw error;
        }
    }

    /**
     * Check if module styles are loaded
     * @param {string} moduleId - Module identifier
     * @returns {boolean}
     */
    isStyleLoaded(moduleId) {
        return this.loadedStyles.has(moduleId);
    }

    /**
     * Get loaded module styles
     * @param {string} moduleId - Module identifier
     * @returns {HTMLElement|null}
     */
    getModuleStyleElement(moduleId) {
        return this.loadedStyles.get(moduleId) || null;
    }

    /**
     * Get all loaded modules
     * @returns {Array<string>}
     */
    getLoadedModules() {
        return Array.from(this.loadedStyles.keys());
    }

    /**
     * Reload module styles
     * @param {string} moduleId - Module identifier
     * @param {string} newCssContent - New CSS content
     * @returns {Promise<void>}
     */
    async reloadModuleStyles(moduleId, newCssContent) {
        this.logger.info('DynamicStyleLoader', `Reloading styles for module: ${moduleId}`);
        
        await this.loadModuleStyles(moduleId, newCssContent, { force: true });
        
        this.dispatchEvent(new CustomEvent('stylesReloaded', {
            detail: { moduleId }
        }));
    }

    /**
     * Update theme for all loaded styles
     * @param {string} themeName - Theme name
     * @returns {Promise<void>}
     */
    async updateTheme(themeName) {
        this.logger.info('DynamicStyleLoader', `Updating theme to: ${themeName}`);
        
        // Update theme attribute on document
        document.documentElement.setAttribute('data-theme', themeName);
        
        // Trigger theme update event for modules that need it
        this.dispatchEvent(new CustomEvent('themeUpdated', {
            detail: { themeName }
        }));
    }

    /**
     * Clear all cached styles
     */
    clearCache() {
        this.styleCache.clear();
        this.logger.info('DynamicStyleLoader', 'Style cache cleared');
    }

    /**
     * Get style loading statistics
     * @returns {Object}
     */
    getStats() {
        return {
            loadedModules: this.loadedStyles.size,
            cachedModules: this.styleCache.size,
            totalCacheSize: this.calculateCacheSize(),
            loadedModulesList: Array.from(this.loadedStyles.keys())
        };
    }

    // Private Methods

    /**
     * Process CSS content
     * @private
     */
    async processCSS(moduleId, cssContent, options = {}) {
        let processedCSS = cssContent;
        
        try {
            // Basic CSS validation
            if (this.config.validateCSS) {
                this.validateCSS(processedCSS);
            }
            
            // Process CSS variables and theme integration
            processedCSS = this.processThemeVariables(processedCSS);
            
            // Add module scoping
            if (this.config.scopeStyles) {
                processedCSS = this.addModuleScoping(moduleId, processedCSS);
            }
            
            // Process relative URLs
            processedCSS = this.processRelativeUrls(moduleId, processedCSS);
            
            // Minify CSS if not in development mode
            if (!this.config.enableHotReload) {
                processedCSS = this.minifyCSS(processedCSS);
            }
            
            return processedCSS;
            
        } catch (error) {
            this.logger.error('DynamicStyleLoader', `CSS processing failed for ${moduleId}:`, error);
            throw new Error(`CSS processing failed: ${error.message}`);
        }
    }

    /**
     * Create style element
     * @private
     */
    async createStyleElement(moduleId, processedCSS, options = {}) {
        const styleElement = document.createElement('style');
        
        // Set attributes
        styleElement.setAttribute('data-module', moduleId);
        styleElement.setAttribute('data-dynamic', 'true');
        styleElement.type = 'text/css';
        
        // Add CSS content
        if (styleElement.styleSheet) {
            // IE support
            styleElement.styleSheet.cssText = processedCSS;
        } else {
            styleElement.appendChild(document.createTextNode(processedCSS));
        }
        
        // Insert into DOM
        this.insertStyleElement(styleElement, options);
        
        return styleElement;
    }

    /**
     * Insert style element into DOM
     * @private
     */
    insertStyleElement(styleElement, options = {}) {
        const { insertBefore = null, insertAfter = null } = options;
        
        if (insertBefore) {
            this.styleContainer.insertBefore(styleElement, insertBefore);
        } else if (insertAfter) {
            this.styleContainer.insertBefore(styleElement, insertAfter.nextSibling);
        } else {
            this.styleContainer.appendChild(styleElement);
        }
    }

    /**
     * Validate CSS content
     * @private
     */
    validateCSS(cssContent) {
        // Basic CSS validation
        const dangerousPatterns = [
            /@import\s+url\(/i,           // Prevent external imports
            /javascript:/i,               // Prevent javascript: URLs
            /expression\s*\(/i,           // Prevent IE expressions
            /binding\s*:/i,               // Prevent XBL bindings
            /-moz-binding\s*:/i,          // Prevent Mozilla bindings
        ];
        
        for (const pattern of dangerousPatterns) {
            if (pattern.test(cssContent)) {
                throw new Error(`Potentially dangerous CSS detected: ${pattern}`);
            }
        }
        
        // Check for balanced braces
        const openBraces = (cssContent.match(/\{/g) || []).length;
        const closeBraces = (cssContent.match(/\}/g) || []).length;
        
        if (openBraces !== closeBraces) {
            throw new Error('CSS syntax error: unbalanced braces');
        }
    }

    /**
     * Process theme variables
     * @private
     */
    processThemeVariables(cssContent) {
        // Ensure CSS custom properties are scoped correctly
        // Add theme context if needed
        return cssContent.replace(/var\(--([^)]+)\)/g, (match, varName) => {
            // Ensure theme variables are properly scoped
            return `var(--${varName})`;
        });
    }

    /**
     * Add module scoping
     * @private
     */
    addModuleScoping(moduleId, cssContent) {
        const scopeSelector = `.${moduleId}-module`;
        this.moduleScopes.set(moduleId, scopeSelector);
        
        // Simple CSS scoping - prepend scope selector to rules
        return cssContent.replace(/([^{}]+)\s*\{/g, (match, selector) => {
            // Skip @rules and pseudo-selectors that shouldn't be scoped
            if (selector.trim().startsWith('@') || 
                selector.trim().startsWith(':root') ||
                selector.trim().startsWith('html') ||
                selector.trim().startsWith('body')) {
                return match;
            }
            
            // Add scope to selector
            const trimmedSelector = selector.trim();
            const scopedSelector = `${scopeSelector} ${trimmedSelector}`;
            return `${scopedSelector} {`;
        });
    }

    /**
     * Process relative URLs in CSS
     * @private
     */
    processRelativeUrls(moduleId, cssContent) {
        const basePath = `/modules/${moduleId}/assets/`;
        
        return cssContent.replace(/url\(['"]?([^'"]*?)['"]?\)/g, (match, url) => {
            // Skip external URLs and data URLs
            if (url.startsWith('http') || url.startsWith('//') || url.startsWith('data:')) {
                return match;
            }
            
            // Convert relative URLs to absolute
            const absoluteUrl = url.startsWith('/') ? url : `${basePath}${url}`;
            return `url('${absoluteUrl}')`;
        });
    }

    /**
     * Minify CSS
     * @private
     */
    minifyCSS(cssContent) {
        return cssContent
            // Remove comments
            .replace(/\/\*[\s\S]*?\*\//g, '')
            // Remove extra whitespace
            .replace(/\s+/g, ' ')
            // Remove whitespace around braces and semicolons
            .replace(/\s*{\s*/g, '{')
            .replace(/;\s*/g, ';')
            .replace(/\s*}\s*/g, '}')
            // Remove trailing semicolons
            .replace(/;}/g, '}')
            .trim();
    }

    /**
     * Get insertion point in DOM
     * @private
     */
    getInsertionPoint() {
        switch (this.config.insertionPoint) {
            case 'head':
                return document.head;
            case 'body':
                return document.body;
            default:
                const element = document.querySelector(this.config.insertionPoint);
                return element || document.head;
        }
    }

    /**
     * Create style container
     * @private
     */
    createStyleContainer() {
        // Check if container already exists
        let container = document.querySelector('#coredesk-dynamic-styles');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'coredesk-dynamic-styles';
            container.setAttribute('data-coredesk', 'dynamic-styles');
            
            // Add comment for debugging
            const comment = document.createComment('CoreDesk Dynamic Module Styles');
            this.insertionPoint.appendChild(comment);
            this.insertionPoint.appendChild(container);
        }
        
        return container;
    }

    /**
     * Setup theme integration
     * @private
     */
    setupThemeIntegration() {
        // Listen for theme changes
        const themeManager = window.themeManager;
        
        if (themeManager && typeof themeManager.on === 'function') {
            themeManager.on('themeChanged', (themeName) => {
                this.updateTheme(themeName);
            });
        }
        
        // Listen for CSS custom property changes
        if (window.CSS && CSS.supports && CSS.supports('(--test: 0)')) {
            // CSS custom properties are supported
            this.logger.debug('DynamicStyleLoader', 'CSS custom properties supported');
        }
    }

    /**
     * Setup hot reload
     * @private
     */
    setupHotReload() {
        // This would integrate with a development server
        // For now, just set up event listeners
        this.addEventListener('hotReload', (event) => {
            const { moduleId, cssContent } = event.detail;
            this.reloadModuleStyles(moduleId, cssContent);
        });
        
        this.logger.debug('DynamicStyleLoader', 'Hot reload enabled');
    }

    /**
     * Setup cache cleanup
     * @private
     */
    setupCacheCleanup() {
        if (!this.config.enableCaching) return;
        
        // Clean cache when it gets too large
        setInterval(() => {
            if (this.styleCache.size > this.config.maxCacheSize) {
                this.cleanupCache();
            }
        }, 10 * 60 * 1000); // Every 10 minutes
    }

    /**
     * Cleanup old cache entries
     * @private
     */
    cleanupCache() {
        const entries = Array.from(this.styleCache.entries());
        
        // Remove oldest entries (simple LRU)
        const toRemove = entries.length - this.config.maxCacheSize;
        
        if (toRemove > 0) {
            const keysToRemove = entries.slice(0, toRemove).map(([key]) => key);
            
            for (const key of keysToRemove) {
                this.styleCache.delete(key);
            }
            
            this.logger.debug('DynamicStyleLoader', `Cleaned up ${toRemove} cache entries`);
        }
    }

    /**
     * Calculate total cache size
     * @private
     */
    calculateCacheSize() {
        let totalSize = 0;
        
        for (const css of this.styleCache.values()) {
            totalSize += css.length;
        }
        
        return totalSize;
    }
}

/**
 * CSS Processor helper class
 */
class CSSProcessor {
    constructor(config) {
        this.config = config;
    }

    /**
     * Parse CSS and extract rules
     * @param {string} cssContent - CSS content
     * @returns {Object} Parsed CSS rules
     */
    parseCSS(cssContent) {
        // Simple CSS parser - could be enhanced with a proper CSS parser library
        const rules = [];
        const ruleRegex = /([^{}]+)\s*\{([^{}]*)\}/g;
        let match;
        
        while ((match = ruleRegex.exec(cssContent)) !== null) {
            rules.push({
                selector: match[1].trim(),
                declarations: match[2].trim()
            });
        }
        
        return { rules };
    }

    /**
     * Detect style conflicts
     * @param {Array} existingRules - Existing CSS rules
     * @param {Array} newRules - New CSS rules
     * @returns {Array} Conflicts found
     */
    detectConflicts(existingRules, newRules) {
        const conflicts = [];
        
        for (const newRule of newRules) {
            for (const existingRule of existingRules) {
                if (newRule.selector === existingRule.selector) {
                    conflicts.push({
                        selector: newRule.selector,
                        existing: existingRule.declarations,
                        new: newRule.declarations
                    });
                }
            }
        }
        
        return conflicts;
    }
}

// Make available globally
window.DynamicStyleLoader = DynamicStyleLoader;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicStyleLoader;
}

console.log('DynamicStyleLoader', '[DynamicStyleLoader] Service loaded successfully');