/**
 * JsonPersistenceService.js
 * Robust file-based persistence system to replace unreliable SQLite
 * Stores all module and application data in JSON files
 */

const fs = require('fs').promises;
const path = require('path');
const os = require('os');

// Try to get electron app, fallback to mock if not available
let app;
try {
    app = require('electron').app;
} catch (error) {
    // Fallback for testing environments
    app = {
        getPath: (name) => {
            if (name === 'userData') {
                return path.join(os.homedir(), '.config', 'coredesk');
            }
            return '';
        }
    };
}

class JsonPersistenceService {
    constructor(logger) {
        this.logger = logger;
        this.dataPath = null;
        this.isInitialized = false;
        this.cache = new Map();
    }

    async initialize() {
        try {
            this.logger.info('JsonPersistenceService', 'Initializing JSON persistence system...');
            
            // Create data directory in userData
            this.dataPath = path.join(app.getPath('userData'), 'coredesk-data');
            await this.ensureDirectory(this.dataPath);
            
            // Create subdirectories for different data types
            await this.ensureDirectory(path.join(this.dataPath, 'modules'));
            await this.ensureDirectory(path.join(this.dataPath, 'user-data'));
            await this.ensureDirectory(path.join(this.dataPath, 'application'));
            
            // Load existing data into cache
            await this.loadCache();
            
            this.isInitialized = true;
            this.logger.info('JsonPersistenceService', 'JSON persistence system initialized successfully');
            this.logger.info('JsonPersistenceService', `Data path: ${this.dataPath}`);
            
            return true;
        } catch (error) {
            this.logger.error('JsonPersistenceService', 'Failed to initialize JSON persistence:', error);
            return false;
        }
    }

    async ensureDirectory(dirPath) {
        try {
            await fs.access(dirPath);
        } catch {
            await fs.mkdir(dirPath, { recursive: true });
            this.logger.debug('JsonPersistenceService', `Created directory: ${dirPath}`);
        }
    }

    async loadCache() {
        try {
            // Load module registry
            const modulesPath = path.join(this.dataPath, 'modules', 'registry.json');
            try {
                const moduleData = await fs.readFile(modulesPath, 'utf8');
                const modules = JSON.parse(moduleData);
                this.cache.set('modules:registry', modules);
                this.logger.info('JsonPersistenceService', `Loaded ${Object.keys(modules).length} modules from cache`);
            } catch {
                this.cache.set('modules:registry', {});
                this.logger.info('JsonPersistenceService', 'No existing module registry found, created new one');
            }
        } catch (error) {
            this.logger.error('JsonPersistenceService', 'Failed to load cache:', error);
        }
    }

    // ===== MODULE PERSISTENCE =====
    
    async registerModule(moduleData) {
        try {
            if (!this.isInitialized) {
                throw new Error('JsonPersistenceService not initialized');
            }

            const { moduleId, name, version, installPath, manifestData, status = 'active' } = moduleData;
            
            if (!moduleId || !name || !version) {
                throw new Error('Missing required module data');
            }

            this.logger.info('JsonPersistenceService', `Registering module: ${moduleId}@${version}`);

            // Get current registry
            const registry = this.cache.get('modules:registry') || {};
            
            // Add/update module
            registry[moduleId] = {
                moduleId,
                name,
                version,
                status,
                installPath,
                manifestData,
                installedAt: registry[moduleId]?.installedAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Update cache
            this.cache.set('modules:registry', registry);

            // Save to file
            const registryPath = path.join(this.dataPath, 'modules', 'registry.json');
            await fs.writeFile(registryPath, JSON.stringify(registry, null, 2), 'utf8');

            // Create individual module data file
            const moduleDataPath = path.join(this.dataPath, 'modules', `${moduleId}.json`);
            await fs.writeFile(moduleDataPath, JSON.stringify(registry[moduleId], null, 2), 'utf8');

            this.logger.info('JsonPersistenceService', `Module ${moduleId} registered successfully`);
            return true;

        } catch (error) {
            this.logger.error('JsonPersistenceService', `Failed to register module ${moduleData?.moduleId}:`, error);
            return false;
        }
    }

    async getInstalledModules() {
        try {
            if (!this.isInitialized) {
                throw new Error('JsonPersistenceService not initialized');
            }

            const registry = this.cache.get('modules:registry') || {};
            return Object.values(registry).filter(module => module.status === 'active');
        } catch (error) {
            this.logger.error('JsonPersistenceService', 'Failed to get installed modules:', error);
            return [];
        }
    }

    async getModule(moduleId) {
        try {
            if (!this.isInitialized) {
                throw new Error('JsonPersistenceService not initialized');
            }

            const registry = this.cache.get('modules:registry') || {};
            return registry[moduleId] || null;
        } catch (error) {
            this.logger.error('JsonPersistenceService', `Failed to get module ${moduleId}:`, error);
            return null;
        }
    }

    async unregisterModule(moduleId) {
        try {
            if (!this.isInitialized) {
                throw new Error('JsonPersistenceService not initialized');
            }

            this.logger.info('JsonPersistenceService', `Unregistering module: ${moduleId}`);

            // Get current registry
            const registry = this.cache.get('modules:registry') || {};
            
            if (registry[moduleId]) {
                delete registry[moduleId];
                
                // Update cache
                this.cache.set('modules:registry', registry);

                // Save to file
                const registryPath = path.join(this.dataPath, 'modules', 'registry.json');
                await fs.writeFile(registryPath, JSON.stringify(registry, null, 2), 'utf8');

                // Remove individual module data file
                const moduleDataPath = path.join(this.dataPath, 'modules', `${moduleId}.json`);
                try {
                    await fs.unlink(moduleDataPath);
                } catch {
                    // File might not exist, which is fine
                }

                this.logger.info('JsonPersistenceService', `Module ${moduleId} unregistered successfully`);
                return true;
            } else {
                this.logger.warn('JsonPersistenceService', `Module ${moduleId} not found in registry`);
                return false;
            }

        } catch (error) {
            this.logger.error('JsonPersistenceService', `Failed to unregister module ${moduleId}:`, error);
            return false;
        }
    }

    // ===== MODULE DATA STORAGE =====
    
    async saveModuleData(moduleId, dataKey, data) {
        try {
            if (!this.isInitialized) {
                throw new Error('JsonPersistenceService not initialized');
            }

            const moduleDataDir = path.join(this.dataPath, 'modules', moduleId);
            await this.ensureDirectory(moduleDataDir);

            const dataPath = path.join(moduleDataDir, `${dataKey}.json`);
            const dataWithMeta = {
                data,
                moduleId,
                dataKey,
                savedAt: new Date().toISOString()
            };

            await fs.writeFile(dataPath, JSON.stringify(dataWithMeta, null, 2), 'utf8');
            
            this.logger.debug('JsonPersistenceService', `Saved data for module ${moduleId}, key: ${dataKey}`);
            return true;

        } catch (error) {
            this.logger.error('JsonPersistenceService', `Failed to save module data for ${moduleId}:`, error);
            return false;
        }
    }

    async loadModuleData(moduleId, dataKey) {
        try {
            if (!this.isInitialized) {
                throw new Error('JsonPersistenceService not initialized');
            }

            const dataPath = path.join(this.dataPath, 'modules', moduleId, `${dataKey}.json`);
            const fileContent = await fs.readFile(dataPath, 'utf8');
            const dataWithMeta = JSON.parse(fileContent);

            this.logger.debug('JsonPersistenceService', `Loaded data for module ${moduleId}, key: ${dataKey}`);
            return dataWithMeta.data;

        } catch (error) {
            this.logger.debug('JsonPersistenceService', `No data found for module ${moduleId}, key: ${dataKey}`);
            return null;
        }
    }

    async deleteModuleData(moduleId, dataKey) {
        try {
            if (!this.isInitialized) {
                throw new Error('JsonPersistenceService not initialized');
            }

            const dataPath = path.join(this.dataPath, 'modules', moduleId, `${dataKey}.json`);
            await fs.unlink(dataPath);
            
            this.logger.debug('JsonPersistenceService', `Deleted data for module ${moduleId}, key: ${dataKey}`);
            return true;

        } catch (error) {
            this.logger.debug('JsonPersistenceService', `Failed to delete data for module ${moduleId}, key: ${dataKey}:`, error);
            return false;
        }
    }

    // ===== GENERIC DATA STORAGE =====
    
    async saveData(category, key, data) {
        try {
            if (!this.isInitialized) {
                throw new Error('JsonPersistenceService not initialized');
            }

            const categoryDir = path.join(this.dataPath, category);
            await this.ensureDirectory(categoryDir);

            const dataPath = path.join(categoryDir, `${key}.json`);
            const dataWithMeta = {
                data,
                category,
                key,
                savedAt: new Date().toISOString()
            };

            await fs.writeFile(dataPath, JSON.stringify(dataWithMeta, null, 2), 'utf8');
            
            this.logger.debug('JsonPersistenceService', `Saved data: ${category}/${key}`);
            return true;

        } catch (error) {
            this.logger.error('JsonPersistenceService', `Failed to save data ${category}/${key}:`, error);
            return false;
        }
    }

    async loadData(category, key) {
        try {
            if (!this.isInitialized) {
                throw new Error('JsonPersistenceService not initialized');
            }

            const dataPath = path.join(this.dataPath, category, `${key}.json`);
            const fileContent = await fs.readFile(dataPath, 'utf8');
            const dataWithMeta = JSON.parse(fileContent);

            this.logger.debug('JsonPersistenceService', `Loaded data: ${category}/${key}`);
            return dataWithMeta.data;

        } catch (error) {
            this.logger.debug('JsonPersistenceService', `No data found: ${category}/${key}`);
            return null;
        }
    }

    // ===== STATISTICS AND MANAGEMENT =====
    
    async getStats() {
        try {
            if (!this.isInitialized) {
                return null;
            }

            const registry = this.cache.get('modules:registry') || {};
            const moduleCount = Object.keys(registry).length;
            const activeModules = Object.values(registry).filter(m => m.status === 'active').length;

            return {
                dataPath: this.dataPath,
                isInitialized: this.isInitialized,
                moduleCount,
                activeModules,
                cacheSize: this.cache.size
            };

        } catch (error) {
            this.logger.error('JsonPersistenceService', 'Failed to get stats:', error);
            return null;
        }
    }
}

module.exports = JsonPersistenceService;