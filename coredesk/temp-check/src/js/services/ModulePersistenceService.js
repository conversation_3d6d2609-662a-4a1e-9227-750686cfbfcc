/**
 * ModulePersistenceService
 * Manages module persistence in SQLite database for CoreDesk Framework v2.0.0
 * 
 * Features:
 * - Register/unregister installed modules in database
 * - Track module status (active/inactive)
 * - Validate module integrity
 * - Provide centralized module state management
 * - Support for module activation/deactivation
 */

class ModulePersistenceService {
    constructor(databaseService, logger) {
        this.db = databaseService;
        this.logger = logger || console;
        this.isInitialized = false;
        
        // Cache for frequently accessed data
        this.moduleCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
        this.lastCacheUpdate = 0;
    }

    /**
     * Initialize the service
     * @returns {Promise<boolean>}
     */
    async initialize() {
        try {
            if (!this.db || !this.db.isInitialized) {
                throw new Error('Database service not available or not initialized');
            }

            this.logger.info('ModulePersistenceService', 'Initializing module persistence service...');
            
            // Verify table exists
            await this.verifyTableExists();
            
            // Load initial cache
            await this.refreshCache();
            
            this.isInitialized = true;
            this.logger.info('ModulePersistenceService', 'Module persistence service initialized successfully');
            
            return true;
        } catch (error) {
            this.logger.error('ModulePersistenceService', 'Failed to initialize:', error);
            return false;
        }
    }

    /**
     * Register a module as installed
     * @param {Object} moduleData - Module data to register
     * @returns {Promise<boolean>}
     */
    async registerInstalledModule(moduleData) {
        try {
            if (!this.isInitialized) {
                throw new Error('Service not initialized');
            }

            const { moduleId, name, version, installPath, manifestData, status = 'active' } = moduleData;
            
            if (!moduleId || !name || !version || !installPath) {
                throw new Error('Missing required module data');
            }

            this.logger.info('ModulePersistenceService', `Registering module: ${moduleId}@${version}`);

            // Check if module already exists
            const existingModule = await this.getInstalledModule(moduleId);
            
            if (existingModule) {
                // Update existing module
                const sql = `
                    UPDATE installed_modules 
                    SET name = ?, version = ?, status = ?, install_path = ?, 
                        manifest_data = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE module_id = ?
                `;
                
                await this.db.executeQuery(sql, [
                    name, version, status, installPath, 
                    JSON.stringify(manifestData), moduleId
                ]);
                
                this.logger.info('ModulePersistenceService', `Updated existing module: ${moduleId}`);
            } else {
                // Insert new module
                const sql = `
                    INSERT INTO installed_modules 
                    (module_id, name, version, status, install_path, manifest_data)
                    VALUES (?, ?, ?, ?, ?, ?)
                `;
                
                await this.db.executeQuery(sql, [
                    moduleId, name, version, status, installPath, 
                    JSON.stringify(manifestData)
                ]);
                
                this.logger.info('ModulePersistenceService', `Registered new module: ${moduleId}`);
            }

            // Refresh cache
            await this.refreshCache();
            
            return true;
        } catch (error) {
            this.logger.error('ModulePersistenceService', `Failed to register module ${moduleData?.moduleId}:`, error);
            return false;
        }
    }

    /**
     * Unregister a module
     * @param {string} moduleId - Module ID to unregister
     * @returns {Promise<boolean>}
     */
    async unregisterModule(moduleId) {
        try {
            if (!this.isInitialized) {
                throw new Error('Service not initialized');
            }

            this.logger.info('ModulePersistenceService', `Unregistering module: ${moduleId}`);

            const sql = 'DELETE FROM installed_modules WHERE module_id = ?';
            const result = await this.db.executeQuery(sql, [moduleId]);
            
            if (result.changes > 0) {
                this.logger.info('ModulePersistenceService', `Successfully unregistered module: ${moduleId}`);
                
                // Refresh cache
                await this.refreshCache();
                
                return true;
            } else {
                this.logger.warn('ModulePersistenceService', `Module not found in database: ${moduleId}`);
                return false;
            }
        } catch (error) {
            this.logger.error('ModulePersistenceService', `Failed to unregister module ${moduleId}:`, error);
            return false;
        }
    }

    /**
     * Get all installed modules
     * @returns {Promise<Array>}
     */
    async getInstalledModules() {
        try {
            if (!this.isInitialized) {
                throw new Error('Service not initialized');
            }

            // Use cache if available and fresh
            if (this.isCacheValid()) {
                return Array.from(this.moduleCache.values());
            }

            const sql = 'SELECT * FROM installed_modules ORDER BY installed_at DESC';
            const modules = await this.db.executeSelect(sql);
            
            // Parse manifest data
            const parsedModules = modules.map(module => ({
                ...module,
                manifest_data: module.manifest_data ? JSON.parse(module.manifest_data) : null
            }));

            // Update cache
            this.updateCache(parsedModules);
            
            return parsedModules;
        } catch (error) {
            this.logger.error('ModulePersistenceService', 'Failed to get installed modules:', error);
            return [];
        }
    }

    /**
     * Get a specific installed module
     * @param {string} moduleId - Module ID
     * @returns {Promise<Object|null>}
     */
    async getInstalledModule(moduleId) {
        try {
            if (!this.isInitialized) {
                throw new Error('Service not initialized');
            }

            // Check cache first
            if (this.isCacheValid() && this.moduleCache.has(moduleId)) {
                return this.moduleCache.get(moduleId);
            }

            const sql = 'SELECT * FROM installed_modules WHERE module_id = ?';
            const module = await this.db.executeSelectOne(sql, [moduleId]);
            
            if (module) {
                // Parse manifest data
                const parsedModule = {
                    ...module,
                    manifest_data: module.manifest_data ? JSON.parse(module.manifest_data) : null
                };
                
                // Update cache entry
                this.moduleCache.set(moduleId, parsedModule);
                
                return parsedModule;
            }
            
            return null;
        } catch (error) {
            this.logger.error('ModulePersistenceService', `Failed to get module ${moduleId}:`, error);
            return null;
        }
    }

    /**
     * Check if a module is installed
     * @param {string} moduleId - Module ID
     * @returns {Promise<boolean>}
     */
    async isModuleInstalled(moduleId) {
        try {
            const module = await this.getInstalledModule(moduleId);
            return module !== null;
        } catch (error) {
            this.logger.error('ModulePersistenceService', `Failed to check if module ${moduleId} is installed:`, error);
            return false;
        }
    }

    /**
     * Update module status
     * @param {string} moduleId - Module ID
     * @param {string} status - New status ('active' or 'inactive')
     * @returns {Promise<boolean>}
     */
    async updateModuleStatus(moduleId, status) {
        try {
            if (!this.isInitialized) {
                throw new Error('Service not initialized');
            }

            if (!['active', 'inactive'].includes(status)) {
                throw new Error('Invalid status. Must be "active" or "inactive"');
            }

            this.logger.info('ModulePersistenceService', `Updating module ${moduleId} status to: ${status}`);

            const sql = `
                UPDATE installed_modules 
                SET status = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE module_id = ?
            `;
            
            const result = await this.db.executeQuery(sql, [status, moduleId]);
            
            if (result.changes > 0) {
                this.logger.info('ModulePersistenceService', `Successfully updated module ${moduleId} status`);
                
                // Refresh cache
                await this.refreshCache();
                
                return true;
            } else {
                this.logger.warn('ModulePersistenceService', `Module not found: ${moduleId}`);
                return false;
            }
        } catch (error) {
            this.logger.error('ModulePersistenceService', `Failed to update module ${moduleId} status:`, error);
            return false;
        }
    }

    /**
     * Get active modules only
     * @returns {Promise<Array>}
     */
    async getActiveModules() {
        try {
            const allModules = await this.getInstalledModules();
            return allModules.filter(module => module.status === 'active');
        } catch (error) {
            this.logger.error('ModulePersistenceService', 'Failed to get active modules:', error);
            return [];
        }
    }

    /**
     * Verify table exists
     * @private
     */
    async verifyTableExists() {
        try {
            const sql = `
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='installed_modules'
            `;
            
            const result = await this.db.executeSelectOne(sql);
            
            if (!result) {
                throw new Error('installed_modules table does not exist');
            }
            
            this.logger.debug('ModulePersistenceService', 'installed_modules table verified');
        } catch (error) {
            this.logger.error('ModulePersistenceService', 'Table verification failed:', error);
            throw error;
        }
    }

    /**
     * Refresh cache
     * @private
     */
    async refreshCache() {
        try {
            const sql = 'SELECT * FROM installed_modules';
            const modules = await this.db.executeSelect(sql);
            
            // Parse and update cache
            this.moduleCache.clear();
            modules.forEach(module => {
                const parsedModule = {
                    ...module,
                    manifest_data: module.manifest_data ? JSON.parse(module.manifest_data) : null
                };
                this.moduleCache.set(module.module_id, parsedModule);
            });
            
            this.lastCacheUpdate = Date.now();
            this.logger.debug('ModulePersistenceService', `Cache refreshed with ${modules.length} modules`);
        } catch (error) {
            this.logger.error('ModulePersistenceService', 'Failed to refresh cache:', error);
        }
    }

    /**
     * Update cache with modules array
     * @private
     */
    updateCache(modules) {
        this.moduleCache.clear();
        modules.forEach(module => {
            this.moduleCache.set(module.module_id, module);
        });
        this.lastCacheUpdate = Date.now();
    }

    /**
     * Check if cache is valid
     * @private
     */
    isCacheValid() {
        return (Date.now() - this.lastCacheUpdate) < this.cacheExpiry;
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.moduleCache.clear();
        this.lastCacheUpdate = 0;
        this.logger.debug('ModulePersistenceService', 'Cache cleared');
    }
}

// Export for Node.js and browser environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModulePersistenceService;
} else if (typeof window !== 'undefined') {
    window.ModulePersistenceService = ModulePersistenceService;
}
