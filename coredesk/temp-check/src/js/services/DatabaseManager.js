/**
 * Database Manager for CoreDesk Framework
 * Handles SQLite database operations with the complete schema from PRD
 * Location: app.getPath('userData')/coredesk.db
 */

class DatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = null;
        this.isInitialized = false;
        this.migrations = [];
        
        // Database configuration
        this.config = {
            filename: 'coredesk.db',
            mode: 'WAL', // Write-Ahead Logging for better performance
            timeout: 10000,
            verbose: process?.argv?.includes('--dev') ? console.log : null
        };
    }

    /**
     * Initialize the database
     * @returns {Promise<boolean>} Success status
     */
    async initialize() {
        try {
            console.log('DatabaseManager', '[DatabaseManager] Initializing SQLite database...', );
            
            // Get database path
            this.dbPath = await this.getDatabasePath();
            
            // Initialize SQLite connection
            await this.createConnection();
            
            // Configure database
            await this.configureDatabase();
            
            // Run migrations/create schema
            await this.createSchema();
            
            // Verify database integrity
            await this.verifyIntegrity();
            
            this.isInitialized = true;
            
            console.log('DatabaseManager', '[DatabaseManager] Database initialized successfully', );
            console.log('DatabaseManager', '[DatabaseManager] Database path:', this.dbPath);
            
            return true;
            
        } catch (error) {
            console.error('DatabaseManager', '[DatabaseManager] Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Get database file path
     * @returns {Promise<string>} Database file path
     */
    async getDatabasePath() {
        const userDataPath = await window.electronAPI.app.getUserDataPath();
        const path = await window.nodeAPI.path.join(userDataPath, this.config.filename);
        return path;
    }

    /**
     * Create SQLite connection
     * @returns {Promise<void>}
     */
    async createConnection() {
        return new Promise((resolve, reject) => {
            // Since we can't require sqlite3 directly in renderer, we'll use IPC
            // The actual SQLite connection will be handled in the main process
            
            // For now, we'll create a mock implementation that works through IPC
            this.db = {
                run: async (sql, params = []) => {
                    return await window.electronAPI.database.query(sql, params);
                },
                get: async (sql, params = []) => {
                    const result = await window.electronAPI.database.query(sql, params);
                    return result.data && result.data.length > 0 ? result.data[0] : null;
                },
                all: async (sql, params = []) => {
                    const result = await window.electronAPI.database.query(sql, params);
                    return result.data || [];
                },
                exec: async (sql) => {
                    return await window.electronAPI.database.query(sql, []);
                }
            };
            
            resolve();
        });
    }

    /**
     * Configure database settings
     * @returns {Promise<void>}
     */
    async configureDatabase() {
        try {
            // Enable WAL mode for better concurrency
            await this.db.run('PRAGMA journal_mode=WAL');
            
            // Enable foreign key constraints
            await this.db.run('PRAGMA foreign_keys=ON');
            
            // Set synchronous mode to NORMAL for better performance
            await this.db.run('PRAGMA synchronous=NORMAL');
            
            // Set page size for optimal performance
            await this.db.run('PRAGMA page_size=4096');
            
            // Set cache size (negative value means KB)
            await this.db.run('PRAGMA cache_size=-64000'); // 64MB cache
            
            console.log('DatabaseManager', '[DatabaseManager] Database configured successfully', );
            
        } catch (error) {
            console.error('DatabaseManager', '[DatabaseManager] Configuration failed:', error);
            throw error;
        }
    }

    /**
     * Create complete database schema from PRD
     * @returns {Promise<void>}
     */
    async createSchema() {
        try {
            console.log('DatabaseManager', '[DatabaseManager] Creating database schema...', );
            
            // Create tables in order (respecting foreign key dependencies)
            await this.createSystemTables();
            await this.createLexFlowTables();
            await this.createProtocolXTables();
            await this.createIndexes();
            await this.createTriggers();
            
            console.log('DatabaseManager', '[DatabaseManager] Schema created successfully', );
            
        } catch (error) {
            console.error('DatabaseManager', '[DatabaseManager] Schema creation failed:', error);
            throw error;
        }
    }

    /**
     * Create system tables (global)
     * @returns {Promise<void>}
     */
    async createSystemTables() {
        const tables = [
            // Users table
            `CREATE TABLE IF NOT EXISTS coredesk_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                company TEXT,
                country TEXT DEFAULT 'US',
                timezone TEXT DEFAULT 'UTC',
                language TEXT DEFAULT 'en',
                theme TEXT DEFAULT 'dark',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                sync_hash TEXT,
                sync_status TEXT DEFAULT 'pending'
            )`,
            
            // Licenses table
            `CREATE TABLE IF NOT EXISTS coredesk_licenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_key TEXT UNIQUE NOT NULL,
                type TEXT NOT NULL CHECK(type IN ('trial', 'premium', 'enterprise')),
                status TEXT NOT NULL CHECK(status IN ('active', 'inactive', 'expired', 'suspended')),
                device_fingerprint TEXT NOT NULL,
                cloud_access BOOLEAN DEFAULT 0,
                storage_quota_mb INTEGER DEFAULT 0,
                storage_used_mb INTEGER DEFAULT 0,
                valid_from DATETIME NOT NULL,
                valid_until DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                sync_hash TEXT,
                sync_status TEXT DEFAULT 'pending'
            )`,
            
            // Settings table
            `CREATE TABLE IF NOT EXISTS coredesk_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                key TEXT NOT NULL,
                value TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES coredesk_users(id),
                UNIQUE(user_id, key)
            )`,
            
            // Sync log table
            `CREATE TABLE IF NOT EXISTS coredesk_sync_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                record_id INTEGER NOT NULL,
                action TEXT NOT NULL CHECK(action IN ('create', 'update', 'delete')),
                sync_direction TEXT CHECK(sync_direction IN ('push', 'pull')),
                sync_status TEXT DEFAULT 'pending',
                sync_attempts INTEGER DEFAULT 0,
                last_error TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                synced_at DATETIME
            )`
        ];

        for (const sql of tables) {
            await this.db.run(sql);
        }
        
        console.log('DatabaseManager', '[DatabaseManager] System tables created', );
    }

    /**
     * Create LexFlow module tables
     * @returns {Promise<void>}
     */
    async createLexFlowTables() {
        const tables = [
            // Legal cases table
            `CREATE TABLE IF NOT EXISTS lexflow_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_number TEXT UNIQUE NOT NULL,
                title TEXT NOT NULL,
                client_name TEXT NOT NULL,
                client_email TEXT,
                client_phone TEXT,
                case_type TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'active',
                priority TEXT DEFAULT 'normal' CHECK(priority IN ('low', 'normal', 'high', 'urgent')),
                assigned_to INTEGER,
                description TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                closed_at DATETIME,
                sync_hash TEXT,
                sync_status TEXT DEFAULT 'pending',
                FOREIGN KEY (assigned_to) REFERENCES coredesk_users(id)
            )`,
            
            // Case documents table
            `CREATE TABLE IF NOT EXISTS lexflow_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_id INTEGER NOT NULL,
                filename TEXT NOT NULL,
                file_path TEXT,
                file_size INTEGER,
                file_type TEXT,
                cloud_url TEXT,
                cloud_storage_path TEXT,
                upload_status TEXT DEFAULT 'pending',
                description TEXT,
                uploaded_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                sync_hash TEXT,
                sync_status TEXT DEFAULT 'pending',
                FOREIGN KEY (case_id) REFERENCES lexflow_cases(id) ON DELETE CASCADE,
                FOREIGN KEY (uploaded_by) REFERENCES coredesk_users(id)
            )`,
            
            // Events and deadlines table
            `CREATE TABLE IF NOT EXISTS lexflow_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_id INTEGER NOT NULL,
                event_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                event_date DATETIME NOT NULL,
                location TEXT,
                reminder_minutes INTEGER DEFAULT 60,
                status TEXT DEFAULT 'scheduled',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                sync_hash TEXT,
                sync_status TEXT DEFAULT 'pending',
                FOREIGN KEY (case_id) REFERENCES lexflow_cases(id) ON DELETE CASCADE
            )`,
            
            // Notes and comments table
            `CREATE TABLE IF NOT EXISTS lexflow_notes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_id INTEGER NOT NULL,
                author_id INTEGER NOT NULL,
                content TEXT NOT NULL,
                is_private BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                sync_hash TEXT,
                sync_status TEXT DEFAULT 'pending',
                FOREIGN KEY (case_id) REFERENCES lexflow_cases(id) ON DELETE CASCADE,
                FOREIGN KEY (author_id) REFERENCES coredesk_users(id)
            )`
        ];

        for (const sql of tables) {
            await this.db.run(sql);
        }
        
        console.log('DatabaseManager', '[DatabaseManager] LexFlow tables created', );
    }

    /**
     * Create ProtocolX module tables
     * @returns {Promise<void>}
     */
    async createProtocolXTables() {
        const tables = [
            // Digital protocols table
            `CREATE TABLE IF NOT EXISTS protocolx_protocols (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                protocol_number TEXT UNIQUE NOT NULL,
                protocol_type TEXT NOT NULL,
                title TEXT NOT NULL,
                parties TEXT NOT NULL, -- JSON array
                status TEXT DEFAULT 'draft',
                notary_id INTEGER,
                location TEXT,
                protocol_date DATETIME,
                expiry_date DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                finalized_at DATETIME,
                sync_hash TEXT,
                sync_status TEXT DEFAULT 'pending',
                FOREIGN KEY (notary_id) REFERENCES coredesk_users(id)
            )`,
            
            // Protocol documents table
            `CREATE TABLE IF NOT EXISTS protocolx_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                protocol_id INTEGER NOT NULL,
                document_type TEXT NOT NULL,
                filename TEXT NOT NULL,
                file_path TEXT,
                file_size INTEGER,
                cloud_url TEXT,
                cloud_storage_path TEXT,
                hash_sha256 TEXT,
                is_signed BOOLEAN DEFAULT 0,
                signed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                sync_hash TEXT,
                sync_status TEXT DEFAULT 'pending',
                FOREIGN KEY (protocol_id) REFERENCES protocolx_protocols(id) ON DELETE CASCADE
            )`,
            
            // Digital signatures table
            `CREATE TABLE IF NOT EXISTS protocolx_signatures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                protocol_id INTEGER NOT NULL,
                document_id INTEGER NOT NULL,
                signer_name TEXT NOT NULL,
                signer_email TEXT NOT NULL,
                signer_id_number TEXT,
                signature_data TEXT,
                signature_type TEXT DEFAULT 'electronic',
                ip_address TEXT,
                signed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                sync_hash TEXT,
                sync_status TEXT DEFAULT 'pending',
                FOREIGN KEY (protocol_id) REFERENCES protocolx_protocols(id) ON DELETE CASCADE,
                FOREIGN KEY (document_id) REFERENCES protocolx_documents(id) ON DELETE CASCADE
            )`
        ];

        for (const sql of tables) {
            await this.db.run(sql);
        }
        
        console.log('DatabaseManager', '[DatabaseManager] ProtocolX tables created', );
    }

    /**
     * Create database indexes for optimization
     * @returns {Promise<void>}
     */
    async createIndexes() {
        const indexes = [
            // System table indexes
            'CREATE INDEX IF NOT EXISTS idx_users_email ON coredesk_users(email)',
            'CREATE INDEX IF NOT EXISTS idx_licenses_key ON coredesk_licenses(license_key)',
            'CREATE INDEX IF NOT EXISTS idx_licenses_device ON coredesk_licenses(device_fingerprint)',
            'CREATE INDEX IF NOT EXISTS idx_sync_log_status ON coredesk_sync_log(sync_status, created_at)',
            
            // LexFlow indexes
            'CREATE INDEX IF NOT EXISTS idx_cases_number ON lexflow_cases(case_number)',
            'CREATE INDEX IF NOT EXISTS idx_cases_status ON lexflow_cases(status)',
            'CREATE INDEX IF NOT EXISTS idx_cases_client ON lexflow_cases(client_name)',
            'CREATE INDEX IF NOT EXISTS idx_documents_case ON lexflow_documents(case_id)',
            'CREATE INDEX IF NOT EXISTS idx_events_case ON lexflow_events(case_id)',
            'CREATE INDEX IF NOT EXISTS idx_events_date ON lexflow_events(event_date)',
            'CREATE INDEX IF NOT EXISTS idx_notes_case ON lexflow_notes(case_id)',
            
            // ProtocolX indexes
            'CREATE INDEX IF NOT EXISTS idx_protocols_number ON protocolx_protocols(protocol_number)',
            'CREATE INDEX IF NOT EXISTS idx_protocols_status ON protocolx_protocols(status)',
            'CREATE INDEX IF NOT EXISTS idx_protocol_docs ON protocolx_documents(protocol_id)',
            'CREATE INDEX IF NOT EXISTS idx_signatures_protocol ON protocolx_signatures(protocol_id)'
        ];

        for (const sql of indexes) {
            await this.db.run(sql);
        }
        
        console.log('DatabaseManager', '[DatabaseManager] Indexes created', );
    }

    /**
     * Create triggers for automatic sync_hash updates
     * @returns {Promise<void>}
     */
    async createTriggers() {
        const triggers = [
            // Trigger for users table
            `CREATE TRIGGER IF NOT EXISTS update_sync_hash_users
            AFTER UPDATE ON coredesk_users
            BEGIN
                UPDATE coredesk_users 
                SET sync_hash = hex(randomblob(16)),
                    updated_at = CURRENT_TIMESTAMP,
                    sync_status = 'pending'
                WHERE id = NEW.id;
            END`,
            
            // Trigger for licenses table
            `CREATE TRIGGER IF NOT EXISTS update_sync_hash_licenses
            AFTER UPDATE ON coredesk_licenses
            BEGIN
                UPDATE coredesk_licenses 
                SET sync_hash = hex(randomblob(16)),
                    updated_at = CURRENT_TIMESTAMP,
                    sync_status = 'pending'
                WHERE id = NEW.id;
            END`,
            
            // Trigger for cases table
            `CREATE TRIGGER IF NOT EXISTS update_sync_hash_cases
            AFTER UPDATE ON lexflow_cases
            BEGIN
                UPDATE lexflow_cases 
                SET sync_hash = hex(randomblob(16)),
                    updated_at = CURRENT_TIMESTAMP,
                    sync_status = 'pending'
                WHERE id = NEW.id;
            END`,
            
            // Trigger for protocols table
            `CREATE TRIGGER IF NOT EXISTS update_sync_hash_protocols
            AFTER UPDATE ON protocolx_protocols
            BEGIN
                UPDATE protocolx_protocols 
                SET sync_hash = hex(randomblob(16)),
                    updated_at = CURRENT_TIMESTAMP,
                    sync_status = 'pending'
                WHERE id = NEW.id;
            END`
        ];

        for (const sql of triggers) {
            await this.db.run(sql);
        }
        
        console.log('DatabaseManager', '[DatabaseManager] Triggers created', );
    }

    /**
     * Verify database integrity
     * @returns {Promise<boolean>} Integrity check result
     */
    async verifyIntegrity() {
        try {
            const result = await this.db.get('PRAGMA integrity_check');
            const isValid = result && result.integrity_check === 'ok';
            
            if (isValid) {
                console.log('DatabaseManager', '[DatabaseManager] Database integrity verified', );
            } else {
                console.error('DatabaseManager', '[DatabaseManager] Database integrity check failed:', result);
            }
            
            return isValid;
            
        } catch (error) {
            console.error('DatabaseManager', '[DatabaseManager] Integrity check error:', error);
            return false;
        }
    }

    /**
     * Execute a query with parameters
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Object>} Query result
     */
    async query(sql, params = []) {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }
        
        try {
            window.Logger?.database.debug('Executing query', { sql, params });
            
            const result = await this.db.run(sql, params);
            
            window.Logger?.database.debug('Query executed successfully', result);
            
            return result;
            
        } catch (error) {
            window.Logger?.database.error('Query execution failed', { sql, params, error: error.message });
            throw error;
        }
    }

    /**
     * Get single record
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Object|null>} Single record or null
     */
    async get(sql, params = []) {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }
        
        return await this.db.get(sql, params);
    }

    /**
     * Get multiple records
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Array>} Array of records
     */
    async all(sql, params = []) {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }
        
        return await this.db.all(sql, params);
    }

    /**
     * Execute SQL without parameters (for schema operations)
     * @param {string} sql - SQL to execute
     * @returns {Promise<Object>} Execution result
     */
    async exec(sql) {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }
        
        return await this.db.exec(sql);
    }

    /**
     * Begin transaction
     * @returns {Promise<void>}
     */
    async beginTransaction() {
        return await this.query('BEGIN TRANSACTION');
    }

    /**
     * Commit transaction
     * @returns {Promise<void>}
     */
    async commit() {
        return await this.query('COMMIT');
    }

    /**
     * Rollback transaction
     * @returns {Promise<void>}
     */
    async rollback() {
        return await this.query('ROLLBACK');
    }

    /**
     * Execute function within transaction
     * @param {Function} fn - Function to execute
     * @returns {Promise<*>} Function result
     */
    async transaction(fn) {
        await this.beginTransaction();
        
        try {
            const result = await fn(this);
            await this.commit();
            return result;
        } catch (error) {
            await this.rollback();
            throw error;
        }
    }

    /**
     * Get database statistics
     * @returns {Promise<Object>} Database statistics
     */
    async getStats() {
        const stats = {
            tables: {},
            totalRecords: 0,
            databaseSize: 0
        };
        
        try {
            // Get table names
            const tables = await this.all(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
            );
            
            // Get record count for each table
            for (const table of tables) {
                const result = await this.get(`SELECT COUNT(*) as count FROM ${table.name}`);
                stats.tables[table.name] = result.count;
                stats.totalRecords += result.count;
            }
            
            // Get database size
            const sizeResult = await this.get('PRAGMA page_count');
            const pageSizeResult = await this.get('PRAGMA page_size');
            
            if (sizeResult && pageSizeResult) {
                stats.databaseSize = sizeResult.page_count * pageSizeResult.page_size;
            }
            
        } catch (error) {
            console.error('DatabaseManager', '[DatabaseManager] Error getting stats:', error);
        }
        
        return stats;
    }

    /**
     * Close database connection
     * @returns {Promise<void>}
     */
    async close() {
        if (this.db) {
            // In a real implementation, we would close the SQLite connection
            // For now, we'll just mark as not initialized
            this.isInitialized = false;
            this.db = null;
            
            console.log('DatabaseManager', '[DatabaseManager] Database connection closed', );
        }
    }
}

// Create global instance
window.databaseManager = new DatabaseManager();

console.log('DatabaseManager', '[DatabaseManager] Global instance created successfully', );