/**
 * UpdateManager
 * Auto-update service for CoreDesk using electron-updater
 * 
 * Features:
 * - Automatic update checking
 * - Multiple update channels (stable, beta, development)
 * - Background downloads with progress tracking
 * - Environment-aware configuration (dev/production)
 * - Integration with portal UpdateService API
 */

const { app, dialog, shell } = require('electron');
const { autoUpdater } = require('electron-updater');
const axios = require('axios');

class UpdateManager extends EventTarget {
    constructor(options = {}) {
        super();
        
        // Configuration
        this.config = {
            // Environment-specific update URLs
            updateServers: {
                development: 'http://localhost:3000',
                production: 'https://coredeskpro.com'
            },
            
            // Update checking configuration
            checkInterval: 4 * 60 * 60 * 1000, // 4 hours in milliseconds
            autoDownload: true,
            autoInstallOnAppQuit: true,
            
            // Update channels
            channel: 'stable', // stable, beta, development
            
            // Notification settings
            silent: false,
            notifyUser: true,
            
            ...options
        };
        
        // State management
        this.isChecking = false;
        this.isDownloading = false;
        this.updateAvailable = false;
        this.updateInfo = null;
        this.downloadProgress = 0;
        this.lastCheckTime = null;
        this.checkIntervalId = null;
        
        // Get current environment
        this.environment = process.env.NODE_ENV || 'development';
        this.isDevelopment = this.environment === 'development';
        
        // Set update server URL based on environment
        this.updateServerUrl = this.config.updateServers[this.environment];
        
        // Logger
        this.logger = {
            info: (message) => console.log(`[UpdateManager] ${message}`),
            error: (message, error) => console.error(`[UpdateManager] ${message}`, error),
            warn: (message) => console.warn(`[UpdateManager] ${message}`),
            debug: (message) => {
                if (this.isDevelopment) {
                    console.debug(`[UpdateManager] ${message}`);
                }
            }
        };
        
        // Initialize
        this.initialize();
    }

    /**
     * Initialize the UpdateManager
     */
    initialize() {
        this.logger.info('Initializing UpdateManager...');
        this.logger.info(`Environment: ${this.environment}`);
        this.logger.info(`Update Server: ${this.updateServerUrl}`);
        this.logger.info(`Channel: ${this.config.channel}`);
        
        // Configure electron-updater
        this.configureAutoUpdater();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Start periodic checking if not in development
        if (!this.isDevelopment) {
            this.startPeriodicChecking();
        }
        
        this.logger.info('UpdateManager initialized successfully');
    }

    /**
     * Configure electron-updater settings
     */
    configureAutoUpdater() {
        // Set update server URL
        autoUpdater.setFeedURL({
            provider: 'generic',
            url: `${this.updateServerUrl}/download`
        });
        
        // Configure auto-updater options
        autoUpdater.autoDownload = this.config.autoDownload;
        autoUpdater.autoInstallOnAppQuit = this.config.autoInstallOnAppQuit;
        
        // Set channel
        autoUpdater.channel = this.config.channel;
        
        // Disable auto-updater in development unless explicitly enabled
        if (this.isDevelopment) {
            autoUpdater.updateConfigPath = null;
            this.logger.warn('Auto-updater disabled in development mode');
        }
        
        this.logger.debug('Auto-updater configured');
    }

    /**
     * Setup event listeners for auto-updater
     */
    setupEventListeners() {
        // Update checking events
        autoUpdater.on('checking-for-update', () => {
            this.logger.info('Checking for updates...');
            this.isChecking = true;
            this.dispatchEvent(new CustomEvent('checking-for-update'));
        });

        autoUpdater.on('update-available', (info) => {
            this.logger.info(`Update available: v${info.version}`);
            this.isChecking = false;
            this.updateAvailable = true;
            this.updateInfo = info;
            this.dispatchEvent(new CustomEvent('update-available', { detail: info }));
        });

        autoUpdater.on('update-not-available', (info) => {
            this.logger.info('No update available');
            this.isChecking = false;
            this.updateAvailable = false;
            this.dispatchEvent(new CustomEvent('update-not-available', { detail: info }));
        });

        // Download events
        autoUpdater.on('download-progress', (progressObj) => {
            this.downloadProgress = progressObj.percent;
            this.logger.debug(`Download progress: ${Math.round(progressObj.percent)}%`);
            this.dispatchEvent(new CustomEvent('download-progress', { detail: progressObj }));
        });

        autoUpdater.on('update-downloaded', (info) => {
            this.logger.info('Update downloaded successfully');
            this.isDownloading = false;
            this.dispatchEvent(new CustomEvent('update-downloaded', { detail: info }));
        });

        // Error handling
        autoUpdater.on('error', (error) => {
            this.logger.error('Auto-updater error:', error);
            this.isChecking = false;
            this.isDownloading = false;
            this.dispatchEvent(new CustomEvent('update-error', { detail: error }));
        });
    }

    /**
     * Check for updates manually
     */
    async checkForUpdates() {
        if (this.isChecking) {
            this.logger.warn('Update check already in progress');
            return false;
        }

        try {
            this.lastCheckTime = new Date();
            
            if (this.isDevelopment) {
                // In development, check via portal API
                return await this.checkForUpdatesViaAPI();
            } else {
                // In production, use electron-updater
                const result = await autoUpdater.checkForUpdates();
                return result !== null;
            }
        } catch (error) {
            this.logger.error('Error checking for updates:', error);
            this.dispatchEvent(new CustomEvent('update-error', { detail: error }));
            return false;
        }
    }

    /**
     * Check for updates via portal API (development mode)
     */
    async checkForUpdatesViaAPI() {
        try {
            const response = await axios.get(`${this.updateServerUrl}/updates/api/check`, {
                params: {
                    currentVersion: app.getVersion(),
                    channel: this.config.channel,
                    platform: process.platform
                },
                timeout: 10000
            });

            const { data } = response;
            
            if (data.success && data.updateAvailable) {
                this.updateAvailable = true;
                this.updateInfo = data.updateInfo;
                this.dispatchEvent(new CustomEvent('update-available', { detail: data.updateInfo }));
                return true;
            } else {
                this.updateAvailable = false;
                this.dispatchEvent(new CustomEvent('update-not-available'));
                return false;
            }
        } catch (error) {
            this.logger.error('Error checking updates via API:', error);
            throw error;
        }
    }

    /**
     * Download and install update
     */
    async downloadAndInstall() {
        if (!this.updateAvailable) {
            this.logger.warn('No update available to download');
            return false;
        }

        try {
            if (this.isDevelopment) {
                // In development, open download page
                shell.openExternal(`${this.updateServerUrl}/download`);
                return true;
            } else {
                // In production, use electron-updater
                this.isDownloading = true;
                await autoUpdater.downloadUpdate();
                return true;
            }
        } catch (error) {
            this.logger.error('Error downloading update:', error);
            this.isDownloading = false;
            this.dispatchEvent(new CustomEvent('update-error', { detail: error }));
            return false;
        }
    }

    /**
     * Install update and restart app
     */
    restartAndInstall() {
        if (this.isDevelopment) {
            this.logger.warn('Restart and install not available in development mode');
            return false;
        }

        try {
            autoUpdater.quitAndInstall();
            return true;
        } catch (error) {
            this.logger.error('Error installing update:', error);
            this.dispatchEvent(new CustomEvent('update-error', { detail: error }));
            return false;
        }
    }

    /**
     * Start periodic update checking
     */
    startPeriodicChecking() {
        if (this.checkIntervalId) {
            this.stopPeriodicChecking();
        }

        this.logger.info(`Starting periodic update checks every ${this.config.checkInterval / 1000 / 60} minutes`);
        
        // Check immediately on start
        setTimeout(() => this.checkForUpdates(), 30000); // Wait 30 seconds after app start
        
        // Then check periodically
        this.checkIntervalId = setInterval(() => {
            this.checkForUpdates();
        }, this.config.checkInterval);
    }

    /**
     * Stop periodic update checking
     */
    stopPeriodicChecking() {
        if (this.checkIntervalId) {
            clearInterval(this.checkIntervalId);
            this.checkIntervalId = null;
            this.logger.info('Stopped periodic update checking');
        }
    }

    /**
     * Set update channel
     */
    setChannel(channel) {
        const validChannels = ['stable', 'beta', 'development'];
        if (!validChannels.includes(channel)) {
            throw new Error(`Invalid channel: ${channel}. Must be one of: ${validChannels.join(', ')}`);
        }

        this.config.channel = channel;
        autoUpdater.channel = channel;
        this.logger.info(`Update channel set to: ${channel}`);
        
        // Trigger immediate check for updates
        this.checkForUpdates();
    }

    /**
     * Get current update status
     */
    getStatus() {
        return {
            isChecking: this.isChecking,
            isDownloading: this.isDownloading,
            updateAvailable: this.updateAvailable,
            updateInfo: this.updateInfo,
            downloadProgress: this.downloadProgress,
            lastCheckTime: this.lastCheckTime,
            currentVersion: app.getVersion(),
            channel: this.config.channel,
            environment: this.environment,
            updateServerUrl: this.updateServerUrl
        };
    }

    /**
     * Get update configuration
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        // Restart periodic checking if interval changed
        if (newConfig.checkInterval && this.checkIntervalId) {
            this.startPeriodicChecking();
        }
        
        this.logger.info('Configuration updated');
    }

    /**
     * Show update notification dialog
     */
    async showUpdateDialog(updateInfo) {
        const { response } = await dialog.showMessageBox({
            type: 'info',
            title: 'Actualización Disponible',
            message: `CoreDesk v${updateInfo.version} está disponible`,
            detail: `Tu versión actual es v${app.getVersion()}.\n\n¿Deseas descargar e instalar la actualización ahora?`,
            buttons: ['Descargar Ahora', 'Recordar Más Tarde', 'Ver Detalles'],
            defaultId: 0,
            cancelId: 1
        });

        switch (response) {
            case 0: // Download Now
                return 'download';
            case 1: // Remind Later
                return 'later';
            case 2: // View Details
                return 'details';
            default:
                return 'later';
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        this.stopPeriodicChecking();
        autoUpdater.removeAllListeners();
        this.logger.info('UpdateManager destroyed');
    }
}

// Make available globally
if (typeof window !== 'undefined') {
    window.UpdateManager = UpdateManager;
}

module.exports = UpdateManager;