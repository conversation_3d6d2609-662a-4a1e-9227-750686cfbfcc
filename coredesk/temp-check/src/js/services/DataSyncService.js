/**
 * DataSyncService.js
 * Hybrid synchronization service implementing local-first with optional Firebase sync
 * Provides conflict resolution, offline support, and conditional cloud integration
 */

// Dependencies will be available globally
// const FirebaseConnector = require('./FirebaseConnector');
// const { authApiService } = require('./api');
// const tokenManager = require('../auth/TokenManager');

class DataSyncService {
    constructor() {
        this.isInitialized = false;
        this.syncEnabled = false;
        this.cloudSyncEnabled = false;
        this.isOnline = navigator.onLine;
        this.isSyncing = false;
        
        this.syncQueue = [];
        this.conflictQueue = [];
        this.lastSyncTimestamp = null;
        this.syncInterval = null;
        
        this.config = {
            syncIntervalMs: 30000, // 30 seconds
            maxRetryAttempts: 3,
            batchSize: 50,
            conflictResolutionStrategy: 'client-wins', // 'client-wins', 'server-wins', 'manual'
            enableOfflineQueue: true,
            enableRealtime: false
        };
        
        this.syncStrategies = {
            'client-wins': this.resolveConflictClientWins.bind(this),
            'server-wins': this.resolveConflictServerWins.bind(this),
            'manual': this.resolveConflictManual.bind(this)
        };
        
        this.eventHandlers = new Map();
        this.firebaseConnector = null;
        this.realtimeListeners = new Map();
        
        this.initialize();
    }

    /**
     * Initialize the data sync service
     */
    async initialize() {
        console.log('Sync', '[DataSyncService] Initializing...', );
        
        try {
            // Load sync configuration
            await this.loadSyncConfiguration();
            
            // Set up network monitoring
            this.setupNetworkMonitoring();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Load sync queue from storage
            this.loadSyncQueue();
            
            // Initialize Firebase if configured
            if (this.shouldInitializeFirebase()) {
                await this.initializeFirebase();
            }
            
            // Start sync if enabled
            if (this.syncEnabled) {
                await this.startSync();
            }
            
            this.isInitialized = true;
            
            console.log('Sync', '[DataSyncService] Initialized successfully', );
            
            // Emit initialization event
            this.emit('initialized', {
                syncEnabled: this.syncEnabled,
                cloudSyncEnabled: this.cloudSyncEnabled,
                isOnline: this.isOnline
            });
            
        } catch (error) {
            console.error('Sync', '[DataSyncService] Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Load synchronization configuration
     * @private
     */
    async loadSyncConfiguration() {
        try {
            // Check if user has cloud access through license
            this.cloudSyncEnabled = await this.hasCloudAccess();
            
            // Load sync settings from localStorage first, then database
            const localSettings = this.loadLocalSyncSettings();
            this.syncEnabled = localSettings.enabled && this.cloudSyncEnabled;
            
            if (localSettings.interval) {
                this.config.syncIntervalMs = localSettings.interval * 1000;
            }
            
            // Load last sync timestamp
            const lastSync = localStorage.getItem('coredesk_last_sync');
            if (lastSync) {
                this.lastSyncTimestamp = new Date(lastSync);
            }
            
            console.log('Sync', '[DataSyncService] Configuration loaded', {
                syncEnabled: this.syncEnabled,
                cloudSyncEnabled: this.cloudSyncEnabled,
                interval: this.config.syncIntervalMs
            });
            
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error loading configuration:', error);
            
            // Fallback to basic configuration
            this.cloudSyncEnabled = false;
            this.syncEnabled = false;
        }
    }
    
    /**
     * Check if user has cloud access based on license
     * @returns {Promise<boolean>} True if cloud access is available
     */
    async hasCloudAccess() {
        const license = window.licenseManager?.currentLicense;
        
        if (!license) {
            console.log('Sync', '[DataSyncService] No license found, cloud access disabled', );
            return false;
        }
        
        // Check license type for cloud features
        const cloudEnabledTypes = ['professional', 'enterprise'];
        const hasCloudAccess = cloudEnabledTypes.includes(license.type) || 
                               license.modules?.includes('cloud-sync');
        
        console.log('Sync', '[DataSyncService] Cloud access check:', {
            licenseType: license.type,
            hasCloudAccess,
            modules: license.modules || []
        });
        
        return hasCloudAccess;
    }
    
    /**
     * Load sync settings from localStorage
     * @private
     */
    loadLocalSyncSettings() {
        const defaultSettings = {
            enabled: true,
            interval: 30, // seconds
            wifiOnly: false,
            autoSync: true
        };
        
        try {
            const stored = localStorage.getItem('coredesk_sync_settings');
            if (stored) {
                return { ...defaultSettings, ...JSON.parse(stored) };
            }
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error loading local settings:', error);
        }
        
        return defaultSettings;
    }
    
    /**
     * Save sync settings to localStorage
     * @private
     */
    saveSyncSettings(settings) {
        try {
            localStorage.setItem('coredesk_sync_settings', JSON.stringify(settings));
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error saving sync settings:', error);
        }
    }
    
    /**
     * Check if Firebase should be initialized
     * @returns {boolean} True if Firebase initialization is needed
     */
    shouldInitializeFirebase() {
        return this.cloudSyncEnabled && this.isOnline && tokenManager.isTokenValid();
    }

    /**
     * Initialize Firebase connection
     * @private
     */
    async initializeFirebase() {
        try {
            console.log('Sync', '[DataSyncService] Initializing Firebase...', );
            
            // Create Firebase connector instance
            this.firebaseConnector = new FirebaseConnector({
                apiKey: process.env.FIREBASE_API_KEY,
                authDomain: process.env.FIREBASE_AUTH_DOMAIN,
                projectId: process.env.FIREBASE_PROJECT_ID,
                storageBucket: process.env.FIREBASE_STORAGE_BUCKET
            });
            
            // Wait for initialization
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Authenticate with Firebase using custom token from backend
            const customToken = await this.getFirebaseCustomToken();
            if (customToken) {
                const authResult = await this.firebaseConnector.authenticate({
                    customToken
                });
                
                if (!authResult.success) {
                    throw new Error('Firebase authentication failed');
                }
            }
            
            console.log('Sync', '[DataSyncService] Firebase initialized successfully', );
            
        } catch (error) {
            console.error('Sync', '[DataSyncService] Firebase initialization failed:', error);
            this.cloudSyncEnabled = false;
            this.firebaseConnector = null;
        }
    }

    /**
     * Get Firebase custom token from backend
     * @returns {Promise<string|null>} Custom token
     * @private
     */
    async getFirebaseCustomToken() {
        try {
            const response = await authApiService.getFirebaseToken();
            if (response.success && response.token) {
                return response.token;
            }
        } catch (error) {
            console.error('Sync', '[DataSyncService] Failed to get Firebase token:', error);
        }
        return null;
    }

    /**
     * Set up network monitoring
     * @private
     */
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            console.log('Sync', '[DataSyncService] Network online', );
            this.isOnline = true;
            this.emit('network-changed', { isOnline: true });
            
            if (this.syncEnabled && this.syncQueue.length > 0) {
                this.processSyncQueue();
            }
        });
        
        window.addEventListener('offline', () => {
            console.log('Sync', '[DataSyncService] Network offline', );
            this.isOnline = false;
            this.emit('network-changed', { isOnline: false });
        });
    }

    /**
     * Set up event listeners
     * @private
     */
    setupEventListeners() {
        // Listen for data changes from other components
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.DATA_CHANGED, (event) => {
            this.handleDataChange(event.detail);
        });
        
        // Listen for license activation (may enable sync)
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.LICENSE_ACTIVATED, async (event) => {
            await this.handleLicenseActivation(event.detail);
        });
        
        // Listen for user logout
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.USER_LOGOUT, () => {
            this.handleUserLogout();
        });
    }
    
    /**
     * Handle license activation event
     * @param {Object} detail - License activation details
     */
    async handleLicenseActivation(detail) {
        console.log('Sync', '[DataSyncService] License activated, checking sync permissions', );
        
        // Re-evaluate cloud access
        const hadCloudAccess = this.cloudSyncEnabled;
        this.cloudSyncEnabled = await this.hasCloudAccess();
        
        if (!hadCloudAccess && this.cloudSyncEnabled) {
            // Cloud access was enabled, initialize sync
            console.log('Sync', '[DataSyncService] Cloud access enabled, starting sync', );
            
            this.syncEnabled = true;
            
            if (this.shouldInitializeFirebase()) {
                await this.initializeFirebase();
            }
            
            await this.startSync();
            
            this.emit('sync-enabled', {
                reason: 'license-activation',
                license: detail.license
            });
            
        } else if (hadCloudAccess && !this.cloudSyncEnabled) {
            // Cloud access was disabled, stop sync
            console.log('Sync', '[DataSyncService] Cloud access disabled, stopping sync', );
            
            this.stopSync();
            
            this.emit('sync-disabled', {
                reason: 'license-change'
            });
        }
    }
    
    /**
     * Handle user logout
     * @private
     */
    handleUserLogout() {
        console.log('Sync', '[DataSyncService] User logged out, stopping sync', );
        
        // Stop sync
        this.stopSync();
        
        // Clear Firebase connection
        if (this.firebaseConnector) {
            this.firebaseConnector.signOut();
            this.firebaseConnector = null;
        }
        
        // Clear sync queue
        this.syncQueue = [];
        this.saveSyncQueue();
    }
    
    /**
     * Handle data change events
     * @param {Object} detail - Data change details
     */
    handleDataChange(detail) {
        if (!this.syncEnabled || !this.cloudSyncEnabled) {
            return;
        }
        
        // Add to sync queue
        this.addToSyncQueue({
            type: 'data-change',
            table: detail.table,
            action: detail.action,
            recordId: detail.recordId,
            data: detail.data,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * Add item to sync queue
     * @param {Object} item - Item to sync
     */
    addToSyncQueue(item) {
        if (!this.cloudSyncEnabled) {
            return;
        }
        
        // Add unique ID
        item.id = this.generateOperationId();
        item.retryCount = 0;
        
        this.syncQueue.push(item);
        
        // Save to localStorage for persistence
        this.saveSyncQueue();
        
        // Process immediately if online and not currently syncing
        if (this.isOnline && !this.isSyncing) {
            setTimeout(() => this.processSyncQueue(), 1000);
        }
        
        console.log('Sync', '[DataSyncService] Item added to sync queue', {
            queueLength: this.syncQueue.length,
            item: { type: item.type, table: item.table, action: item.action }
        });
    }
    
    /**
     * Save sync queue to localStorage
     * @private
     */
    saveSyncQueue() {
        try {
            localStorage.setItem('coredesk_sync_queue', JSON.stringify(this.syncQueue));
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error saving sync queue:', error);
        }
    }
    
    /**
     * Load sync queue from localStorage
     * @private
     */
    loadSyncQueue() {
        try {
            const stored = localStorage.getItem('coredesk_sync_queue');
            if (stored) {
                this.syncQueue = JSON.parse(stored);
                console.log('Sync', '[DataSyncService] Sync queue loaded', {
                    queueLength: this.syncQueue.length
                });
            }
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error loading sync queue:', error);
            this.syncQueue = [];
        }
    }

    /**
     * Start synchronization service
     */
    async startSync() {
        if (!this.syncEnabled || this.syncInterval) {
            return;
        }
        
        console.log('Sync', '[DataSyncService] Starting sync service...', );
        
        try {
            // Perform initial sync
            await this.performSync();
            
            // Set up periodic sync
            this.syncInterval = setInterval(() => {
                if (!this.isSyncing && this.isOnline) {
                    this.performSync();
                }
            }, this.config.syncIntervalMs);
            
            // Setup real-time listeners if enabled
            if (this.config.enableRealtime && this.firebaseConnector) {
                await this.setupRealtimeListeners();
            }
            
            console.log('Sync', '[DataSyncService] Sync service started', );
            
            this.emit('sync-started', {
                interval: this.config.syncIntervalMs,
                cloudEnabled: this.cloudSyncEnabled,
                realtime: this.config.enableRealtime
            });
            
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error starting sync:', error);
        }
    }

    /**
     * Stop synchronization service
     */
    stopSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
            
            // Remove real-time listeners
            this.removeRealtimeListeners();
            
            console.log('Sync', '[DataSyncService] Sync service stopped', );
            
            this.emit('sync-stopped', {
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Setup real-time listeners for Firebase collections
     * @private
     */
    async setupRealtimeListeners() {
        if (!this.firebaseConnector || !this.firebaseConnector.isAuthenticated) {
            return;
        }
        
        const collections = ['cases', 'documents', 'sync_metadata'];
        
        for (const collection of collections) {
            try {
                const listenerId = this.firebaseConnector.setupRealtimeListener(
                    collection,
                    {
                        where: [['userId', '==', this.firebaseConnector.currentUser.uid]],
                        orderBy: [['updatedAt', 'desc']],
                        limit: 100
                    },
                    (change) => this.handleRealtimeChange(collection, change)
                );
                
                this.realtimeListeners.set(collection, listenerId);
                console.log('Sync', `[DataSyncService] Real-time listener setup for ${collection}`, );
                
            } catch (error) {
                console.error('Sync', `[DataSyncService] Failed to setup listener for ${collection}:`, error);
            }
        }
    }

    /**
     * Remove all real-time listeners
     * @private
     */
    removeRealtimeListeners() {
        if (!this.firebaseConnector) {
            return;
        }
        
        for (const [collection, listenerId] of this.realtimeListeners) {
            this.firebaseConnector.removeRealtimeListener(listenerId);
            console.log('Sync', `[DataSyncService] Real-time listener removed for ${collection}`, );
        }
        
        this.realtimeListeners.clear();
    }

    /**
     * Handle real-time change from Firebase
     * @param {string} collection - Collection name
     * @param {Object} change - Change event
     * @private
     */
    async handleRealtimeChange(collection, change) {
        if (change.type === 'error') {
            console.error('Sync', '[DataSyncService] Real-time listener error:', change.error);
            return;
        }
        
        console.log('Sync', '[DataSyncService] Real-time change detected', {
            collection,
            type: change.type,
            docId: change.doc?.id
        });
        
        try {
            // Apply change to local database
            await this.applyRemoteChange(collection, change);
            
            // Emit event for UI updates
            this.emit('remote-change', {
                collection,
                change,
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            console.error('Sync', '[DataSyncService] Failed to apply remote change:', error);
        }
    }

    /**
     * Apply remote change to local database
     * @param {string} collection - Collection name
     * @param {Object} change - Change data
     * @private
     */
    async applyRemoteChange(collection, change) {
        if (!window.electronAPI) {
            return;
        }
        
        const { type, doc } = change;
        const table = this.getLocalTableName(collection);
        
        switch (type) {
            case 'added':
            case 'modified':
                // Upsert to local database
                await this.upsertLocalRecord(table, doc.id, doc.data);
                break;
                
            case 'removed':
                // Delete from local database
                await this.deleteLocalRecord(table, doc.id);
                break;
        }
    }

    /**
     * Perform synchronization
     * @returns {Promise<Object>} Sync result
     */
    async performSync() {
        if (this.isSyncing || !this.cloudSyncEnabled || !this.isOnline) {
            return { success: false, reason: 'sync-not-ready' };
        }
        
        // Check if Firebase is connected
        if (!this.firebaseConnector || !this.firebaseConnector.isAuthenticated) {
            // Try to initialize Firebase
            if (this.shouldInitializeFirebase()) {
                await this.initializeFirebase();
            }
            
            if (!this.firebaseConnector || !this.firebaseConnector.isAuthenticated) {
                return { success: false, reason: 'firebase-not-connected' };
            }
        }
        
        this.isSyncing = true;
        
        console.log('Sync', '[DataSyncService] Starting sync operation...', );
        
        try {
            this.emit('sync-started-operation', {
                timestamp: new Date().toISOString(),
                queueSize: this.syncQueue.length
            });
            
            const syncResult = {
                success: true,
                timestamp: new Date().toISOString(),
                operations: {
                    pushed: 0,
                    pulled: 0,
                    conflicts: 0,
                    errors: 0
                }
            };
            
            // Process sync queue (local changes to push)
            if (this.syncQueue.length > 0) {
                const pushResult = await this.processSyncQueue();
                syncResult.operations.pushed = pushResult.success;
                syncResult.operations.errors += pushResult.errors;
            }
            
            // Pull remote changes
            const pullResult = await this.pullRemoteChanges();
            syncResult.operations.pulled = pullResult.pulled;
            syncResult.operations.conflicts += pullResult.conflicts;
            
            // Process conflicts
            if (this.conflictQueue.length > 0) {
                const conflictResult = await this.processConflicts();
                syncResult.operations.conflicts = conflictResult.resolved;
            }
            
            // Update last sync timestamp
            this.lastSyncTimestamp = new Date();
            localStorage.setItem('coredesk_last_sync', this.lastSyncTimestamp.toISOString());
            
            console.log('Sync', '[DataSyncService] Sync completed successfully:', syncResult);
            
            this.emit('sync-completed', syncResult);
            
            return syncResult;
            
        } catch (error) {
            console.error('Sync', '[DataSyncService] Sync operation failed:', error);
            
            const errorResult = {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
            
            this.emit('sync-error', errorResult);
            
            return errorResult;
            
        } finally {
            this.isSyncing = false;
        }
    }

    /**
     * Process sync queue (push local changes)
     * @returns {Promise<Object>} Push result
     * @private
     */
    async processSyncQueue() {
        const result = { success: 0, errors: 0 };
        
        if (!this.firebaseConnector || this.syncQueue.length === 0) {
            return result;
        }
        
        const processedItems = [];
        
        for (const item of this.syncQueue) {
            try {
                await this.pushItemToCloud(item);
                result.success++;
                processedItems.push(item.id);
                
            } catch (error) {
                console.error('Sync', '[DataSyncService] Failed to push item:', error);
                result.errors++;
                
                // Retry logic
                if (item.retryCount < this.config.maxRetryAttempts) {
                    item.retryCount++;
                    item.lastError = error.message;
                } else {
                    // Move to conflict queue after max retries
                    this.conflictQueue.push({
                        ...item,
                        error: error.message,
                        conflictType: 'push-failed'
                    });
                    processedItems.push(item.id);
                }
            }
        }
        
        // Remove processed items from queue
        this.syncQueue = this.syncQueue.filter(item => !processedItems.includes(item.id));
        this.saveSyncQueue();
        
        return result;
    }

    /**
     * Push single item to cloud
     * @param {Object} item - Sync queue item
     * @private
     */
    async pushItemToCloud(item) {
        if (!this.firebaseConnector) {
            throw new Error('Firebase not connected');
        }
        
        const collection = this.getCloudCollectionName(item.table);
        
        switch (item.action) {
            case 'INSERT':
            case 'UPDATE':
                await this.firebaseConnector.saveDocument(
                    collection,
                    item.recordId,
                    item.data
                );
                break;
                
            case 'DELETE':
                await this.firebaseConnector.deleteDocument(
                    collection,
                    item.recordId
                );
                break;
                
            default:
                throw new Error(`Unknown action: ${item.action}`);
        }
        
        console.log('Sync', '[DataSyncService] Pushed to cloud:', {
            collection,
            action: item.action,
            recordId: item.recordId
        });
    }

    /**
     * Pull remote changes from cloud
     * @returns {Promise<Object>} Pull result
     * @private
     */
    async pullRemoteChanges() {
        const result = { pulled: 0, conflicts: 0 };
        
        if (!this.firebaseConnector || !this.lastSyncTimestamp) {
            return result;
        }
        
        const collections = ['cases', 'documents', 'sync_metadata'];
        
        for (const collection of collections) {
            try {
                // Get changes since last sync
                const changes = await this.firebaseConnector.getChangesSince(
                    collection,
                    this.lastSyncTimestamp
                );
                
                for (const change of changes) {
                    try {
                        const hasConflict = await this.checkForConflict(collection, change);
                        
                        if (hasConflict) {
                            this.conflictQueue.push({
                                collection,
                                change,
                                conflictType: 'version-mismatch'
                            });
                            result.conflicts++;
                        } else {
                            await this.applyRemoteChange(collection, {
                                type: 'modified',
                                doc: change
                            });
                            result.pulled++;
                        }
                        
                    } catch (error) {
                        console.error('Sync', '[DataSyncService] Failed to apply change:', error);
                    }
                }
                
            } catch (error) {
                console.error('Sync', `[DataSyncService] Failed to pull changes for ${collection}:`, error);
            }
        }
        
        return result;
    }

    /**
     * Check for conflict between local and remote data
     * @param {string} collection - Collection name
     * @param {Object} remoteData - Remote data
     * @returns {Promise<boolean>} True if conflict exists
     * @private
     */
    async checkForConflict(collection, remoteData) {
        if (!window.electronAPI) {
            return false;
        }
        
        const table = this.getLocalTableName(collection);
        
        try {
            // Get local record
            const localData = await window.electronAPI.executeQuery(
                `SELECT * FROM ${table} WHERE id = ?`,
                [remoteData.id]
            );
            
            if (!localData || localData.length === 0) {
                return false; // No local record, no conflict
            }
            
            const localRecord = localData[0];
            
            // Compare versions or timestamps
            const localUpdated = new Date(localRecord.updated_at || localRecord.updatedAt);
            const remoteUpdated = new Date(remoteData.data.updatedAt);
            
            // Conflict if local has been modified after last sync
            return localUpdated > this.lastSyncTimestamp && localUpdated !== remoteUpdated;
            
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error checking for conflict:', error);
            return false;
        }
    }

    /**
     * Process conflicts
     * @returns {Promise<Object>} Conflict resolution result
     * @private
     */
    async processConflicts() {
        const result = { resolved: 0, pending: 0 };
        
        const resolvedConflicts = [];
        
        for (const conflict of this.conflictQueue) {
            try {
                const strategy = this.syncStrategies[this.config.conflictResolutionStrategy];
                if (strategy) {
                    const resolved = await strategy(conflict);
                    if (resolved.resolution !== 'manual-pending') {
                        resolvedConflicts.push(conflict);
                        result.resolved++;
                    } else {
                        result.pending++;
                    }
                }
            } catch (error) {
                console.error('Sync', '[DataSyncService] Conflict resolution failed:', error);
            }
        }
        
        // Remove resolved conflicts from queue
        this.conflictQueue = this.conflictQueue.filter(
            conflict => !resolvedConflicts.includes(conflict)
        );
        
        return result;
    }

    /**
     * Resolve conflict using client-wins strategy
     * @param {Object} conflict - Conflict to resolve
     * @returns {Promise<Object>} Resolved conflict
     * @private
     */
    async resolveConflictClientWins(conflict) {
        console.log('Sync', '[DataSyncService] Resolving conflict (client wins):', conflict);
        
        // Push local version to cloud
        if (conflict.conflictType === 'version-mismatch') {
            const localData = await this.getLocalRecord(
                conflict.collection,
                conflict.change.id
            );
            
            if (localData) {
                await this.firebaseConnector.saveDocument(
                    conflict.collection,
                    conflict.change.id,
                    localData
                );
            }
        }
        
        return {
            id: conflict.id,
            resolution: 'client-wins',
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Resolve conflict using server-wins strategy
     * @param {Object} conflict - Conflict to resolve
     * @returns {Promise<Object>} Resolved conflict
     * @private
     */
    async resolveConflictServerWins(conflict) {
        console.log('Sync', '[DataSyncService] Resolving conflict (server wins):', conflict);
        
        // Apply remote version to local
        if (conflict.conflictType === 'version-mismatch') {
            await this.applyRemoteChange(conflict.collection, {
                type: 'modified',
                doc: conflict.change
            });
        }
        
        return {
            id: conflict.id,
            resolution: 'server-wins',
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Resolve conflict manually
     * @param {Object} conflict - Conflict to resolve
     * @returns {Promise<Object>} Resolved conflict
     * @private
     */
    async resolveConflictManual(conflict) {
        console.log('Sync', '[DataSyncService] Conflict requires manual resolution:', conflict);
        
        // Emit event for manual resolution
        this.emit('conflict-requires-resolution', conflict);
        
        return {
            id: conflict.id,
            resolution: 'manual-pending',
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Get local table name for cloud collection
     * @param {string} collection - Cloud collection name
     * @returns {string} Local table name
     * @private
     */
    getLocalTableName(collection) {
        const mapping = {
            'cases': 'cases',
            'documents': 'documents',
            'sync_metadata': 'sync_log',
            'users': 'users',
            'licenses': 'licenses',
            'activations': 'activations'
        };
        
        return mapping[collection] || collection;
    }

    /**
     * Get cloud collection name for local table
     * @param {string} table - Local table name
     * @returns {string} Cloud collection name
     * @private
     */
    getCloudCollectionName(table) {
        const mapping = {
            'cases': 'cases',
            'documents': 'documents',
            'sync_log': 'sync_metadata',
            'users': 'users',
            'licenses': 'licenses',
            'activations': 'activations'
        };
        
        return mapping[table] || table;
    }

    /**
     * Get local record
     * @param {string} collection - Collection name
     * @param {string} recordId - Record ID
     * @returns {Promise<Object|null>} Local record
     * @private
     */
    async getLocalRecord(collection, recordId) {
        if (!window.electronAPI) {
            return null;
        }
        
        const table = this.getLocalTableName(collection);
        
        try {
            const result = await window.electronAPI.executeQuery(
                `SELECT * FROM ${table} WHERE id = ?`,
                [recordId]
            );
            
            return result && result.length > 0 ? result[0] : null;
            
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error getting local record:', error);
            return null;
        }
    }

    /**
     * Upsert local record
     * @param {string} table - Table name
     * @param {string} recordId - Record ID
     * @param {Object} data - Record data
     * @private
     */
    async upsertLocalRecord(table, recordId, data) {
        if (!window.electronAPI) {
            return;
        }
        
        try {
            // Check if record exists
            const existing = await window.electronAPI.executeQuery(
                `SELECT id FROM ${table} WHERE id = ?`,
                [recordId]
            );
            
            if (existing && existing.length > 0) {
                // Update existing record
                const updateFields = Object.keys(data)
                    .filter(key => key !== 'id')
                    .map(key => `${key} = ?`);
                
                const updateValues = Object.keys(data)
                    .filter(key => key !== 'id')
                    .map(key => data[key]);
                
                await window.electronAPI.executeQuery(
                    `UPDATE ${table} SET ${updateFields.join(', ')}, sync_hash = ? WHERE id = ?`,
                    [...updateValues, this.generateSyncHash(data), recordId]
                );
            } else {
                // Insert new record
                const fields = ['id', ...Object.keys(data), 'sync_hash'];
                const placeholders = fields.map(() => '?').join(', ');
                const values = [recordId, ...Object.values(data), this.generateSyncHash(data)];
                
                await window.electronAPI.executeQuery(
                    `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`,
                    values
                );
            }
            
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error upserting local record:', error);
        }
    }

    /**
     * Delete local record
     * @param {string} table - Table name
     * @param {string} recordId - Record ID
     * @private
     */
    async deleteLocalRecord(table, recordId) {
        if (!window.electronAPI) {
            return;
        }
        
        try {
            await window.electronAPI.executeQuery(
                `DELETE FROM ${table} WHERE id = ?`,
                [recordId]
            );
        } catch (error) {
            console.error('Sync', '[DataSyncService] Error deleting local record:', error);
        }
    }

    /**
     * Generate sync hash for data
     * @param {Object} data - Data to hash
     * @returns {string} Sync hash
     * @private
     */
    generateSyncHash(data) {
        const crypto = require('crypto');
        const content = JSON.stringify(data, Object.keys(data).sort());
        return crypto.createHash('sha256').update(content).digest('hex');
    }

    /**
     * Generate unique operation ID
     * @returns {string} Operation ID
     * @private
     */
    generateOperationId() {
        return `sync_op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Force immediate synchronization
     * @returns {Promise<Object>} Sync result
     */
    async forceSync() {
        console.log('Sync', '[DataSyncService] Force sync requested', );
        
        if (this.isSyncing) {
            return { success: false, reason: 'sync-in-progress' };
        }
        
        return await this.performSync();
    }

    /**
     * Get synchronization status
     * @returns {Object} Sync status
     */
    getSyncStatus() {
        return {
            isInitialized: this.isInitialized,
            syncEnabled: this.syncEnabled,
            cloudSyncEnabled: this.cloudSyncEnabled,
            isOnline: this.isOnline,
            isSyncing: this.isSyncing,
            lastSyncTimestamp: this.lastSyncTimestamp,
            queueSize: this.syncQueue.length,
            conflictCount: this.conflictQueue.length,
            firebaseConnected: this.firebaseConnector?.isAuthenticated || false,
            realtimeListeners: this.realtimeListeners.size,
            config: { ...this.config }
        };
    }

    /**
     * Update sync configuration
     * @param {Object} newConfig - New configuration
     */
    async updateConfig(newConfig) {
        const oldConfig = { ...this.config };
        
        Object.assign(this.config, newConfig);
        
        // Save settings
        const settings = this.loadLocalSyncSettings();
        Object.assign(settings, newConfig);
        this.saveSyncSettings(settings);
        
        // Restart sync if interval changed
        if (oldConfig.syncIntervalMs !== this.config.syncIntervalMs && this.syncInterval) {
            this.stopSync();
            await this.startSync();
        }
        
        // Setup/remove realtime listeners if changed
        if (oldConfig.enableRealtime !== this.config.enableRealtime) {
            if (this.config.enableRealtime && this.firebaseConnector) {
                await this.setupRealtimeListeners();
            } else {
                this.removeRealtimeListeners();
            }
        }
        
        console.log('Sync', '[DataSyncService] Configuration updated:', newConfig);
        
        this.emit('config-updated', {
            oldConfig,
            newConfig: this.config
        });
    }

    /**
     * Enable sync (if user has cloud access)
     */
    async enableSync() {
        if (!await this.hasCloudAccess()) {
            throw new Error('Cloud access required to enable sync');
        }
        
        this.syncEnabled = true;
        this.cloudSyncEnabled = true;
        
        if (this.shouldInitializeFirebase() && !this.firebaseConnector) {
            await this.initializeFirebase();
        }
        
        await this.startSync();
        
        // Save settings
        const settings = this.loadLocalSyncSettings();
        settings.enabled = true;
        this.saveSyncSettings(settings);
        
        console.log('Sync', '[DataSyncService] Sync enabled', );
        
        this.emit('sync-enabled', {
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * Disable sync
     */
    disableSync() {
        this.syncEnabled = false;
        this.stopSync();
        
        // Save settings
        const settings = this.loadLocalSyncSettings();
        settings.enabled = false;
        this.saveSyncSettings(settings);
        
        console.log('Sync', '[DataSyncService] Sync disabled', );
        
        this.emit('sync-disabled', {
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * Add event listener
     * @param {string} event - Event name
     * @param {Function} handler - Event handler
     */
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }

    /**
     * Remove event listener
     * @param {string} event - Event name
     * @param {Function} handler - Event handler
     */
    off(event, handler) {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index !== -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Emit event
     * @param {string} event - Event name
     * @param {*} data - Event data
     * @private
     */
    emit(event, data) {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`[DataSyncService] Event handler error for ${event}:`, error);
                }
            });
        }
        
        // Also emit global event
        window.CoreDeskEvents?.emit(`sync:${event}`, data);
    }

    /**
     * Get synchronization statistics
     * @returns {Object} Sync statistics
     */
    getSyncStats() {
        return {
            syncEnabled: this.syncEnabled,
            cloudSyncEnabled: this.cloudSyncEnabled,
            isOnline: this.isOnline,
            lastSyncTime: this.lastSyncTime,
            syncInProgress: this.syncInProgress,
            queueLength: this.syncQueue.length,
            totalOperations: this.totalOperations || 0,
            successfulOperations: this.successfulOperations || 0,
            failedOperations: this.failedOperations || 0,
            conflictsResolved: this.conflictsResolved || 0,
            status: this.getSyncStatus()
        };
    }

    /**
     * Get current sync status
     * @returns {string} Sync status
     */
    getSyncStatus() {
        if (!this.syncEnabled) return 'disabled';
        if (!this.cloudSyncEnabled) return 'local-only';
        if (!this.isOnline) return 'offline';
        if (this.syncInProgress) return 'syncing';
        if (this.syncQueue.length > 0) return 'pending';
        return 'up-to-date';
    }
}

// Create global instance
window.dataSyncService = new DataSyncService();

console.log('Sync', '[DataSyncService] Class defined and global instance created successfully', );