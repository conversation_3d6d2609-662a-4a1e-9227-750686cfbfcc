/**
 * WindowHandlers.js
 * Window control IPC handlers for main process
 */

const { ipcMain } = require('electron');

class WindowHandlers {
    constructor(window) {
        this.window = window;
        this.setupHandlers();
    }

    setupHandlers() {
        // Window minimize
        ipcMain.handle('window:minimize', () => {
            if (this.window) {
                this.window.minimize();
            }
        });

        // Window maximize/restore
        ipcMain.handle('window:maximize', () => {
            if (this.window) {
                if (this.window.isMaximized()) {
                    this.window.unmaximize();
                } else {
                    this.window.maximize();
                }
            }
        });

        // Window close
        ipcMain.handle('window:close', () => {
            if (this.window) {
                this.window.close();
            }
        });

        // Window state queries
        ipcMain.handle('window:isMaximized', () => {
            return this.window ? this.window.isMaximized() : false;
        });

        ipcMain.handle('window:isMinimized', () => {
            return this.window ? this.window.isMinimized() : false;
        });

        ipcMain.handle('window:isFocused', () => {
            return this.window ? this.window.isFocused() : false;
        });
    }

    cleanup() {
        // Remove handlers when needed
        ipcMain.removeAllListeners('window:minimize');
        ipcMain.removeAllListeners('window:maximize');
        ipcMain.removeAllListeners('window:close');
        ipcMain.removeAllListeners('window:isMaximized');
        ipcMain.removeAllListeners('window:isMinimized');
        ipcMain.removeAllListeners('window:isFocused');
    }
}

module.exports = WindowHandlers;