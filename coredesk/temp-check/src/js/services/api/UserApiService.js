/**
 * UserApiService.js
 * User management API service for CoreDesk
 * Handles user profile and account-related API calls
 */

const apiClient = require('./ApiClient');

class UserApiService {
    constructor() {
        this.endpoints = {
            profile: '/users/profile',
            updateProfile: '/users/profile',
            changePassword: '/users/change-password',
            uploadAvatar: '/users/avatar',
            deleteAvatar: '/users/avatar',
            getUsageStats: '/users/usage-stats',
            getActivityLog: '/users/activity-log',
            getPreferences: '/users/preferences',
            updatePreferences: '/users/preferences',
            exportData: '/users/export-data',
            deleteAccount: '/users/delete-account'
        };
    }

    /**
     * Get user profile
     * @returns {Promise<Object>} Profile response
     */
    async getProfile() {
        try {
            const response = await apiClient.get(this.endpoints.profile);

            return {
                success: true,
                profile: response.data,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to get profile',
                code: error.code
            };
        }
    }

    /**
     * Update user profile
     * @param {Object} profileData - Profile update data
     * @returns {Promise<Object>} Update response
     */
    async updateProfile(profileData) {
        try {
            const response = await apiClient.put(this.endpoints.updateProfile, profileData);

            return {
                success: true,
                profile: response.data,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to update profile',
                code: error.code
            };
        }
    }

    /**
     * Change user password
     * @param {string} oldPassword - Current password
     * @param {string} newPassword - New password
     * @returns {Promise<Object>} Password change response
     */
    async changePassword(oldPassword, newPassword) {
        try {
            const response = await apiClient.post(this.endpoints.changePassword, {
                oldPassword,
                newPassword
            });

            return {
                success: true,
                message: response.data.message,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to change password',
                code: error.code
            };
        }
    }

    /**
     * Upload user avatar
     * @param {File} avatarFile - Avatar image file
     * @returns {Promise<Object>} Upload response
     */
    async uploadAvatar(avatarFile) {
        try {
            const formData = new FormData();
            formData.append('avatar', avatarFile);

            const response = await apiClient.post(this.endpoints.uploadAvatar, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            return {
                success: true,
                avatarUrl: response.data.avatarUrl,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to upload avatar',
                code: error.code
            };
        }
    }

    /**
     * Delete user avatar
     * @returns {Promise<Object>} Delete response
     */
    async deleteAvatar() {
        try {
            const response = await apiClient.delete(this.endpoints.deleteAvatar);

            return {
                success: true,
                message: response.data.message,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to delete avatar',
                code: error.code
            };
        }
    }

    /**
     * Get user usage statistics
     * @param {Object} params - Query parameters (dateFrom, dateTo, etc.)
     * @returns {Promise<Object>} Usage stats response
     */
    async getUsageStats(params = {}) {
        try {
            const response = await apiClient.get(this.endpoints.getUsageStats, {
                params
            });

            return {
                success: true,
                stats: response.data.stats,
                summary: response.data.summary,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to get usage stats',
                code: error.code
            };
        }
    }

    /**
     * Get user activity log
     * @param {Object} params - Query parameters (page, limit, filter, etc.)
     * @returns {Promise<Object>} Activity log response
     */
    async getActivityLog(params = {}) {
        try {
            const response = await apiClient.get(this.endpoints.getActivityLog, {
                params: {
                    page: params.page || 1,
                    limit: params.limit || 50,
                    ...params
                }
            });

            return {
                success: true,
                activities: response.data.activities,
                pagination: response.data.pagination,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to get activity log',
                code: error.code
            };
        }
    }

    /**
     * Get user preferences
     * @returns {Promise<Object>} Preferences response
     */
    async getPreferences() {
        try {
            const response = await apiClient.get(this.endpoints.getPreferences);

            return {
                success: true,
                preferences: response.data.preferences,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to get preferences',
                code: error.code
            };
        }
    }

    /**
     * Update user preferences
     * @param {Object} preferences - Preferences to update
     * @returns {Promise<Object>} Update response
     */
    async updatePreferences(preferences) {
        try {
            const response = await apiClient.put(this.endpoints.updatePreferences, {
                preferences
            });

            return {
                success: true,
                preferences: response.data.preferences,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to update preferences',
                code: error.code
            };
        }
    }

    /**
     * Export user data
     * @param {string} format - Export format (json, csv, pdf)
     * @returns {Promise<Object>} Export response
     */
    async exportData(format = 'json') {
        try {
            const response = await apiClient.post(this.endpoints.exportData, {
                format
            });

            return {
                success: true,
                downloadUrl: response.data.downloadUrl,
                expiresAt: response.data.expiresAt,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to export data',
                code: error.code
            };
        }
    }

    /**
     * Delete user account
     * @param {string} password - User password for confirmation
     * @param {string} reason - Deletion reason (optional)
     * @returns {Promise<Object>} Deletion response
     */
    async deleteAccount(password, reason = '') {
        try {
            const response = await apiClient.delete(this.endpoints.deleteAccount, {
                data: {
                    password,
                    reason
                }
            });

            return {
                success: true,
                message: response.data.message,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to delete account',
                code: error.code
            };
        }
    }

    /**
     * Format user data for display
     * @param {Object} userData - Raw user data
     * @returns {Object} Formatted user data
     */
    formatUserData(userData) {
        return {
            id: userData.id,
            email: userData.email,
            fullName: userData.fullName || userData.full_name,
            displayName: userData.displayName || userData.display_name || userData.fullName || 'User',
            avatarUrl: userData.avatarUrl || userData.avatar_url,
            role: userData.role || 'user',
            isActive: userData.isActive !== undefined ? userData.isActive : userData.is_active,
            createdAt: new Date(userData.createdAt || userData.created_at),
            updatedAt: new Date(userData.updatedAt || userData.updated_at),
            lastLogin: userData.lastLogin ? new Date(userData.lastLogin || userData.last_login) : null,
            license: userData.license,
            preferences: userData.preferences || {},
            stats: userData.stats || {}
        };
    }

    /**
     * Calculate storage usage
     * @param {Object} stats - Usage statistics
     * @returns {Object} Storage usage info
     */
    calculateStorageUsage(stats) {
        const bytesToMB = (bytes) => (bytes / (1024 * 1024)).toFixed(2);
        const bytesToGB = (bytes) => (bytes / (1024 * 1024 * 1024)).toFixed(2);

        const totalBytes = stats.totalStorageUsed || 0;
        const limitBytes = stats.storageLimit || 5 * 1024 * 1024 * 1024; // 5GB default
        const usagePercent = (totalBytes / limitBytes * 100).toFixed(1);

        return {
            used: totalBytes < 1024 * 1024 * 1024 ? `${bytesToMB(totalBytes)} MB` : `${bytesToGB(totalBytes)} GB`,
            limit: `${bytesToGB(limitBytes)} GB`,
            percent: parseFloat(usagePercent),
            remaining: limitBytes - totalBytes,
            isNearLimit: usagePercent > 80,
            isOverLimit: totalBytes > limitBytes
        };
    }
}

// Create singleton instance
const userApiService = new UserApiService();

module.exports = userApiService;