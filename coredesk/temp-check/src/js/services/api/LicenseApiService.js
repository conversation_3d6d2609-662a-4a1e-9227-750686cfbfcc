/**
 * LicenseApiService.js
 * License management API service for CoreDesk
 * Handles all license-related API calls including trial flow
 */

// ApiClient will be available globally
// const apiClient = require('./ApiClient');

class LicenseApiService {
    constructor() {
        this.apiClient = window.apiClient || null;
        this.endpoints = {
            checkEligibility: '/licenses/trial/check-eligibility',
            requestTrial: '/licenses/trial/request',
            verifyEmail: '/licenses/trial/verify-email',
            generateTrial: '/licenses/trial/generate',
            validateLicense: '/licenses/validate',
            activateLicense: '/licenses/activate',
            deactivateLicense: '/licenses/deactivate',
            getLicenseInfo: '/licenses/info',
            getLicenseHistory: '/licenses/history',
            renewLicense: '/licenses/renew',
            upgradeLicense: '/licenses/upgrade'
        };
    }

    /**
     * Check trial eligibility
     * @param {string} deviceFingerprint - Device fingerprint
     * @returns {Promise<Object>} Eligibility response
     */
    async checkTrialEligibility(deviceFingerprint) {
        try {
            const response = await this.apiClient.post(this.endpoints.checkEligibility, {
                deviceFingerprint
            });

            return {
                success: true,
                eligible: response.data.eligible,
                reason: response.data.reason,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                eligible: false,
                error: error.message || 'Failed to check eligibility',
                code: error.code
            };
        }
    }

    /**
     * Request trial license (Step 1)
     * @param {string} email - User email
     * @param {string} deviceFingerprint - Device fingerprint
     * @returns {Promise<Object>} Trial request response
     */
    async requestTrial(email, deviceFingerprint) {
        try {
            const response = await this.apiClient.post(this.endpoints.requestTrial, {
                email,
                deviceFingerprint
            });

            return {
                success: true,
                requestId: response.data.requestId,
                message: response.data.message,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to request trial',
                code: error.code,
                details: error.data
            };
        }
    }

    /**
     * Verify email with code (Step 2)
     * @param {string} email - User email
     * @param {string} verificationCode - 6-digit verification code
     * @param {string} requestId - Request ID from step 1
     * @returns {Promise<Object>} Verification response
     */
    async verifyEmail(email, verificationCode, requestId) {
        try {
            const response = await this.apiClient.post(this.endpoints.verifyEmail, {
                email,
                verificationCode,
                requestId
            });

            return {
                success: true,
                verified: response.data.verified,
                token: response.data.token,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                verified: false,
                error: error.message || 'Failed to verify email',
                code: error.code
            };
        }
    }

    /**
     * Generate trial license (Step 3)
     * @param {Object} data - Trial generation data
     * @returns {Promise<Object>} License generation response
     */
    async generateTrial(data) {
        try {
            const response = await this.apiClient.post(this.endpoints.generateTrial, {
                email: data.email,
                token: data.token,
                deviceFingerprint: data.deviceFingerprint,
                requestId: data.requestId
            });

            return {
                success: true,
                licenseKey: response.data.licenseKey,
                expiresAt: response.data.expiresAt,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to generate trial license',
                code: error.code
            };
        }
    }

    /**
     * Validate license key
     * @param {string} licenseKey - License key to validate
     * @returns {Promise<Object>} Validation response
     */
    async validateLicense(licenseKey) {
        try {
            const response = await this.apiClient.post(this.endpoints.validateLicense, {
                licenseKey
            });

            return {
                success: true,
                valid: response.data.valid,
                licenseInfo: response.data.licenseInfo,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                valid: false,
                error: error.message || 'Failed to validate license',
                code: error.code
            };
        }
    }

    /**
     * Activate license (Step 4)
     * @param {string} licenseKey - License key
     * @param {Object} deviceInfo - Device information
     * @returns {Promise<Object>} Activation response
     */
    async activateLicense(licenseKey, deviceInfo) {
        try {
            const response = await this.apiClient.post(this.endpoints.activateLicense, {
                licenseKey,
                deviceInfo: {
                    fingerprint: deviceInfo.fingerprint,
                    hostname: deviceInfo.hostname,
                    platform: deviceInfo.platform,
                    arch: deviceInfo.arch,
                    cpuInfo: deviceInfo.cpuInfo,
                    macAddresses: deviceInfo.macAddresses,
                    displayInfo: deviceInfo.displayInfo
                }
            });

            return {
                success: true,
                activated: true,
                activationId: response.data.activationId,
                expiresAt: response.data.expiresAt,
                features: response.data.features,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                activated: false,
                error: error.message || 'Failed to activate license',
                code: error.code
            };
        }
    }

    /**
     * Deactivate license
     * @param {string} licenseKey - License key
     * @param {string} activationId - Activation ID
     * @returns {Promise<Object>} Deactivation response
     */
    async deactivateLicense(licenseKey, activationId) {
        try {
            const response = await this.apiClient.post(this.endpoints.deactivateLicense, {
                licenseKey,
                activationId
            });

            return {
                success: true,
                deactivated: true,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                deactivated: false,
                error: error.message || 'Failed to deactivate license',
                code: error.code
            };
        }
    }

    /**
     * Get license information
     * @param {string} licenseKey - License key
     * @returns {Promise<Object>} License info response
     */
    async getLicenseInfo(licenseKey) {
        try {
            const response = await this.apiClient.get(`${this.endpoints.getLicenseInfo}/${licenseKey}`);

            return {
                success: true,
                licenseInfo: response.data,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to get license info',
                code: error.code
            };
        }
    }

    /**
     * Get license history
     * @param {string} licenseKey - License key
     * @returns {Promise<Object>} License history response
     */
    async getLicenseHistory(licenseKey) {
        try {
            const response = await this.apiClient.get(`${this.endpoints.getLicenseHistory}/${licenseKey}`);

            return {
                success: true,
                history: response.data.history,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to get license history',
                code: error.code
            };
        }
    }

    /**
     * Renew license
     * @param {string} licenseKey - License key to renew
     * @param {Object} renewalData - Renewal information
     * @returns {Promise<Object>} Renewal response
     */
    async renewLicense(licenseKey, renewalData) {
        try {
            const response = await this.apiClient.post(this.endpoints.renewLicense, {
                licenseKey,
                ...renewalData
            });

            return {
                success: true,
                renewed: true,
                newExpiryDate: response.data.expiresAt,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                renewed: false,
                error: error.message || 'Failed to renew license',
                code: error.code
            };
        }
    }

    /**
     * Upgrade license
     * @param {string} currentLicenseKey - Current license key
     * @param {string} targetPlan - Target plan to upgrade to
     * @returns {Promise<Object>} Upgrade response
     */
    async upgradeLicense(currentLicenseKey, targetPlan) {
        try {
            const response = await this.apiClient.post(this.endpoints.upgradeLicense, {
                currentLicenseKey,
                targetPlan
            });

            return {
                success: true,
                upgraded: true,
                newLicenseKey: response.data.newLicenseKey,
                features: response.data.features,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                upgraded: false,
                error: error.message || 'Failed to upgrade license',
                code: error.code
            };
        }
    }

    /**
     * Format license expiry date
     * @param {string} expiryDate - Expiry date string
     * @returns {Object} Formatted expiry info
     */
    formatExpiryInfo(expiryDate) {
        const expiry = new Date(expiryDate);
        const now = new Date();
        const daysRemaining = Math.floor((expiry - now) / (1000 * 60 * 60 * 24));
        
        return {
            expiryDate: expiry.toLocaleDateString(),
            daysRemaining,
            isExpired: daysRemaining < 0,
            isExpiringSoon: daysRemaining > 0 && daysRemaining <= 7
        };
    }

    /**
     * Validate license key format
     * @param {string} licenseKey - License key to validate
     * @returns {boolean} Validation result
     */
    validateLicenseKeyFormat(licenseKey) {
        // CoreDesk license format: XXXX-XXXX-XXXX-XXXX
        const licensePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
        return licensePattern.test(licenseKey);
    }

    /**
     * Initialize dependencies after all components are loaded
     */
    initializeDependencies() {
        if (!this.apiClient && window.apiClient) {
            this.apiClient = window.apiClient;
        }
    }
}

// Create singleton instance
const licenseApiService = new LicenseApiService();

// Make available globally for browser environment
if (typeof window !== 'undefined') {
    window.LicenseApiService = LicenseApiService;
    window.licenseApiService = licenseApiService;
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = licenseApiService;
}