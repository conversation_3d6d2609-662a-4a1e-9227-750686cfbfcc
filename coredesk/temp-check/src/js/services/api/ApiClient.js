/**
 * ApiClient.js
 * Base API client with axios configuration for CoreDesk
 * Handles request/response interceptors, token attachment, and error handling
 */

// Axios will be available globally (loaded via CDN or bundler)
// const axios = require('axios');
// require('dotenv').config();

class ApiClient {
    constructor() {
        // SECURITY: Always use HTTPS in production
        // Use environment variable or config to determine if we're in development
        const isDevelopment = window.CoreDeskAuth?.environment === 'development' || 
                            window.location.hostname === 'localhost' || 
                            window.location.hostname === '127.0.0.1';
        
        // Use configured baseUrl or fallback to development default
        const defaultUrl = `https://api.coredeskpro.com/v1`; // Correct production API URL
        this.baseURL = window.CoreDeskAuth?.api?.baseUrl || defaultUrl;
        
        // SECURITY: Only warn about HTTP in production, but respect configured baseUrl
        if (!isDevelopment && this.baseURL.startsWith('http://')) {
            console.warn('[ApiClient] Using HTTP in non-development environment. Ensure this is intentional.');
            // Don't automatically convert to HTTPS - respect the configuration
        }
        
        this.client = null;
        this.isRefreshing = false;
        this.failedQueue = [];
        
        this.initialize();
    }

    /**
     * Initialize axios instance with configuration
     */
    initialize() {
        console.log('[ApiClient] Initializing... baseURL:', this.baseURL);
        
        // Check if axios is available
        if (typeof window !== 'undefined' && window.axios) {
            console.log('[ApiClient] Axios found, creating client...');
            this.client = window.axios.create({
            baseURL: this.baseURL,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Client-Version': '2.0.0',
                'X-Client-Platform': 'electron'
            }
            });
            
            this.setupInterceptors();
            console.log('[ApiClient] Initialization complete');
        } else {
            console.error('[ApiClient] Axios not available - API calls will fail');
            console.error('[ApiClient] Please ensure axios is loaded before ApiClient');
        }
    }

    /**
     * Setup request and response interceptors
     */
    setupInterceptors() {
        // Request interceptor
        this.client.interceptors.request.use(
            (config) => {
                // Attach token if available
                const token = this.getToken();
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }

                // Add request timestamp
                config.metadata = { startTime: new Date() };

                // Log request in development (browser-safe environment check)
                const isDevelopment = window.CoreDeskAuth?.environment === 'development' || 
                                    window.location.hostname === 'localhost' || 
                                    window.location.hostname === '127.0.0.1';
                                    
                if (isDevelopment) {
                    console.log('API Request:', {
                        method: config.method.toUpperCase(),
                        url: config.url,
                        params: config.params,
                        data: config.data
                    });
                }

                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // Response interceptor
        this.client.interceptors.response.use(
            (response) => {
                // Calculate request duration
                const duration = new Date() - response.config.metadata.startTime;

                // Log response in development (browser-safe environment check)
                const isDevelopment = window.CoreDeskAuth?.environment === 'development' || 
                                    window.location.hostname === 'localhost' || 
                                    window.location.hostname === '127.0.0.1';
                                    
                if (isDevelopment) {
                    console.log('API Response:', {
                        status: response.status,
                        url: response.config.url,
                        duration: `${duration}ms`,
                        data: response.data
                    });
                }

                return response;
            },
            async (error) => {
                const originalRequest = error.config;

                // Handle 401 Unauthorized
                if (error.response?.status === 401 && !originalRequest._retry) {
                    // Skip token refresh for auth endpoints (login, register, etc.)
                    if (originalRequest.url && originalRequest.url.includes('/auth/')) {
                        console.log('[ApiClient] 401 on auth endpoint - not attempting token refresh');
                        return Promise.reject(error);
                    }
                    
                    if (this.isRefreshing) {
                        // Token refresh is already in progress
                        return new Promise((resolve, reject) => {
                            this.failedQueue.push({ resolve, reject });
                        }).then(token => {
                            originalRequest.headers.Authorization = `Bearer ${token}`;
                            return this.client(originalRequest);
                        }).catch(err => {
                            return Promise.reject(err);
                        });
                    }

                    originalRequest._retry = true;
                    this.isRefreshing = true;

                    try {
                        const newToken = await this.refreshToken();
                        
                        // Process queued requests
                        this.processQueue(null, newToken);
                        
                        // Retry original request
                        originalRequest.headers.Authorization = `Bearer ${newToken}`;
                        return this.client(originalRequest);
                        
                    } catch (refreshError) {
                        // Token refresh failed
                        this.processQueue(refreshError, null);
                        
                        // Clear auth data and redirect to login
                        this.clearAuthData();
                        this.redirectToLogin();
                        
                        return Promise.reject(refreshError);
                    } finally {
                        this.isRefreshing = false;
                    }
                }

                // Handle other errors
                return Promise.reject(this.handleError(error));
            }
        );
    }

    /**
     * Process queued requests after token refresh
     */
    processQueue(error, token = null) {
        this.failedQueue.forEach(prom => {
            if (error) {
                prom.reject(error);
            } else {
                prom.resolve(token);
            }
        });
        
        this.failedQueue = [];
    }

    /**
     * Refresh authentication token
     */
    async refreshToken() {
        const refreshToken = localStorage.getItem('coredesk_refresh_token');
        
        if (!refreshToken) {
            console.warn('[ApiClient] No refresh token available - API refresh tokens not implemented yet');
            throw new Error('No refresh token available');
        }

        try {
            const response = await this.client.post('/auth/refresh-token', {
                refreshToken
            });

            const { token, refreshToken: newRefreshToken } = response.data;
            
            // CRITICAL: Check for persistent logout protection before storing tokens
            const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
            const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
            const now = Date.now();
            
            // If logout was forced within the last 10 seconds, refuse to store tokens
            if (persistentLogoutFlag === 'true' || (logoutTimestamp && (now - parseInt(logoutTimestamp)) < 10000)) {
                console.log('ApiClient.refreshToken: BLOCKED - Persistent logout protection active, refusing to store tokens');
                throw new Error('Token storage blocked during logout protection period');
            }
            
            // CRITICAL: Don't store tokens if logout is in progress
            if (window._isLoggingOut) {
                console.log('ApiClient.refreshToken: BLOCKED - logout in progress, refusing to store tokens');
                throw new Error('Token storage blocked during logout');
            }
            
            // Store new tokens using TokenManager (with built-in protection) instead of direct localStorage
            if (window.tokenManager) {
                console.log('ApiClient.refreshToken: Using TokenManager to store tokens');
                window.tokenManager.storeTokens({
                    token,
                    refreshToken: newRefreshToken,
                    expiresIn: response.data.expiresIn
                });
            } else {
                // Fallback to direct storage only if TokenManager unavailable (but still with protection)
                console.warn('ApiClient.refreshToken: TokenManager unavailable, using fallback storage');
                localStorage.setItem('coredesk_token', token);
                if (newRefreshToken) {
                    localStorage.setItem('coredesk_refresh_token', newRefreshToken);
                }
                
                // Update token expiry
                if (response.data.expiresIn) {
                    const expiryTime = new Date(Date.now() + (response.data.expiresIn * 1000));
                    localStorage.setItem('coredesk_token_expiry', expiryTime.toISOString());
                }
            }

            return token;
            
        } catch (error) {
            console.error('Token refresh failed:', error);
            throw error;
        }
    }

    /**
     * Get current authentication token
     */
    getToken() {
        // Try to use TokenManager first (handles encrypted storage)
        if (window.tokenManager && typeof window.tokenManager.getToken === 'function') {
            const token = window.tokenManager.getToken();
            if (token) {
                console.log('[ApiClient] Retrieved token from TokenManager');
                return token;
            }
        }
        
        // Fallback to direct localStorage access (for backward compatibility)
        const directToken = localStorage.getItem('coredesk_token');
        if (directToken) {
            console.log('[ApiClient] Retrieved token from direct localStorage');
            return directToken;
        }
        
        console.warn('[ApiClient] No token found in TokenManager or localStorage');
        return null;
    }

    /**
     * Clear authentication data
     */
    clearAuthData() {
        localStorage.removeItem('coredesk_token');
        localStorage.removeItem('coredesk_refresh_token');
        localStorage.removeItem('coredesk_token_expiry');
        localStorage.removeItem('coredesk_user');
    }

    /**
     * Redirect to login
     */
    redirectToLogin() {
        // Emit logout event
        window.dispatchEvent(new CustomEvent('coredesk:auth:logout', {
            detail: { reason: 'token_expired' }
        }));
    }

    /**
     * Handle API errors
     */
    handleError(error) {
        const errorResponse = {
            message: 'An unexpected error occurred',
            code: 'UNKNOWN_ERROR',
            status: null,
            data: null
        };

        if (error.response) {
            // Server responded with error
            errorResponse.status = error.response.status;
            errorResponse.data = error.response.data;
            errorResponse.message = error.response.data?.message || error.message;
            errorResponse.code = error.response.data?.code || `HTTP_${error.response.status}`;
            
        } else if (error.request) {
            // Request made but no response
            errorResponse.message = 'No response from server';
            errorResponse.code = 'NETWORK_ERROR';
            
        } else {
            // Request setup error
            errorResponse.message = error.message;
            errorResponse.code = 'REQUEST_ERROR';
        }

        // Log error in development (browser-safe environment check)
        const isDevelopment = window.CoreDeskAuth?.environment === 'development' || 
                            window.location.hostname === 'localhost' || 
                            window.location.hostname === '127.0.0.1';
                            
        if (isDevelopment) {
            console.error('API Error:', errorResponse);
        }

        return errorResponse;
    }

    /**
     * Make GET request
     */
    async get(url, config = {}) {
        return this.client.get(url, config);
    }

    /**
     * Make POST request
     */
    async post(url, data = {}, config = {}) {
        if (!this.client) {
            throw new Error('ApiClient not properly initialized - this.client is null');
        }
        
        return this.client.post(url, data, config);
    }

    /**
     * Make PUT request
     */
    async put(url, data = {}, config = {}) {
        return this.client.put(url, data, config);
    }

    /**
     * Make PATCH request
     */
    async patch(url, data = {}, config = {}) {
        return this.client.patch(url, data, config);
    }

    /**
     * Make DELETE request
     */
    async delete(url, config = {}) {
        return this.client.delete(url, config);
    }

    /**
     * Get axios instance for advanced usage
     */
    getInstance() {
        return this.client;
    }

    /**
     * Get current configuration
     */
    getConfig() {
        return {
            baseURL: this.baseURL,
            timeout: this.client.defaults.timeout,
            headers: this.client.defaults.headers
        };
    }

    /**
     * Update base URL
     */
    setBaseURL(url) {
        this.baseURL = url;
        this.client.defaults.baseURL = url;
    }

    /**
     * Add custom header
     */
    setHeader(key, value) {
        this.client.defaults.headers.common[key] = value;
    }

    /**
     * Remove custom header
     */
    removeHeader(key) {
        delete this.client.defaults.headers.common[key];
    }
}

// Create singleton instance
const apiClient = new ApiClient();

// Make available globally for browser environment
if (typeof window !== 'undefined') {
    window.ApiClient = ApiClient;
    window.apiClient = apiClient;
    
    // If axios is not available yet, try to reinitialize when DOM is ready
    if (!window.axios) {
        console.log('[ApiClient] Axios not available yet, will retry when DOM is ready');
        document.addEventListener('DOMContentLoaded', () => {
            console.log('[ApiClient] DOM ready, retrying initialization...');
            apiClient.initialize();
        });
    }
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = apiClient;
}