/**
 * AuthApiService.js
 * Authentication API service for CoreDesk
 * Handles all authentication-related API calls
 */

// ApiClient will be available globally
// const apiClient = require('./ApiClient');

class AuthApiService {
    constructor() {
        // Initialize apiClient - it may not be available immediately if scripts load in parallel
        this.apiClient = null;
        this.endpoints = {
            login: '/auth/login',
            register: '/auth/register',
            loginWithLicense: '/auth/login-with-license',
            logout: '/auth/logout',
            refreshToken: '/auth/refresh-token',
            profile: '/users/me',
            verifyToken: '/auth/verify-token',
            changePassword: '/auth/change-password',
            requestPasswordReset: '/auth/request-password-reset',
            resetPassword: '/auth/reset-password',
            firebaseToken: '/auth/firebase-token'
        };
        
        // Try to initialize immediately if available
        this.initializeApiClient();
    }
    
    /**
     * Initialize or reinitialize the API client
     */
    initializeApiClient() {
        if (typeof window !== 'undefined' && window.apiClient) {
            this.apiClient = window.apiClient;
            console.log('[AuthApiService] API client initialized successfully');
        } else {
            console.warn('[AuthApiService] API client not yet available - will retry on method calls');
        }
    }

    /**
     * Login with email and password
     * @param {string} email - User email
     * @param {string} password - User password
     * @param {boolean} rememberMe - Remember user session
     * @returns {Promise<Object>} Authentication response
     */
    async login(email, password, rememberMe = false) {
        try {
            // Try to initialize API client if not available
            if (!this.apiClient) {
                this.initializeApiClient();
            }
            
            // Ensure API client is available
            if (!this.apiClient) {
                throw new Error('API client not initialized. Please check your connection to the backend server.');
            }

            const response = await this.apiClient.post(this.endpoints.login, {
                email,
                password,
                rememberMe
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('API login failed:', error.message);
            
            // Return actual API error without mock fallback
            let errorMessage = 'Error de conexión con el servidor. Por favor verifica tu conexión e intenta nuevamente.';
            
            if (error.response && error.response.data && error.response.data.error) {
                errorMessage = error.response.data.error.message || error.response.data.error;
            } else if (error.message) {
                errorMessage = error.message;
            }
            
            return {
                success: false,
                error: errorMessage,
                code: error.response?.data?.error?.code || 'API_CONNECTION_ERROR'
            };
        }
    }

    /**
     * Register a new user account
     * @param {Object} userData - User registration data
     * @param {string} userData.email - User email
     * @param {string} userData.password - User password
     * @param {string} userData.firstName - User first name
     * @param {string} userData.lastName - User last name
     * @param {string} [userData.company] - User company (optional)
     * @param {string} [userData.phone] - User phone (optional)
     * @returns {Promise<Object>} Registration response
     */
    async register(userData) {
        try {
            // Try to initialize API client if not available
            if (!this.apiClient) {
                this.initializeApiClient();
            }
            
            // Ensure API client is available
            if (!this.apiClient) {
                throw new Error('API client not initialized. Please check your connection to the backend server.');
            }

            const response = await this.apiClient.post(this.endpoints.register, userData);

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('API registration failed:', error.message);
            
            // Return actual API error without mock fallback
            let errorMessage = 'Error de conexión con el servidor. Por favor verifica tu conexión e intenta nuevamente.';
            
            if (error.response && error.response.data && error.response.data.error) {
                errorMessage = error.response.data.error.message || error.response.data.error;
            } else if (error.message) {
                errorMessage = error.message;
            }
            
            return {
                success: false,
                error: errorMessage,
                code: error.response?.data?.error?.code || 'API_CONNECTION_ERROR'
            };
        }
    }


    /**
     * Login with license key
     * @param {string} licenseKey - License key
     * @param {string} deviceFingerprint - Device fingerprint
     * @returns {Promise<Object>} Authentication response
     */
    async loginWithLicense(licenseKey, deviceFingerprint) {
        try {
            // Ensure API client is available
            if (!this.apiClient) {
                throw new Error('API client not initialized. Please check your connection to the backend server.');
            }

            const response = await this.apiClient.post(this.endpoints.loginWithLicense, {
                licenseKey,
                machineId: deviceFingerprint
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('API license login failed:', error.message);
            
            let errorMessage = 'Error de conexión con el servidor. Por favor verifica tu conexión e intenta nuevamente.';
            
            if (error.response && error.response.data && error.response.data.error) {
                errorMessage = error.response.data.error.message || error.response.data.error;
            } else if (error.message) {
                errorMessage = error.message;
            }
            
            return {
                success: false,
                error: errorMessage,
                code: error.response?.data?.error?.code || 'API_CONNECTION_ERROR'
            };
        }
    }

    /**
     * Logout current user
     * @returns {Promise<Object>} Logout response
     */
    async logout() {
        try {
            // Try to logout from server if API client is available
            if (this.apiClient) {
                const response = await this.apiClient.post(this.endpoints.logout);
                return {
                    success: true,
                    data: response.data
                };
            } else {
                // If no API client, just return success to clear local data
                return { success: true };
            }
        } catch (error) {
            // Even if logout fails on server, we should clear local data
            console.warn('Server logout failed, but clearing local data:', error.message);
            return {
                success: true,
                error: error.message,
                code: error.code
            };
        }
    }

    /**
     * Refresh authentication token
     * @param {string} refreshToken - Refresh token
     * @returns {Promise<Object>} Token refresh response
     */
    async refreshToken(refreshToken) {
        try {
            // Ensure API client is available
            if (!this.apiClient) {
                throw new Error('API client not initialized. Please check your connection to the backend server.');
            }

            const response = await this.apiClient.post(this.endpoints.refreshToken, {
                refreshToken
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('API token refresh failed:', error.message);
            
            let errorMessage = 'Error al renovar token. Por favor inicia sesión nuevamente.';
            
            if (error.response && error.response.data && error.response.data.error) {
                errorMessage = error.response.data.error.message || error.response.data.error;
            } else if (error.message) {
                errorMessage = error.message;
            }
            
            return {
                success: false,
                error: errorMessage,
                code: error.response?.data?.error?.code || 'TOKEN_REFRESH_ERROR'
            };
        }
    }

    /**
     * Get current user profile
     * @returns {Promise<Object>} User profile response
     */
    async getProfile() {
        try {
            // Ensure API client is available
            if (!this.apiClient) {
                throw new Error('API client not initialized. Please check your connection to the backend server.');
            }

            const response = await this.apiClient.get(this.endpoints.profile);

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('API get profile failed:', error.message);
            
            let errorMessage = 'Error al obtener perfil de usuario.';
            
            if (error.response && error.response.data && error.response.data.error) {
                errorMessage = error.response.data.error.message || error.response.data.error;
            } else if (error.message) {
                errorMessage = error.message;
            }

            return {
                success: false,
                error: errorMessage,
                code: error.response?.data?.error?.code || 'PROFILE_FETCH_ERROR'
            };
        }
    }

    /**
     * Update user profile
     * @param {Object} profileData - Profile update data
     * @returns {Promise<Object>} Update response
     */
    async updateProfile(profileData) {
        try {
            const response = await this.apiClient.put(this.endpoints.profile, profileData);

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to update profile',
                code: error.code
            };
        }
    }

    /**
     * Verify authentication token
     * @param {string} token - Token to verify
     * @returns {Promise<Object>} Verification response
     */
    async verifyToken(token) {
        try {
            const response = await this.apiClient.post(this.endpoints.verifyToken, {
                token
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Token verification failed',
                code: error.code
            };
        }
    }

    /**
     * Change user password
     * @param {string} currentPassword - Current password
     * @param {string} newPassword - New password
     * @returns {Promise<Object>} Password change response
     */
    async changePassword(currentPassword, newPassword) {
        try {
            const response = await this.apiClient.post(this.endpoints.changePassword, {
                currentPassword,
                newPassword
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to change password',
                code: error.code
            };
        }
    }

    /**
     * Request password reset
     * @param {string} email - User email
     * @returns {Promise<Object>} Password reset request response
     */
    async requestPasswordReset(email) {
        try {
            const response = await this.apiClient.post(this.endpoints.requestPasswordReset, {
                email
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to request password reset',
                code: error.code
            };
        }
    }

    /**
     * Reset password with token
     * @param {string} token - Reset token
     * @param {string} newPassword - New password
     * @returns {Promise<Object>} Password reset response
     */
    async resetPassword(token, newPassword) {
        try {
            const response = await this.apiClient.post(this.endpoints.resetPassword, {
                token,
                newPassword
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to reset password',
                code: error.code
            };
        }
    }

    /**
     * Get Firebase custom token
     * @returns {Promise<Object>} Firebase token response
     */
    async getFirebaseToken() {
        try {
            const response = await this.apiClient.get(this.endpoints.firebaseToken);

            return {
                success: true,
                token: response.data.token,
                expiresIn: response.data.expiresIn
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Failed to get Firebase token',
                code: error.code
            };
        }
    }

    /**
     * Validate authentication response
     * @param {Object} response - API response
     * @returns {boolean} Validation result
     */
    validateAuthResponse(response) {
        return response?.data?.token && response?.data?.user;
    }

    /**
     * Extract auth data from response
     * @param {Object} response - API response
     * @returns {Object} Auth data
     */
    extractAuthData(response) {
        const { token, refreshToken, user, expiresIn, license } = response.data;
        
        return {
            token,
            refreshToken: refreshToken || null, // Handle missing refreshToken gracefully
            user,
            expiresIn,
            license,
            expiryTime: expiresIn ? new Date(Date.now() + (expiresIn * 1000)) : null
        };
    }

    /**
     * Initialize dependencies after all components are loaded
     */
    initializeDependencies() {
        this.initializeApiClient();
    }

}

// Create singleton instance
const authApiService = new AuthApiService();

// Make available globally for browser environment
if (typeof window !== 'undefined') {
    window.AuthApiService = AuthApiService;
    window.authApiService = authApiService;
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = authApiService;
}