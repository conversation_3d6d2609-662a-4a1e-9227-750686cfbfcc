/**
 * FirebaseConnector.js
 * Firebase integration layer for CoreDesk data synchronization
 * Handles Firestore operations, authentication, and real-time updates
 */

const { 
    auth, 
    firestore, 
    storage,
    isFirebaseInitialized,
    getFirebaseAuth,
    getFirebaseFirestore,
    getFirebaseStorage
} = window.firebaseConfig || {};

// Firebase auth methods will be available globally
// const { 
//     signInWithEmailAndPassword,
//     signInWithCustomToken,
//     signOut,
//     onAuthStateChanged
// } = require('firebase/auth');

// Firebase Firestore methods will be available globally
// const {
//     doc,
//     collection,
//     getDoc,
//     setDoc,
//     updateDoc,
//     deleteDoc,
//     query,
//     where,
//     orderBy,
//     limit,
//     getDocs,
//     onSnapshot,
//     serverTimestamp,
//     writeBatch,
//     enableNetwork,
//     disableNetwork
// } = require('firebase/firestore');

// Firebase Storage methods will be available globally
// const {
//     ref,
//     uploadBytes,
//     uploadBytesResumable,
//     getDownloadURL,
//     deleteObject,
//     listAll
// } = require('firebase/storage');

class FirebaseConnector {
    constructor(config) {
        this.config = config;
        this.app = null;
        this.firestore = null;
        this.auth = null;
        this.storage = null;
        this.isInitialized = false;
        this.isAuthenticated = false;
        this.currentUser = null;
        
        this.collections = {
            users: 'coredesk-users',
            cases: 'coredesk-cases',
            documents: 'coredesk-documents',
            sync_metadata: 'coredesk-sync-metadata',
            conflicts: 'coredesk-conflicts',
            licenses: 'coredesk-licenses',
            activations: 'coredesk-activations'
        };
        
        this.listeners = new Map();
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
        
        this.initialize();
    }

    /**
     * Initialize Firebase connection
     */
    async initialize() {
        console.log('FirebaseConnector', '[FirebaseConnector] Initializing Firebase...', );
        
        try {
            if (!isFirebaseInitialized()) {
                throw new Error('Firebase not initialized in config');
            }
            
            // Get Firebase services
            this.auth = getFirebaseAuth();
            this.firestore = getFirebaseFirestore();
            this.storage = getFirebaseStorage();
            
            // Setup auth state listener
            this.setupAuthListener();
            
            this.isInitialized = true;
            
            console.log('FirebaseConnector', '[FirebaseConnector] Firebase initialized successfully', );
            
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Firebase initialization failed:', error);
            throw error;
        }
    }

    /**
     * Setup authentication state listener
     * @private
     */
    setupAuthListener() {
        onAuthStateChanged(this.auth, (user) => {
            if (user) {
                this.currentUser = {
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName,
                    lastLogin: user.metadata.lastSignInTime
                };
                this.isAuthenticated = true;
                console.log('FirebaseConnector', '[FirebaseConnector] User authenticated:', user.uid);
            } else {
                this.currentUser = null;
                this.isAuthenticated = false;
                console.log('FirebaseConnector', '[FirebaseConnector] User signed out');
            }
        });
    }

    /**
     * Authenticate with Firebase using email/password
     * @param {Object} credentials - Authentication credentials
     * @returns {Promise<Object>} Authentication result
     */
    async authenticate(credentials) {
        if (!this.isInitialized) {
            throw new Error('Firebase not initialized');
        }
        
        console.log('FirebaseConnector', '[FirebaseConnector] Authenticating with Firebase...', );
        
        try {
            let userCredential;
            
            if (credentials.customToken) {
                // Sign in with custom token from backend
                userCredential = await signInWithCustomToken(this.auth, credentials.customToken);
            } else if (credentials.email && credentials.password) {
                // Sign in with email/password
                userCredential = await signInWithEmailAndPassword(
                    this.auth, 
                    credentials.email, 
                    credentials.password
                );
            } else {
                throw new Error('Invalid credentials provided');
            }
            
            const user = userCredential.user;
            
            this.currentUser = {
                uid: user.uid,
                email: user.email,
                displayName: user.displayName || 'CoreDesk User',
                lastLogin: new Date().toISOString()
            };
            
            this.isAuthenticated = true;
            
            console.log('FirebaseConnector', '[FirebaseConnector] Authentication successful:', this.currentUser.uid);
            
            return {
                success: true,
                user: this.currentUser
            };
            
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Authentication failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Sign out from Firebase
     */
    async signOut() {
        if (!this.isAuthenticated) {
            return;
        }
        
        console.log('FirebaseConnector', '[FirebaseConnector] Signing out...', );
        
        try {
            // Clear listeners
            this.removeAllListeners();
            
            // Sign out from Firebase
            await signOut(this.auth);
            
            // Clear user data
            this.currentUser = null;
            this.isAuthenticated = false;
            
            console.log('FirebaseConnector', '[FirebaseConnector] Signed out successfully', );
            
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Sign out failed:', error);
        }
    }

    /**
     * Save document to Firestore
     * @param {string} collectionName - Collection name
     * @param {string} docId - Document ID
     * @param {Object} data - Document data
     * @returns {Promise<Object>} Save result
     */
    async saveDocument(collectionName, docId, data) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        console.log("Component", `[FirebaseConnector] Saving document: ${collectionName}/${docId}`);
        
        try {
            const documentData = {
                ...data,
                userId: this.currentUser.uid,
                updatedAt: serverTimestamp(),
                version: (data.version || 0) + 1
            };
            
            // Create document reference
            const docRef = doc(this.firestore, this.collections[collectionName] || collectionName, docId);
            
            // Save to Firestore
            await setDoc(docRef, documentData);
            
            console.log("Component", `[FirebaseConnector] Document saved: ${collectionName}/${docId}`);
            
            return {
                success: true,
                docId,
                version: documentData.version
            };
            
        } catch (error) {
            console.error(`[FirebaseConnector] Error saving document ${collectionName}/${docId}:`, error);
            throw error;
        }
    }

    /**
     * Get document from Firestore
     * @param {string} collectionName - Collection name
     * @param {string} docId - Document ID
     * @returns {Promise<Object>} Document data
     */
    async getDocument(collectionName, docId) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        console.log("Component", `[FirebaseConnector] Getting document: ${collectionName}/${docId}`);
        
        try {
            // Create document reference
            const docRef = doc(this.firestore, this.collections[collectionName] || collectionName, docId);
            
            // Get from Firestore
            const docSnap = await getDoc(docRef);
            
            if (docSnap.exists()) {
                console.log("Component", `[FirebaseConnector] Document found: ${collectionName}/${docId}`);
                return {
                    exists: true,
                    data: docSnap.data(),
                    id: docId
                };
            } else {
                console.log("Component", `[FirebaseConnector] Document not found: ${collectionName}/${docId}`);
                return {
                    exists: false,
                    data: null,
                    id: docId
                };
            }
            
        } catch (error) {
            console.error(`[FirebaseConnector] Error getting document ${collectionName}/${docId}:`, error);
            throw error;
        }
    }

    /**
     * Delete document from Firestore
     * @param {string} collectionName - Collection name
     * @param {string} docId - Document ID
     * @returns {Promise<Object>} Delete result
     */
    async deleteDocument(collectionName, docId) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        console.log("Component", `[FirebaseConnector] Deleting document: ${collectionName}/${docId}`);
        
        try {
            // Create document reference
            const docRef = doc(this.firestore, this.collections[collectionName] || collectionName, docId);
            
            // Delete from Firestore
            await deleteDoc(docRef);
            
            console.log("Component", `[FirebaseConnector] Document deleted: ${collectionName}/${docId}`);
            
            return {
                success: true,
                docId
            };
            
        } catch (error) {
            console.error(`[FirebaseConnector] Error deleting document ${collectionName}/${docId}:`, error);
            throw error;
        }
    }

    /**
     * Query documents from Firestore
     * @param {string} collectionName - Collection name
     * @param {Object} queryOptions - Query options
     * @returns {Promise<Array>} Query results
     */
    async queryDocuments(collectionName, queryOptions = {}) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        console.log(`[FirebaseConnector] Querying collection: ${collectionName}`, queryOptions);
        
        try {
            const collectionRef = collection(this.firestore, this.collections[collectionName] || collectionName);
            let q = query(collectionRef);
            
            // Apply where clauses
            if (queryOptions.where) {
                for (const condition of queryOptions.where) {
                    q = query(q, where(...condition));
                }
            }
            
            // Apply orderBy
            if (queryOptions.orderBy) {
                for (const order of queryOptions.orderBy) {
                    q = query(q, orderBy(...order));
                }
            }
            
            // Apply limit
            if (queryOptions.limit) {
                q = query(q, limit(queryOptions.limit));
            }
            
            // Execute query
            const querySnapshot = await getDocs(q);
            const results = [];
            
            querySnapshot.forEach((doc) => {
                results.push({
                    id: doc.id,
                    data: doc.data()
                });
            });
            
            console.log("Component", `[FirebaseConnector] Query returned ${results.length} documents`);
            
            return results;
            
        } catch (error) {
            console.error(`[FirebaseConnector] Error querying collection ${collectionName}:`, error);
            throw error;
        }
    }

    /**
     * Get changes since timestamp
     * @param {string} collectionName - Collection name
     * @param {Date} since - Timestamp to get changes since
     * @returns {Promise<Array>} Changes
     */
    async getChangesSince(collectionName, since) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        console.log("Component", `[FirebaseConnector] Getting changes since: ${since.toISOString()}`);
        
        try {
            const queryOptions = {
                where: [['updatedAt', '>', since]],
                orderBy: [['updatedAt', 'asc']]
            };
            
            const changes = await this.queryDocuments(collectionName, queryOptions);
            
            console.log("Component", `[FirebaseConnector] Found ${changes.length} changes since ${since.toISOString()}`);
            
            return changes;
            
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Error getting changes:', error);
            throw error;
        }
    }

    /**
     * Set up real-time listener
     * @param {string} collectionName - Collection name
     * @param {Object} queryOptions - Query options
     * @param {Function} callback - Change callback
     * @returns {string} Listener ID
     */
    setupRealtimeListener(collectionName, queryOptions, callback) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        const listenerId = `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        console.log("Component", `[FirebaseConnector] Setting up real-time listener: ${listenerId}`);
        
        try {
            const collectionRef = collection(this.firestore, this.collections[collectionName] || collectionName);
            let q = query(collectionRef);
            
            // Apply query options (same as queryDocuments)
            if (queryOptions.where) {
                for (const condition of queryOptions.where) {
                    q = query(q, where(...condition));
                }
            }
            
            if (queryOptions.orderBy) {
                for (const order of queryOptions.orderBy) {
                    q = query(q, orderBy(...order));
                }
            }
            
            if (queryOptions.limit) {
                q = query(q, limit(queryOptions.limit));
            }
            
            // Setup real-time listener
            const unsubscribe = onSnapshot(q, 
                (snapshot) => {
                    snapshot.docChanges().forEach((change) => {
                        callback({
                            type: change.type, // 'added', 'modified', 'removed'
                            doc: {
                                id: change.doc.id,
                                data: change.doc.data()
                            }
                        });
                    });
                },
                (error) => {
                    console.error('FirebaseConnector', '[FirebaseConnector] Listener error:', error);
                    callback({ type: 'error', error });
                }
            );
            
            // Store listener info
            this.listeners.set(listenerId, {
                id: listenerId,
                collectionName,
                queryOptions,
                callback,
                unsubscribe,
                active: true,
                createdAt: new Date().toISOString()
            });
            
            return listenerId;
            
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Error setting up listener:', error);
            throw error;
        }
    }

    /**
     * Remove real-time listener
     * @param {string} listenerId - Listener ID
     */
    removeRealtimeListener(listenerId) {
        const listener = this.listeners.get(listenerId);
        
        if (listener) {
            // Call the unsubscribe function
            if (listener.unsubscribe) {
                listener.unsubscribe();
            }
            
            listener.active = false;
            this.listeners.delete(listenerId);
            
            console.log("Component", `[FirebaseConnector] Removed real-time listener: ${listenerId}`);
        }
    }

    /**
     * Remove all real-time listeners
     */
    removeAllListeners() {
        console.log("Component", `[FirebaseConnector] Removing ${this.listeners.size} listeners`);
        
        for (const [listenerId] of this.listeners) {
            this.removeRealtimeListener(listenerId);
        }
    }

    /**
     * Batch write operations
     * @param {Array} operations - Array of operations
     * @returns {Promise<Object>} Batch result
     */
    async batchWrite(operations) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        console.log("Component", `[FirebaseConnector] Executing batch write with ${operations.length} operations`);
        
        try {
            const batch = writeBatch(this.firestore);
            
            for (const operation of operations) {
                const collectionName = this.collections[operation.collection] || operation.collection;
                const docRef = doc(this.firestore, collectionName, operation.docId);
                
                switch (operation.type) {
                    case 'set':
                        batch.set(docRef, {
                            ...operation.data,
                            userId: this.currentUser.uid,
                            updatedAt: serverTimestamp()
                        });
                        break;
                        
                    case 'update':
                        batch.update(docRef, {
                            ...operation.data,
                            updatedAt: serverTimestamp()
                        });
                        break;
                        
                    case 'delete':
                        batch.delete(docRef);
                        break;
                        
                    default:
                        throw new Error(`Unknown operation type: ${operation.type}`);
                }
            }
            
            // Commit the batch
            await batch.commit();
            
            console.log("Component", `[FirebaseConnector] Batch write completed: ${operations.length} operations`);
            
            return {
                success: true,
                operationCount: operations.length
            };
            
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Batch write failed:', error);
            throw error;
        }
    }

    /**
     * Update document in Firestore
     * @param {string} collectionName - Collection name
     * @param {string} docId - Document ID
     * @param {Object} data - Update data
     * @returns {Promise<Object>} Update result
     */
    async updateDocument(collectionName, docId, data) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        console.log("Component", `[FirebaseConnector] Updating document: ${collectionName}/${docId}`);
        
        try {
            const docRef = doc(this.firestore, this.collections[collectionName] || collectionName, docId);
            
            const updateData = {
                ...data,
                updatedAt: serverTimestamp()
            };
            
            await updateDoc(docRef, updateData);
            
            console.log("Component", `[FirebaseConnector] Document updated: ${collectionName}/${docId}`);
            
            return {
                success: true,
                docId
            };
            
        } catch (error) {
            console.error(`[FirebaseConnector] Error updating document ${collectionName}/${docId}:`, error);
            throw error;
        }
    }

    /**
     * Upload file to Firebase Storage
     * @param {string} path - Storage path
     * @param {Blob|File} file - File to upload
     * @param {Function} onProgress - Progress callback
     * @returns {Promise<Object>} Upload result
     */
    async uploadFile(path, file, onProgress) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        console.log("Component", `[FirebaseConnector] Uploading file to: ${path}`);
        
        try {
            const storageRef = ref(this.storage, path);
            
            // Create upload task
            const uploadTask = uploadBytesResumable(storageRef, file);
            
            // Return promise that resolves when upload completes
            return new Promise((resolve, reject) => {
                uploadTask.on('state_changed',
                    (snapshot) => {
                        // Progress callback
                        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                        if (onProgress) {
                            onProgress(progress, snapshot);
                        }
                    },
                    (error) => {
                        // Error callback
                        console.error('FirebaseConnector', '[FirebaseConnector] Upload error:', error);
                        reject(error);
                    },
                    async () => {
                        // Complete callback
                        try {
                            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
                            console.log("Component", `[FirebaseConnector] File uploaded successfully: ${path}`);
                            resolve({
                                success: true,
                                downloadURL,
                                path,
                                size: uploadTask.snapshot.totalBytes
                            });
                        } catch (error) {
                            reject(error);
                        }
                    }
                );
            });
            
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Upload failed:', error);
            throw error;
        }
    }

    /**
     * Download file from Firebase Storage
     * @param {string} path - Storage path
     * @returns {Promise<string>} Download URL
     */
    async getFileDownloadURL(path) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        try {
            const storageRef = ref(this.storage, path);
            const url = await getDownloadURL(storageRef);
            return url;
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Error getting download URL:', error);
            throw error;
        }
    }

    /**
     * Delete file from Firebase Storage
     * @param {string} path - Storage path
     * @returns {Promise<Object>} Delete result
     */
    async deleteFile(path) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated');
        }
        
        try {
            const storageRef = ref(this.storage, path);
            await deleteObject(storageRef);
            
            console.log("Component", `[FirebaseConnector] File deleted: ${path}`);
            
            return {
                success: true,
                path
            };
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Error deleting file:', error);
            throw error;
        }
    }

    /**
     * Enable/disable network for offline testing
     * @param {boolean} enabled - Network state
     */
    async setNetworkEnabled(enabled) {
        try {
            if (enabled) {
                await enableNetwork(this.firestore);
                console.log('FirebaseConnector', '[FirebaseConnector] Network enabled');
            } else {
                await disableNetwork(this.firestore);
                console.log('FirebaseConnector', '[FirebaseConnector] Network disabled');
            }
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Error setting network state:', error);
        }
    }

    /**
     * Check if document exists
     * @param {string} collectionName - Collection name
     * @param {string} docId - Document ID
     * @returns {Promise<boolean>} Existence status
     */
    async documentExists(collectionName, docId) {
        try {
            const doc = await this.getDocument(collectionName, docId);
            return doc.exists;
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Error checking document existence:', error);
            return false;
        }
    }

    /**
     * Get collection size
     * @param {string} collectionName - Collection name
     * @returns {Promise<number>} Collection size
     */
    async getCollectionSize(collectionName) {
        try {
            const docs = await this.queryDocuments(collectionName, { limit: 1000 });
            return docs.length;
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Error getting collection size:', error);
            return 0;
        }
    }

    /**
     * Get connection status
     * @returns {Object} Connection status
     */
    getConnectionStatus() {
        return {
            isInitialized: this.isInitialized,
            isAuthenticated: this.isAuthenticated,
            currentUser: this.currentUser,
            activeListeners: this.listeners.size,
            config: {
                projectId: this.config?.projectId,
                apiKey: this.config?.apiKey ? '***' : null
            }
        };
    }

    /**
     * Test connection
     * @returns {Promise<Object>} Test result
     */
    async testConnection() {
        console.log('FirebaseConnector', '[FirebaseConnector] Testing connection...', );
        
        try {
            if (!this.isInitialized) {
                throw new Error('Firebase not initialized');
            }
            
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            // Test basic operation
            const testDoc = await this.getDocument('test', 'connection-test');
            
            return {
                success: true,
                message: 'Connection test successful',
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('FirebaseConnector', '[FirebaseConnector] Connection test failed:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Get Firebase statistics
     * @returns {Object} Firebase statistics
     */
    getStats() {
        return {
            initialized: this.isInitialized,
            authenticated: this.isAuthenticated,
            activeListeners: this.listeners.size,
            collections: Object.keys(this.collections).length,
            retryAttempts: this.retryAttempts,
            retryDelay: this.retryDelay
        };
    }
}

console.log('FirebaseConnector', '[FirebaseConnector] Class defined successfully', );