/**
 * ConfigurationPanel.js
 * Comprehensive configuration panel for CoreDesk Framework
 * Manages all system settings including appearance, sync, modules, and advanced options
 */

class ConfigurationPanel {
    constructor() {
        this.panel = null;
        this.isVisible = false;
        this.currentSection = 'general';
        this.unsavedChanges = false;
        
        this.settings = {
            general: {},
            appearance: {},
            sync: {},
            modules: {},
            updates: {},
            advanced: {},
            license: {}
        };
        
        this.defaultSettings = {
            general: {
                language: 'es',
                country: 'US',
                timezone: 'auto',
                startup_module: null,
                auto_save: true,
                auto_save_interval: 30,
                coredesk_directory: null // Will be set to default CoreDesk path
            },
            appearance: {
                theme: 'dark',
                font_size: 14,
                font_family: 'System',
                ui_scale: 100,
                show_welcome_screen: true,
                compact_mode: false,
                animations_enabled: true
            },
            sync: {
                enabled: false,
                cloud_enabled: false,
                interval_ms: 30000,
                conflict_strategy: 'client-wins',
                offline_queue: true,
                realtime_enabled: false,
                firebase_config: null
            },
            modules: {
                lexflow_enabled: true,
                protocolx_enabled: true,
                auditpro_enabled: false,
                finsync_enabled: false
            },
            advanced: {
                debug_mode: false,
                performance_monitoring: true,
                crash_reporting: true,
                beta_features: false,
                max_tabs: 20,
                cache_size_mb: 100
            },
            license: {
                auto_check: true,
                check_interval_hours: 24,
                offline_grace_days: 7
            },
            updates: {
                auto_check: true,
                check_interval_hours: 4,
                auto_download: true,
                auto_install: false,
                channel: 'stable',
                environment: 'production',
                notify_available: true,
                notify_downloaded: true
            }
        };
        
        this.sections = [
            { id: 'general', title: 'General', icon: '⚙️' },
            { id: 'appearance', title: 'Apariencia', icon: '🎨' },
            { id: 'sync', title: 'Sincronización', icon: '🔄' },
            { id: 'modules', title: 'Módulos', icon: '📦' },
            { id: 'updates', title: 'Actualizaciones', icon: '🔄' },
            { id: 'advanced', title: 'Avanzado', icon: '🔧' },
            { id: 'license', title: 'Licencia', icon: '🔑' }
        ];
        
        this.initialize();
    }

    /**
     * Initialize the configuration panel
     */
    async initialize() {
        console.log('Config', '[ConfigurationPanel] Initializing...', );
        
        try {
            // Load current settings
            await this.loadSettings();
            
            // Initialize CoreDesk directory setting
            await this.initializeCoreDeskDirectorySetting();
            
            // Create panel UI
            this.createPanel();
            
            // Set up event listeners
            this.setupEventListeners();
            
            console.log('Config', '[ConfigurationPanel] Initialized successfully', );
            
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Initialization failed:', error);
        }
    }

    /**
     * Load settings from database and localStorage
     * @private
     */
    async loadSettings() {
        try {
            console.log('Config', '[ConfigurationPanel] Loading settings...', );
            
            // Initialize with defaults
            this.settings = JSON.parse(JSON.stringify(this.defaultSettings));
            
            // Load from database if available
            if (window.electronAPI && window.electronAPI.executeQuery) {
                const dbSettings = await window.electronAPI.executeQuery(
                    'SELECT category, key, value FROM settings'
                );
                
                // Apply database settings
                dbSettings.forEach(setting => {
                    if (this.settings[setting.category]) {
                        try {
                            // Try to parse as JSON, fallback to string
                            let value = setting.value;
                            if (typeof value === 'string') {
                                try {
                                    value = JSON.parse(value);
                                } catch (e) {
                                    // Keep as string if not valid JSON
                                }
                            }
                            this.settings[setting.category][setting.key] = value;
                        } catch (error) {
                            console.warn('Config', '[ConfigurationPanel] Error parsing setting:', setting.key, error);
                        }
                    }
                });
            }
            
            // Load from localStorage for session settings
            const sessionSettings = localStorage.getItem('coredesk_session_settings');
            if (sessionSettings) {
                try {
                    const parsed = JSON.parse(sessionSettings);
                    Object.assign(this.settings, parsed);
                } catch (error) {
                    console.warn('Config', '[ConfigurationPanel] Error parsing session settings:', error);
                }
            }
            
            console.log('Config', '[ConfigurationPanel] Settings loaded:', this.settings);
            
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error loading settings:', error);
        }
    }

    /**
     * Create the configuration panel UI
     * @private
     */
    createPanel() {
        this.panel = document.createElement('div');
        this.panel.className = 'config-panel-container';
        this.panel.id = 'config-panel';
        this.panel.style.display = 'none';
        
        this.panel.innerHTML = `
            <div class="config-panel">
                <div class="config-panel-header">
                    <h2>Configuración de CoreDesk</h2>
                    <button class="config-panel-close" id="config-panel-close">×</button>
                </div>
                
                <div class="config-panel-body">
                    <!-- Sidebar Navigation -->
                    <div class="config-sidebar">
                        <nav class="config-nav">
                            ${this.sections.map(section => `
                                <button class="config-nav-item ${section.id === this.currentSection ? 'active' : ''}" 
                                        data-section="${section.id}">
                                    <span class="nav-icon">${section.icon}</span>
                                    <span class="nav-title">${section.title}</span>
                                </button>
                            `).join('')}
                        </nav>
                    </div>
                    
                    <!-- Content Area -->
                    <div class="config-content">
                        <div id="config-content-area" class="config-content-area">
                            <!-- Dynamic content will be inserted here -->
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="config-panel-footer">
                    <div class="config-footer-left">
                        <button class="btn btn-secondary" id="config-reset-section">Restaurar sección</button>
                        <button class="btn btn-danger" id="config-reset-all">Restaurar todo</button>
                    </div>
                    <div class="config-footer-right">
                        <button class="btn btn-secondary" id="config-cancel">Cancelar</button>
                        <button class="btn btn-primary" id="config-apply">Aplicar</button>
                    </div>
                </div>
            </div>
        `;
        
        // Add to document
        document.body.appendChild(this.panel);
        
        // Render initial section
        this.renderSection(this.currentSection);
    }
    
    /**
     * Setup event listeners
     * @private
     */
    setupEventListeners() {
        // Close panel
        const closeBtn = this.panel.querySelector('#config-panel-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Config', '[ConfigurationPanel] Close button clicked');
                this.hide();
            });
        } else {
            console.warn('Config', '[ConfigurationPanel] Close button not found');
        }
        
        // Cancel button
        const cancelBtn = this.panel.querySelector('#config-cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Config', '[ConfigurationPanel] Cancel button clicked');
                this.hide();
            });
        } else {
            console.warn('Config', '[ConfigurationPanel] Cancel button not found');
        }
        
        // Apply button
        this.panel.querySelector('#config-apply').addEventListener('click', () => {
            this.applySettings();
        });
        
        // Reset section button
        this.panel.querySelector('#config-reset-section').addEventListener('click', () => {
            this.resetSection();
        });
        
        // Reset all button
        this.panel.querySelector('#config-reset-all').addEventListener('click', () => {
            this.resetAllSettings();
        });
        
        // Navigation - ensure DOM is ready
        setTimeout(() => {
            this.panel.querySelectorAll('.config-nav-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const section = e.currentTarget.dataset.section;
                    console.log('Config', `[ConfigurationPanel] Navigation clicked: ${section}`);
                    this.switchSection(section);
                });
            });
        }, 50);
        
        // Close on background click
        this.panel.addEventListener('click', (e) => {
            if (e.target === this.panel) {
                this.hide();
            }
        });
        
        // Listen for setting changes for real-time application
        this.panel.addEventListener('change', (e) => {
            if (e.target.matches('[data-setting]')) {
                this.handleSettingChange(e.target);
            }
        });
        
        // Listen for input events for real-time preview
        this.panel.addEventListener('input', (e) => {
            if (e.target.matches('[data-setting]')) {
                this.handleSettingPreview(e.target);
            }
        });
    }
    
    /**
     * Handle setting change with real-time application
     * @param {HTMLElement} element - Input element
     */
    handleSettingChange(element) {
        const settingPath = element.dataset.setting;
        const [category, key] = settingPath.split('.');
        
        let value = element.value;
        
        // Convert value based on input type
        if (element.type === 'checkbox') {
            value = element.checked;
        } else if (element.type === 'number') {
            value = parseFloat(element.value);
        } else if (element.type === 'range') {
            value = parseInt(element.value);
        }
        
        // Update internal settings
        if (!this.settings[category]) {
            this.settings[category] = {};
        }
        this.settings[category][key] = value;
        
        // Apply immediately for certain settings
        this.applySettingImmediately(category, key, value);
        
        // Mark as having unsaved changes
        this.unsavedChanges = true;
        this.updateApplyButton();
        
        console.log('Config', '[ConfigurationPanel] Setting changed:', { category, key, value });
    }
    
    /**
     * Handle setting preview (for real-time visual feedback)
     * @param {HTMLElement} element - Input element
     */
    handleSettingPreview(element) {
        const settingPath = element.dataset.setting;
        const [category, key] = settingPath.split('.');
        
        // Only preview certain appearance settings
        if (category === 'appearance') {
            this.previewSetting(key, element.value);
        }
    }
    
    /**
     * Apply setting immediately for real-time changes
     * @param {string} category - Setting category
     * @param {string} key - Setting key
     * @param {*} value - Setting value
     */
    applySettingImmediately(category, key, value) {
        switch (`${category}.${key}`) {
            case 'appearance.theme':
                this.applyTheme(value);
                break;
                
            case 'appearance.font_size':
                this.applyFontSize(value);
                break;
                
            case 'appearance.ui_scale':
                this.applyUIScale(value);
                break;
                
            case 'appearance.animations_enabled':
                this.applyAnimations(value);
                break;
                
            case 'general.language':
                this.applyLanguage(value);
                break;
                
            default:
                // Some settings require restart or manual application
                break;
        }
    }
    
    /**
     * Apply theme immediately
     * @param {string} theme - Theme name
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        // Update icon colors if needed
        if (window.activityBar) {
            window.activityBar.updateForTheme(theme);
        }
        
        console.log('Config', '[ConfigurationPanel] Theme applied:', theme);
    }
    
    /**
     * Apply font size immediately
     * @param {number} fontSize - Font size in pixels
     */
    applyFontSize(fontSize) {
        document.documentElement.style.setProperty('--base-font-size', `${fontSize}px`);
        console.log('Config', '[ConfigurationPanel] Font size applied:', fontSize);
    }
    
    /**
     * Apply UI scale immediately
     * @param {number} scale - Scale percentage
     */
    applyUIScale(scale) {
        const scaleValue = scale / 100;
        document.documentElement.style.setProperty('--ui-scale', scaleValue);
        console.log('Config', '[ConfigurationPanel] UI scale applied:', scale);
    }
    
    /**
     * Apply animations setting immediately
     * @param {boolean} enabled - Whether animations are enabled
     */
    applyAnimations(enabled) {
        if (enabled) {
            document.documentElement.classList.remove('no-animations');
        } else {
            document.documentElement.classList.add('no-animations');
        }
        console.log('Config', '[ConfigurationPanel] Animations applied:', enabled);
    }
    
    /**
     * Apply language immediately
     * @param {string} language - Language code
     */
    applyLanguage(language) {
        if (window.I18n) {
            window.I18n.setLanguage(language);
            window.I18n.applyTranslations();
        }
        console.log('Config', '[ConfigurationPanel] Language applied:', language);
    }
    
    /**
     * Preview setting (temporary visual change)
     * @param {string} key - Setting key
     * @param {*} value - Setting value
     */
    previewSetting(key, value) {
        switch (key) {
            case 'font_size':
                document.documentElement.style.setProperty('--preview-font-size', `${value}px`);
                break;
            case 'ui_scale':
                document.documentElement.style.setProperty('--preview-ui-scale', value / 100);
                break;
        }
    }
    
    /**
     * Switch to a different section
     * @param {string} sectionId - Section ID
     */
    switchSection(sectionId) {
        if (sectionId === this.currentSection) return;
        
        // Update navigation
        this.panel.querySelectorAll('.config-nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.section === sectionId) {
                item.classList.add('active');
            }
        });
        
        // Update current section
        this.currentSection = sectionId;
        
        // Render new section
        this.renderSection(sectionId);
        
        console.log('Config', '[ConfigurationPanel] Switched to section:', sectionId);
    }
    
    /**
     * Render a configuration section
     * @param {string} sectionId - Section ID
     */
    renderSection(sectionId) {
        const contentArea = this.panel.querySelector('#config-content-area');
        
        switch (sectionId) {
            case 'general':
                contentArea.innerHTML = this.renderGeneralSection();
                break;
            case 'appearance':
                contentArea.innerHTML = this.renderAppearanceSection();
                break;
            case 'sync':
                contentArea.innerHTML = this.renderSyncSection();
                break;
            case 'modules':
                contentArea.innerHTML = this.renderModulesSection();
                break;
            case 'updates':
                contentArea.innerHTML = this.renderUpdatesSection();
                break;
            case 'advanced':
                contentArea.innerHTML = this.renderAdvancedSection();
                break;
            case 'license':
                contentArea.innerHTML = this.renderLicenseSection();
                break;
            default:
                contentArea.innerHTML = '<p>Sección no encontrada</p>';
        }
        
        // Populate current values
        this.populateCurrentValues();
        
        // Setup section-specific event listeners
        this.setupSectionEventListeners();
    }
    
    /**
     * Render general settings section
     * @returns {string} HTML content
     */
    renderGeneralSection() {
        return `
            <div class="config-section">
                <h3>Configuración General</h3>
                <p class="section-description">Configuraciones básicas de la aplicación</p>
                
                <div class="form-group">
                    <label for="setting-language">Idioma</label>
                    <select id="setting-language" data-setting="general.language">
                        <option value="es">Español</option>
                        <option value="en">English</option>
                        <option value="pt">Português</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="setting-country">País</label>
                    <select id="setting-country" data-setting="general.country">
                        <option value="US">Estados Unidos</option>
                        <option value="ES">España</option>
                        <option value="MX">México</option>
                        <option value="AR">Argentina</option>
                        <option value="CO">Colombia</option>
                        <option value="CL">Chile</option>
                        <option value="PE">Perú</option>
                        <option value="SV">El Salvador</option>
                        <option value="BR">Brasil</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="setting-timezone">Zona Horaria</label>
                    <select id="setting-timezone" data-setting="general.timezone">
                        <option value="auto">Automática</option>
                        <option value="America/New_York">Nueva York</option>
                        <option value="America/Chicago">Chicago</option>
                        <option value="America/Los_Angeles">Los Ángeles</option>
                        <option value="America/Mexico_City">Ciudad de México</option>
                        <option value="America/Bogota">Bogotá</option>
                        <option value="America/Lima">Lima</option>
                        <option value="America/Santiago">Santiago</option>
                        <option value="America/Buenos_Aires">Buenos Aires</option>
                        <option value="America/Sao_Paulo">São Paulo</option>
                        <option value="Europe/Madrid">Madrid</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" data-setting="general.auto_save"> 
                        Guardado automático
                    </label>
                    <p class="form-help">Guarda automáticamente los cambios cada cierto tiempo</p>
                </div>
                
                <div class="form-group">
                    <label for="setting-auto-save-interval">Intervalo de guardado (segundos)</label>
                    <input type="number" id="setting-auto-save-interval" data-setting="general.auto_save_interval" 
                           min="10" max="300" step="10">
                </div>
                
                <div class="form-group">
                    <label for="setting-coredesk-directory">Directorio de trabajo de CoreDesk</label>
                    <div class="input-group">
                        <input type="text" id="setting-coredesk-directory" data-setting="general.coredesk_directory" 
                               placeholder="Seleccionar directorio..." readonly>
                        <button type="button" id="browse-coredesk-directory" class="btn btn-secondary">Examinar</button>
                        <button type="button" id="reset-coredesk-directory" class="btn btn-outline">Restablecer</button>
                    </div>
                    <p class="form-help">Directorio donde CoreDesk almacenará todos los archivos de trabajo. Cambiar requiere reiniciar la aplicación.</p>
                </div>
            </div>
        `;
    }
    
    /**
     * Render appearance settings section
     * @returns {string} HTML content
     */
    renderAppearanceSection() {
        return `
            <div class="config-section">
                <h3>Configuración de Apariencia</h3>
                <p class="section-description">Personaliza la interfaz visual de CoreDesk</p>
                
                <div class="form-group">
                    <label for="setting-theme">Tema</label>
                    <select id="setting-theme" data-setting="appearance.theme">
                        <option value="dark">Oscuro</option>
                        <option value="light">Claro</option>
                        <option value="auto">Automático</option>
                    </select>
                    <p class="form-help">El tema se aplica inmediatamente</p>
                </div>
                
                <div class="form-group">
                    <label for="setting-font-size">Tamaño de fuente</label>
                    <input type="range" id="setting-font-size" data-setting="appearance.font_size" 
                           min="10" max="20" step="1">
                    <span class="range-value" id="font-size-value">14px</span>
                    <p class="form-help">Ajusta el tamaño de la fuente en toda la aplicación</p>
                </div>
                
                <div class="form-group">
                    <label for="setting-ui-scale">Escala de interfaz</label>
                    <input type="range" id="setting-ui-scale" data-setting="appearance.ui_scale" 
                           min="75" max="150" step="5">
                    <span class="range-value" id="ui-scale-value">100%</span>
                    <p class="form-help">Escala toda la interfaz de usuario</p>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" data-setting="appearance.animations_enabled"> 
                        Habilitar animaciones
                    </label>
                    <p class="form-help">Desactivar puede mejorar el rendimiento en equipos lentos</p>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" data-setting="appearance.compact_mode"> 
                        Modo compacto
                    </label>
                    <p class="form-help">Reduce el espaciado para mostrar más información</p>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" data-setting="appearance.show_welcome_screen"> 
                        Mostrar pantalla de bienvenida
                    </label>
                    <p class="form-help">Muestra la pantalla de bienvenida al iniciar</p>
                </div>
            </div>
        `;
    }
    
    /**
     * Populate current values in form elements
     */
    populateCurrentValues() {
        this.panel.querySelectorAll('[data-setting]').forEach(element => {
            const settingPath = element.dataset.setting;
            const [category, key] = settingPath.split('.');
            const value = this.settings[category]?.[key];
            
            if (value !== undefined) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
                
                // Update range display values
                if (element.type === 'range') {
                    this.updateRangeDisplay(element);
                }
            }
        });
    }
    
    /**
     * Update range input display values
     * @param {HTMLElement} rangeElement - Range input element
     */
    updateRangeDisplay(rangeElement) {
        const setting = rangeElement.dataset.setting;
        const value = rangeElement.value;
        
        let displayValue = value;
        
        switch (setting) {
            case 'appearance.font_size':
                displayValue = `${value}px`;
                const fontSizeDisplay = document.getElementById('font-size-value');
                if (fontSizeDisplay) fontSizeDisplay.textContent = displayValue;
                break;
                
            case 'appearance.ui_scale':
                displayValue = `${value}%`;
                const uiScaleDisplay = document.getElementById('ui-scale-value');
                if (uiScaleDisplay) uiScaleDisplay.textContent = displayValue;
                break;
        }
    }
    
    /**
     * Apply all current settings
     */
    async applySettings() {
        try {
            console.log('Config', '[ConfigurationPanel] Applying all settings...', );
            
            // Save to database
            await this.saveSettingsToDatabase();
            
            // Save to localStorage
            this.saveSettingsToLocalStorage();
            
            // Apply all settings immediately
            this.applyAllSettings();
            
            // Mark as saved
            this.unsavedChanges = false;
            this.updateApplyButton();
            
            // Show success message
            this.showNotification('Configuración guardada correctamente', 'success');
            
            console.log('Config', '[ConfigurationPanel] Settings applied successfully', );
            
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error applying settings:', error);
            this.showNotification('Error al guardar la configuración', 'error');
        }
    }
    
    /**
     * Apply all settings immediately
     */
    applyAllSettings() {
        // Apply appearance settings
        if (this.settings.appearance) {
            Object.entries(this.settings.appearance).forEach(([key, value]) => {
                this.applySettingImmediately('appearance', key, value);
            });
        }
        
        // Apply general settings
        if (this.settings.general) {
            Object.entries(this.settings.general).forEach(([key, value]) => {
                this.applySettingImmediately('general', key, value);
            });
        }
        
        // Emit settings applied event
        window.dispatchEvent(new CustomEvent('coredesk:settings:applied', {
            detail: { settings: this.settings }
        }));
    }
    
    /**
     * Save settings to database
     */
    async saveSettingsToDatabase() {
        // Implementation would depend on database API
        console.log('Config', '[ConfigurationPanel] Settings would be saved to database', );
    }
    
    /**
     * Save settings to localStorage
     */
    saveSettingsToLocalStorage() {
        try {
            localStorage.setItem('coredesk_session_settings', JSON.stringify(this.settings));
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error saving to localStorage:', error);
        }
    }
    
    /**
     * Update apply button state
     */
    updateApplyButton() {
        const applyButton = this.panel.querySelector('#config-apply');
        if (applyButton) {
            applyButton.disabled = !this.unsavedChanges;
            applyButton.textContent = this.unsavedChanges ? 'Aplicar *' : 'Aplicar';
        }
    }
    
    /**
     * Show configuration panel
     */
    show() {
        this.panel.style.display = 'flex';
        this.isVisible = true;
        
        // Focus on panel
        this.panel.focus();
        
        console.log('Config', '[ConfigurationPanel] Panel shown', );
    }
    
    /**
     * Hide configuration panel
     */
    hide() {
        this.panel.style.display = 'none';
        this.isVisible = false;
        
        console.log('Config', '[ConfigurationPanel] Panel hidden', );
    }
    
    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type
     */
    showNotification(message, type = 'info') {
        // Simple implementation - could be enhanced with proper notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
            color: white;
            border-radius: 4px;
            z-index: 10000;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 3000);
    }

    /**
     * Render sync settings section
     * @returns {string} HTML content
     * @private
     */
    renderSyncSection() {
        return `
            <div class="config-section">
                <h3>Configuración de Sincronización</h3>
                <p class="section-description">Gestiona la sincronización de datos local y en la nube</p>
                
                <div class="setting-group">
                    <h4>Sincronización Local</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-sync-enabled" class="setting-checkbox">
                            Habilitar Sincronización
                        </label>
                        <span class="setting-description">Activar la sincronización automática de datos</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Intervalo de Sincronización</label>
                        <select id="setting-sync-interval" class="setting-input">
                            <option value="10000">10 segundos</option>
                            <option value="30000">30 segundos</option>
                            <option value="60000">1 minuto</option>
                            <option value="300000">5 minutos</option>
                            <option value="600000">10 minutos</option>
                        </select>
                        <span class="setting-description">Frecuencia de sincronización automática</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-offline-queue" class="setting-checkbox">
                            Cola Sin Conexión
                        </label>
                        <span class="setting-description">Mantener cola de cambios cuando no hay conexión</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Sincronización en la Nube</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-cloud-sync" class="setting-checkbox">
                            Habilitar Sincronización en la Nube
                        </label>
                        <span class="setting-description">Sincronizar datos con Firebase (requiere licencia premium)</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-realtime-sync" class="setting-checkbox">
                            Sincronización en Tiempo Real
                        </label>
                        <span class="setting-description">Recibir actualizaciones inmediatas de otros dispositivos</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Configuración de Firebase</label>
                        <button id="firebase-config-btn" class="btn btn-secondary">Configurar Firebase</button>
                        <span class="setting-description">Configurar credenciales de Firebase para la nube</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Resolución de Conflictos</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">Estrategia de Conflictos</label>
                        <select id="setting-conflict-strategy" class="setting-input">
                            <option value="client-wins">Cliente Gana</option>
                            <option value="server-wins">Servidor Gana</option>
                            <option value="manual">Resolución Manual</option>
                        </select>
                        <span class="setting-description">Cómo resolver conflictos de sincronización</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render modules settings section
     * @returns {string} HTML content
     * @private
     */
    renderModulesSection() {
        return `
            <div class="config-section">
                <h3>Configuración de Módulos</h3>
                <p class="section-description">Gestiona qué módulos están disponibles y su configuración</p>
                
                <div class="setting-group">
                    <h4>Módulos Disponibles</h4>
                    
                    <div class="module-grid">
                        <div class="module-setting-card">
                            <div class="module-header">
                                <h5>LexFlow</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="setting-lexflow-enabled" class="module-toggle">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <p class="module-description">Gestión completa de casos legales y documentación jurídica</p>
                            <div class="module-status">Estado: <span class="status-indicator active">Activo</span></div>
                        </div>
                        
                        <div class="module-setting-card">
                            <div class="module-header">
                                <h5>ProtocolX</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="setting-protocolx-enabled" class="module-toggle">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <p class="module-description">Administración digital de protocolos notariales</p>
                            <div class="module-status">Estado: <span class="status-indicator active">Activo</span></div>
                        </div>
                        
                        <div class="module-setting-card">
                            <div class="module-header">
                                <h5>AuditPro</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="setting-auditpro-enabled" class="module-toggle" disabled>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <p class="module-description">Herramientas especializadas para auditorías contables</p>
                            <div class="module-status">Estado: <span class="status-indicator planned">Planificado</span></div>
                        </div>
                        
                        <div class="module-setting-card">
                            <div class="module-header">
                                <h5>FinSync</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="setting-finsync-enabled" class="module-toggle" disabled>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <p class="module-description">Contabilidad integral para contadores públicos</p>
                            <div class="module-status">Estado: <span class="status-indicator planned">Planificado</span></div>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Comportamiento de Módulos</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">Máximo de Pestañas por Módulo</label>
                        <input type="number" id="setting-max-tabs-per-module" class="setting-input" min="1" max="50" step="1">
                        <span class="setting-description">Número máximo de pestañas abiertas por módulo</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-module-confirmation" class="setting-checkbox">
                            Confirmar Cambio de Módulo
                        </label>
                        <span class="setting-description">Pedir confirmación antes de cambiar de módulo</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-preserve-module-state" class="setting-checkbox">
                            Preservar Estado de Módulo
                        </label>
                        <span class="setting-description">Mantener el estado de pestañas al cambiar módulos</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render advanced settings section
     * @returns {string} HTML content
     * @private
     */
    renderAdvancedSection() {
        return `
            <div class="config-section">
                <h3>Configuración Avanzada</h3>
                <p class="section-description">Configuraciones avanzadas del sistema y desarrollo</p>
                
                <div class="setting-group">
                    <h4>Desarrollo y Depuración</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-debug-mode" class="setting-checkbox">
                            Modo de Depuración
                        </label>
                        <span class="setting-description">Habilitar logs detallados en la consola</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-beta-features" class="setting-checkbox">
                            Características Beta
                        </label>
                        <span class="setting-description">Habilitar funciones experimentales</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Nivel de Log</label>
                        <select id="setting-log-level" class="setting-input">
                            <option value="error">Error</option>
                            <option value="warn">Advertencia</option>
                            <option value="info">Información</option>
                            <option value="debug">Depuración</option>
                            <option value="trace">Rastreo</option>
                        </select>
                        <span class="setting-description">Nivel de detalle de los logs</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Rendimiento</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-performance-monitoring" class="setting-checkbox">
                            Monitoreo de Rendimiento
                        </label>
                        <span class="setting-description">Recopilar métricas de rendimiento</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Máximo de Pestañas</label>
                        <input type="number" id="setting-max-tabs" class="setting-input" min="5" max="100" step="1">
                        <span class="setting-description">Número máximo de pestañas abiertas simultáneamente</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Tamaño de Caché (MB)</label>
                        <input type="number" id="setting-cache-size" class="setting-input" min="50" max="1000" step="50">
                        <span class="setting-description">Tamaño máximo del caché en memoria</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Reportes y Análisis</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-crash-reporting" class="setting-checkbox">
                            Reportes de Errores
                        </label>
                        <span class="setting-description">Enviar reportes automáticos de errores</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-usage-analytics" class="setting-checkbox">
                            Análisis de Uso
                        </label>
                        <span class="setting-description">Recopilar estadísticas anónimas de uso</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Mantenimiento del Sistema</h4>
                    
                    <div class="system-info">
                        <div class="info-row">
                            <span>Versión de CoreDesk:</span>
                            <span>v2.0.0</span>
                        </div>
                        <div class="info-row">
                            <span>Base de Datos:</span>
                            <span id="database-size">Calculando...</span>
                        </div>
                        <div class="info-row">
                            <span>Caché Actual:</span>
                            <span id="cache-usage">0 MB</span>
                        </div>
                    </div>
                    
                    <div class="maintenance-actions">
                        <button id="clear-cache-btn" class="btn btn-secondary">Limpiar Caché</button>
                        <button id="export-logs-btn" class="btn btn-secondary">Exportar Logs</button>
                        <button id="system-diagnostics-btn" class="btn btn-secondary">Diagnósticos</button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render license settings section
     * @returns {string} HTML content
     * @private
     */
    renderLicenseSection() {
        return `
            <div class="config-section">
                <h3>Configuración de Licencia</h3>
                <p class="section-description">Gestiona tu licencia y configuraciones relacionadas</p>
                
                <div class="setting-group">
                    <h4>Estado de Licencia</h4>
                    
                    <div class="license-info-card" id="license-info-card">
                        <div class="license-status">
                            <div class="status-badge" id="license-status-badge">Cargando...</div>
                        </div>
                        <div class="license-details" id="license-details">
                            <!-- License details will be populated here -->
                        </div>
                    </div>
                    
                    <div class="license-actions">
                        <button id="activate-license-btn" class="btn btn-primary">Activar Nueva Licencia</button>
                        <button id="deactivate-license-btn" class="btn btn-secondary">Desactivar Licencia</button>
                        <button id="check-license-btn" class="btn btn-secondary">Verificar Licencia</button>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Configuración de Verificación</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-auto-check-license" class="setting-checkbox">
                            Verificación Automática
                        </label>
                        <span class="setting-description">Verificar automáticamente el estado de la licencia</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Intervalo de Verificación</label>
                        <select id="setting-license-check-interval" class="setting-input">
                            <option value="12">12 horas</option>
                            <option value="24">24 horas</option>
                            <option value="48">48 horas</option>
                            <option value="168">1 semana</option>
                        </select>
                        <span class="setting-description">Frecuencia de verificación automática</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Días de Gracia Sin Conexión</label>
                        <input type="number" id="setting-offline-grace-days" class="setting-input" min="1" max="30" step="1">
                        <span class="setting-description">Días que la licencia sigue válida sin conexión</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Información del Dispositivo</h4>
                    
                    <div class="device-info" id="device-info">
                        <div class="info-row">
                            <span>ID del Dispositivo:</span>
                            <span id="device-id">Generando...</span>
                        </div>
                        <div class="info-row">
                            <span>Plataforma:</span>
                            <span id="device-platform">Desconocida</span>
                        </div>
                        <div class="info-row">
                            <span>Activaciones:</span>
                            <span id="device-activations">0/1</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render modules settings section
     * @returns {string} HTML content
     * @private
     */
    renderModulesSection() {
        return `
            <div class="config-section">
                <h3>Configuración de Módulos</h3>
                <p class="section-description">Gestiona qué módulos están disponibles y su configuración</p>
                
                <div class="setting-group">
                    <h4>Módulos Disponibles</h4>
                    
                    <div class="module-grid">
                        <div class="module-setting-card">
                            <div class="module-header">
                                <h5>LexFlow</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="setting-lexflow-enabled" class="module-toggle">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <p class="module-description">Gestión completa de casos legales y documentación jurídica</p>
                            <div class="module-status">Estado: <span class="status-indicator active">Activo</span></div>
                        </div>
                        
                        <div class="module-setting-card">
                            <div class="module-header">
                                <h5>ProtocolX</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="setting-protocolx-enabled" class="module-toggle">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <p class="module-description">Administración digital de protocolos notariales</p>
                            <div class="module-status">Estado: <span class="status-indicator active">Activo</span></div>
                        </div>
                        
                        <div class="module-setting-card">
                            <div class="module-header">
                                <h5>AuditPro</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="setting-auditpro-enabled" class="module-toggle" disabled>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <p class="module-description">Herramientas especializadas para auditorías contables</p>
                            <div class="module-status">Estado: <span class="status-indicator planned">Planificado</span></div>
                        </div>
                        
                        <div class="module-setting-card">
                            <div class="module-header">
                                <h5>FinSync</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="setting-finsync-enabled" class="module-toggle" disabled>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <p class="module-description">Contabilidad integral para contadores públicos</p>
                            <div class="module-status">Estado: <span class="status-indicator planned">Planificado</span></div>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Comportamiento de Módulos</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">Máximo de Pestañas por Módulo</label>
                        <input type="number" id="setting-max-tabs-per-module" class="setting-input" min="1" max="50" step="1">
                        <span class="setting-description">Número máximo de pestañas abiertas por módulo</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-module-confirmation" class="setting-checkbox">
                            Confirmar Cambio de Módulo
                        </label>
                        <span class="setting-description">Pedir confirmación antes de cambiar de módulo</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-preserve-module-state" class="setting-checkbox">
                            Preservar Estado de Módulo
                        </label>
                        <span class="setting-description">Mantener el estado de pestañas al cambiar módulos</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render advanced settings section
     * @returns {string} HTML content
     * @private
     */
    renderAdvancedSection() {
        return `
            <div class="config-section">
                <h3>Configuración Avanzada</h3>
                <p class="section-description">Configuraciones avanzadas del sistema y desarrollo</p>
                
                <div class="setting-group">
                    <h4>Desarrollo y Depuración</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-debug-mode" class="setting-checkbox">
                            Modo de Depuración
                        </label>
                        <span class="setting-description">Habilitar logs detallados en la consola</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-beta-features" class="setting-checkbox">
                            Características Beta
                        </label>
                        <span class="setting-description">Habilitar funciones experimentales</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Nivel de Log</label>
                        <select id="setting-log-level" class="setting-input">
                            <option value="error">Error</option>
                            <option value="warn">Advertencia</option>
                            <option value="info">Información</option>
                            <option value="debug">Depuración</option>
                            <option value="trace">Rastreo</option>
                        </select>
                        <span class="setting-description">Nivel de detalle de los logs</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Rendimiento</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-performance-monitoring" class="setting-checkbox">
                            Monitoreo de Rendimiento
                        </label>
                        <span class="setting-description">Recopilar métricas de rendimiento</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Máximo de Pestañas</label>
                        <input type="number" id="setting-max-tabs" class="setting-input" min="5" max="100" step="1">
                        <span class="setting-description">Número máximo de pestañas abiertas simultáneamente</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Tamaño de Caché (MB)</label>
                        <input type="number" id="setting-cache-size" class="setting-input" min="50" max="1000" step="50">
                        <span class="setting-description">Tamaño máximo del caché en memoria</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Reportes y Análisis</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-crash-reporting" class="setting-checkbox">
                            Reportes de Errores
                        </label>
                        <span class="setting-description">Enviar reportes automáticos de errores</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-usage-analytics" class="setting-checkbox">
                            Análisis de Uso
                        </label>
                        <span class="setting-description">Recopilar estadísticas anónimas de uso</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Mantenimiento del Sistema</h4>
                    
                    <div class="system-info">
                        <div class="info-row">
                            <span>Versión de CoreDesk:</span>
                            <span>v2.0.0</span>
                        </div>
                        <div class="info-row">
                            <span>Base de Datos:</span>
                            <span id="database-size">Calculando...</span>
                        </div>
                        <div class="info-row">
                            <span>Caché Actual:</span>
                            <span id="cache-usage">0 MB</span>
                        </div>
                    </div>
                    
                    <div class="maintenance-actions">
                        <button id="clear-cache-btn" class="btn btn-secondary">Limpiar Caché</button>
                        <button id="export-logs-btn" class="btn btn-secondary">Exportar Logs</button>
                        <button id="system-diagnostics-btn" class="btn btn-secondary">Diagnósticos</button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render license settings section
     * @returns {string} HTML content
     * @private
     */
    renderLicenseSection() {
        return `
            <div class="config-section">
                <h3>Configuración de Licencia</h3>
                <p class="section-description">Gestiona tu licencia y configuraciones relacionadas</p>
                
                <div class="setting-group">
                    <h4>Estado de Licencia</h4>
                    
                    <div class="license-info-card" id="license-info-card">
                        <div class="license-status">
                            <div class="status-badge" id="license-status-badge">Cargando...</div>
                        </div>
                        <div class="license-details" id="license-details">
                            <!-- License details will be populated here -->
                        </div>
                    </div>
                    
                    <div class="license-actions">
                        <button id="activate-license-btn" class="btn btn-primary">Activar Nueva Licencia</button>
                        <button id="deactivate-license-btn" class="btn btn-secondary">Desactivar Licencia</button>
                        <button id="check-license-btn" class="btn btn-secondary">Verificar Licencia</button>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Configuración de Verificación</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-auto-check-license" class="setting-checkbox">
                            Verificación Automática
                        </label>
                        <span class="setting-description">Verificar automáticamente el estado de la licencia</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Intervalo de Verificación</label>
                        <select id="setting-license-check-interval" class="setting-input">
                            <option value="12">12 horas</option>
                            <option value="24">24 horas</option>
                            <option value="48">48 horas</option>
                            <option value="168">1 semana</option>
                        </select>
                        <span class="setting-description">Frecuencia de verificación automática</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Días de Gracia Sin Conexión</label>
                        <input type="number" id="setting-offline-grace-days" class="setting-input" min="1" max="30" step="1">
                        <span class="setting-description">Días que la licencia sigue válida sin conexión</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Información del Dispositivo</h4>
                    
                    <div class="device-info" id="device-info">
                        <div class="info-row">
                            <span>ID del Dispositivo:</span>
                            <span id="device-id">Generando...</span>
                        </div>
                        <div class="info-row">
                            <span>Plataforma:</span>
                            <span id="device-platform">Desconocida</span>
                        </div>
                        <div class="info-row">
                            <span>Activaciones:</span>
                            <span id="device-activations">0/1</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Set up section-specific event listeners
     * @param {string} sectionId - Section ID
     * @private
     */
    setupSectionEventListeners(sectionId) {
        switch (sectionId) {
            case 'general':
                this.setupGeneralEventListeners();
                break;
            case 'appearance':
                this.setupAppearanceEventListeners();
                break;
            case 'sync':
                this.setupSyncEventListeners();
                break;
            case 'modules':
                this.setupModulesEventListeners();
                break;
            case 'updates':
                this.setupUpdatesEventListeners();
                break;
            case 'advanced':
                this.setupAdvancedEventListeners();
                break;
            case 'license':
                this.setupLicenseEventListeners();
                break;
        }
    }

    /**
     * Set up general section event listeners
     * @private
     */
    setupGeneralEventListeners() {
        // Language change
        const languageSelect = this.panel.querySelector('#setting-language');
        if (languageSelect) {
            languageSelect.addEventListener('change', () => {
                this.markUnsavedChanges();
            });
        }
        
        // Auto-save interval dependency
        const autoSaveCheckbox = this.panel.querySelector('#setting-auto-save');
        const autoSaveInterval = this.panel.querySelector('#setting-auto-save-interval');
        
        if (autoSaveCheckbox && autoSaveInterval) {
            autoSaveCheckbox.addEventListener('change', () => {
                autoSaveInterval.disabled = !autoSaveCheckbox.checked;
                this.markUnsavedChanges();
            });
        }
        
        // Directory selection buttons
        const browseBtn = this.panel.querySelector('#browse-coredesk-directory');
        const resetBtn = this.panel.querySelector('#reset-coredesk-directory');
        
        if (browseBtn) {
            browseBtn.addEventListener('click', () => {
                this.selectCoreDeskDirectory();
            });
        }
        
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetCoreDeskDirectory();
            });
        }
        
        // Mark changes for all inputs
        this.panel.querySelectorAll('#config-content-area input, #config-content-area select').forEach(input => {
            input.addEventListener('change', () => this.markUnsavedChanges());
        });
    }

    /**
     * Set up appearance section event listeners
     * @private
     */
    setupAppearanceEventListeners() {
        // Theme selection
        const themeOptions = this.panel.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            option.addEventListener('click', () => {
                const theme = option.dataset.theme;
                const radio = option.querySelector('input[type="radio"]');
                radio.checked = true;
                
                // Apply theme immediately
                this.applyTheme(theme);
                this.markUnsavedChanges();
            });
        });
        
        // Font size slider
        const fontSizeSlider = this.panel.querySelector('#setting-font-size');
        const fontSizeValue = this.panel.querySelector('#font-size-value');
        
        if (fontSizeSlider && fontSizeValue) {
            fontSizeSlider.addEventListener('input', () => {
                fontSizeValue.textContent = `${fontSizeSlider.value}px`;
                this.applyFontSize(fontSizeSlider.value);
                this.markUnsavedChanges();
            });
        }
        
        // UI scale slider
        const uiScaleSlider = this.panel.querySelector('#setting-ui-scale');
        const uiScaleValue = this.panel.querySelector('#ui-scale-value');
        
        if (uiScaleSlider && uiScaleValue) {
            uiScaleSlider.addEventListener('input', () => {
                uiScaleValue.textContent = `${uiScaleSlider.value}%`;
                this.applyUIScale(uiScaleSlider.value);
                this.markUnsavedChanges();
            });
        }
        
        // Mark changes for all inputs
        this.panel.querySelectorAll('#config-content-area input, #config-content-area select').forEach(input => {
            input.addEventListener('change', () => this.markUnsavedChanges());
        });
    }

    /**
     * Set up sync section event listeners
     * @private
     */
    setupSyncEventListeners() {
        // Firebase config button
        const firebaseConfigBtn = this.panel.querySelector('#firebase-config-btn');
        if (firebaseConfigBtn) {
            firebaseConfigBtn.addEventListener('click', () => {
                this.showFirebaseConfigModal();
            });
        }
        
        // Force sync button
        const forceSyncBtn = this.panel.querySelector('#force-sync-btn');
        if (forceSyncBtn) {
            forceSyncBtn.addEventListener('click', async () => {
                if (window.dataSyncService) {
                    await window.dataSyncService.forcSync();
                    this.updateSyncStatus();
                }
            });
        }
        
        // Clear queue button
        const clearQueueBtn = this.panel.querySelector('#clear-queue-btn');
        if (clearQueueBtn) {
            clearQueueBtn.addEventListener('click', () => {
                if (confirm('¿Estás seguro de que quieres limpiar la cola de sincronización?')) {
                    // Clear sync queue logic would go here
                    console.log('Config', 'Sync queue cleared', );
                }
            });
        }
        
        // Update sync status
        this.updateSyncStatus();
        
        // Mark changes for all inputs
        this.panel.querySelectorAll('#config-content-area input, #config-content-area select').forEach(input => {
            input.addEventListener('change', () => this.markUnsavedChanges());
        });
    }

    /**
     * Set up modules section event listeners
     * @private
     */
    setupModulesEventListeners() {
        // Module toggles
        this.panel.querySelectorAll('.module-toggle').forEach(toggle => {
            toggle.addEventListener('change', () => {
                this.markUnsavedChanges();
            });
        });
        
        // Mark changes for all inputs
        this.panel.querySelectorAll('#config-content-area input, #config-content-area select').forEach(input => {
            input.addEventListener('change', () => this.markUnsavedChanges());
        });
    }

    /**
     * Set up advanced section event listeners
     * @private
     */
    setupAdvancedEventListeners() {
        // Maintenance actions
        const clearCacheBtn = this.panel.querySelector('#clear-cache-btn');
        if (clearCacheBtn) {
            clearCacheBtn.addEventListener('click', () => {
                this.clearCache();
            });
        }
        
        const exportLogsBtn = this.panel.querySelector('#export-logs-btn');
        if (exportLogsBtn) {
            exportLogsBtn.addEventListener('click', () => {
                this.exportLogs();
            });
        }
        
        const diagnosticsBtn = this.panel.querySelector('#system-diagnostics-btn');
        if (diagnosticsBtn) {
            diagnosticsBtn.addEventListener('click', () => {
                this.runSystemDiagnostics();
            });
        }
        
        // Update system info
        this.updateSystemInfo();
        
        // Mark changes for all inputs
        this.panel.querySelectorAll('#config-content-area input, #config-content-area select').forEach(input => {
            input.addEventListener('change', () => this.markUnsavedChanges());
        });
    }

    /**
     * Set up license section event listeners
     * @private
     */
    setupLicenseEventListeners() {
        // License actions
        const activateBtn = this.panel.querySelector('#activate-license-btn');
        if (activateBtn) {
            activateBtn.addEventListener('click', () => {
                if (window.licenseActivationModal) {
                    window.licenseActivationModal.show('license');
                }
            });
        }
        
        const deactivateBtn = this.panel.querySelector('#deactivate-license-btn');
        if (deactivateBtn) {
            deactivateBtn.addEventListener('click', () => {
                this.deactivateLicense();
            });
        }
        
        const checkBtn = this.panel.querySelector('#check-license-btn');
        if (checkBtn) {
            checkBtn.addEventListener('click', () => {
                this.checkLicense();
            });
        }
        
        // Update license info
        this.updateLicenseInfo();
        this.updateDeviceInfo();
        
        // Mark changes for all inputs
        this.panel.querySelectorAll('#config-content-area input, #config-content-area select').forEach(input => {
            input.addEventListener('change', () => this.markUnsavedChanges());
        });
    }

    /**
     * Apply current values to section inputs
     * @param {string} sectionId - Section ID
     * @private
     */
    applySectionValues(sectionId) {
        const sectionSettings = this.settings[sectionId];
        if (!sectionSettings) return;
        
        Object.keys(sectionSettings).forEach(key => {
            const input = this.panel.querySelector(`#setting-${key.replace(/_/g, '-')}`);
            if (input) {
                const value = sectionSettings[key];
                
                if (input.type === 'checkbox') {
                    input.checked = value;
                } else if (input.type === 'radio') {
                    if (input.value === value) {
                        input.checked = true;
                    }
                } else {
                    input.value = value;
                }
                
                // Trigger change event to update UI
                input.dispatchEvent(new Event('change'));
            }
        });
        
        // Special handling for theme
        if (sectionId === 'appearance' && sectionSettings.theme) {
            const themeRadio = this.panel.querySelector(`#theme-${sectionSettings.theme}`);
            if (themeRadio) {
                themeRadio.checked = true;
            }
        }
    }

    /**
     * Mark that there are unsaved changes
     * @private
     */
    markUnsavedChanges() {
        this.unsavedChanges = true;
        
        const statusElement = this.panel.querySelector('#config-status .status-text');
        if (statusElement) {
            statusElement.textContent = 'Cambios sin guardar';
            statusElement.style.color = '#ffc107';
        }
        
        // Enable save button
        const saveButton = this.panel.querySelector('#config-save-btn');
        if (saveButton) {
            saveButton.disabled = false;
        }
    }

    /**
     * Save all settings
     * @private
     */
    async saveSettings() {
        try {
            console.log('Config', '[ConfigurationPanel] Saving settings...', );
            
            // Collect values from all sections
            this.collectSettingsFromUI();
            
            // Save to database
            await this.saveToDatabase();
            
            // Save to localStorage for session settings
            localStorage.setItem('coredesk_session_settings', JSON.stringify(this.settings));
            
            // Apply settings immediately
            this.applySettings();
            
            // Update status
            this.unsavedChanges = false;
            const statusElement = this.panel.querySelector('#config-status .status-text');
            if (statusElement) {
                statusElement.textContent = 'Configuración guardada';
                statusElement.style.color = '#28a745';
            }
            
            // Disable save button
            const saveButton = this.panel.querySelector('#config-save-btn');
            if (saveButton) {
                saveButton.disabled = true;
            }
            
            console.log('Config', '[ConfigurationPanel] Settings saved successfully', );
            
            // Show notification
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Configuración guardada exitosamente', 'success');
            }
            
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error saving settings:', error);
            
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Error al guardar configuración', 'error');
            }
        }
    }

    /**
     * Collect settings from UI inputs
     * @private
     */
    collectSettingsFromUI() {
        // Collect from all sections
        Object.keys(this.settings).forEach(sectionId => {
            Object.keys(this.settings[sectionId]).forEach(key => {
                const input = this.panel.querySelector(`#setting-${key.replace(/_/g, '-')}`);
                if (input) {
                    let value;
                    
                    if (input.type === 'checkbox') {
                        value = input.checked;
                    } else if (input.type === 'radio' && input.checked) {
                        value = input.value;
                    } else if (input.type === 'number') {
                        value = parseInt(input.value);
                    } else {
                        value = input.value;
                    }
                    
                    this.settings[sectionId][key] = value;
                }
            });
        });
        
        // Special handling for theme
        const themeRadio = this.panel.querySelector('input[name="theme"]:checked');
        if (themeRadio) {
            this.settings.appearance.theme = themeRadio.value;
        }
    }

    /**
     * Save settings to database
     * @private
     */
    async saveToDatabase() {
        if (!window.electronAPI || !window.electronAPI.executeQuery) {
            console.warn('Config', '[ConfigurationPanel] Database not available', );
            return;
        }
        
        try {
            // Save each setting to database
            for (const [category, settings] of Object.entries(this.settings)) {
                for (const [key, value] of Object.entries(settings)) {
                    await window.electronAPI.executeQuery(
                        'INSERT OR REPLACE INTO settings (category, key, value) VALUES (?, ?, ?)',
                        [category, key, JSON.stringify(value)]
                    );
                }
            }
            
            console.log('Config', '[ConfigurationPanel] Settings saved to database', );
            
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error saving to database:', error);
            throw error;
        }
    }

    /**
     * Apply settings immediately
     * @private
     */
    applySettings() {
        // Apply theme
        if (this.settings.appearance.theme) {
            this.applyTheme(this.settings.appearance.theme);
        }
        
        // Apply font size
        if (this.settings.appearance.font_size) {
            this.applyFontSize(this.settings.appearance.font_size);
        }
        
        // Apply UI scale
        if (this.settings.appearance.ui_scale) {
            this.applyUIScale(this.settings.appearance.ui_scale);
        }
        
        // Apply language (would require page reload in real implementation)
        if (this.settings.general.language) {
            document.documentElement.lang = this.settings.general.language;
        }
        
        // Update sync service configuration
        if (window.dataSyncService) {
            window.dataSyncService.updateConfig({
                syncIntervalMs: this.settings.sync.interval_ms,
                conflictResolutionStrategy: this.settings.sync.conflict_strategy,
                enableOfflineQueue: this.settings.sync.offline_queue
            });
        }
    }

    /**
     * Cancel changes and reload original settings
     * @private
     */
    async cancelChanges() {
        if (this.unsavedChanges) {
            const confirmed = confirm('¿Deseas descartar los cambios no guardados?');
            if (!confirmed) {
                return;
            }
        }
        
        // Reload settings
        await this.loadSettings();
        
        // Re-render current section
        this.renderSection(this.currentSection);
        
        // Reset unsaved changes flag
        this.unsavedChanges = false;
        
        const statusElement = this.panel.querySelector('#config-status .status-text');
        if (statusElement) {
            statusElement.textContent = 'Cambios descartados';
            statusElement.style.color = '#6c757d';
        }
    }

    /**
     * Reset all settings to defaults
     * @private
     */
    async resetToDefaults() {
        const confirmed = confirm('¿Estás seguro de que quieres restaurar todas las configuraciones a los valores predeterminados?');
        if (!confirmed) {
            return;
        }
        
        try {
            // Reset to defaults
            this.settings = JSON.parse(JSON.stringify(this.defaultSettings));
            
            // Save to database
            await this.saveToDatabase();
            
            // Apply settings
            this.applySettings();
            
            // Re-render current section
            this.renderSection(this.currentSection);
            
            // Reset unsaved changes flag
            this.unsavedChanges = false;
            
            const statusElement = this.panel.querySelector('#config-status .status-text');
            if (statusElement) {
                statusElement.textContent = 'Configuración restaurada';
                statusElement.style.color = '#28a745';
            }
            
            console.log('Config', '[ConfigurationPanel] Settings reset to defaults', );
            
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Configuración restaurada a valores predeterminados', 'success');
            }
            
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error resetting to defaults:', error);
            
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Error al restaurar configuración', 'error');
            }
        }
    }

    /**
     * Apply theme
     * @param {string} theme - Theme name
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        console.log("Component", `[ConfigurationPanel] Theme applied: ${theme}`);
    }

    /**
     * Apply font size
     * @param {number} fontSize - Font size in pixels
     */
    applyFontSize(fontSize) {
        document.documentElement.style.setProperty('--base-font-size', `${fontSize}px`);
        console.log("Component", `[ConfigurationPanel] Font size applied: ${fontSize}px`);
    }

    /**
     * Apply UI scale
     * @param {number} scale - Scale percentage
     */
    applyUIScale(scale) {
        document.documentElement.style.setProperty('--ui-scale', `${scale / 100}`);
        console.log("Component", `[ConfigurationPanel] UI scale applied: ${scale}%`);
    }

    // Additional utility methods...

    /**
     * Show Firebase configuration modal
     * @private
     */
    showFirebaseConfigModal() {
        // This would open a detailed Firebase configuration modal
        console.log('Config', '[ConfigurationPanel] Firebase config modal would open here', );
        
        if (window.coreDesk && window.coreDesk.showNotification) {
            window.coreDesk.showNotification('Configuración de Firebase próximamente disponible', 'info');
        }
    }

    /**
     * Update sync status display
     * @private
     */
    updateSyncStatus() {
        if (!window.dataSyncService) return;
        
        const status = window.dataSyncService.getSyncStatus();
        
        const syncStatusElement = this.panel.querySelector('#current-sync-status');
        const lastSyncElement = this.panel.querySelector('#last-sync-time');
        const pendingOpsElement = this.panel.querySelector('#pending-operations');
        
        if (syncStatusElement) {
            syncStatusElement.textContent = status.syncEnabled ? 'Habilitada' : 'Deshabilitada';
        }
        
        if (lastSyncElement) {
            lastSyncElement.textContent = status.lastSyncTimestamp ? 
                new Date(status.lastSyncTimestamp).toLocaleString() : 'Nunca';
        }
        
        if (pendingOpsElement) {
            pendingOpsElement.textContent = status.queueSize.toString();
        }
    }

    /**
     * Update license information
     * @private
     */
    updateLicenseInfo() {
        if (!window.licenseManager) return;
        
        const licenseStatus = window.licenseManager.getLicenseStatus();
        
        const statusBadge = this.panel.querySelector('#license-status-badge');
        const licenseDetails = this.panel.querySelector('#license-details');
        
        if (statusBadge) {
            if (licenseStatus.isActivated) {
                statusBadge.textContent = 'Licencia Activa';
                statusBadge.className = 'status-badge active';
            } else {
                statusBadge.textContent = 'Sin Licencia';
                statusBadge.className = 'status-badge inactive';
            }
        }
        
        if (licenseDetails && licenseStatus.license) {
            licenseDetails.innerHTML = `
                <div class="license-detail">
                    <span>Tipo:</span>
                    <span>${this.formatLicenseType(licenseStatus.license.type)}</span>
                </div>
                <div class="license-detail">
                    <span>Clave:</span>
                    <span>${licenseStatus.license.key}</span>
                </div>
                ${licenseStatus.license.email ? `
                    <div class="license-detail">
                        <span>Email:</span>
                        <span>${licenseStatus.license.email}</span>
                    </div>
                ` : ''}
                ${licenseStatus.license.expiresAt ? `
                    <div class="license-detail">
                        <span>Vence:</span>
                        <span>${new Date(licenseStatus.license.expiresAt).toLocaleDateString()}</span>
                    </div>
                ` : ''}
            `;
        }
    }

    /**
     * Update device information
     * @private
     */
    updateDeviceInfo() {
        if (!window.licenseManager || !window.licenseManager.licenseValidator) return;
        
        const deviceSummary = window.licenseManager.licenseValidator.getDeviceSummary();
        
        const deviceIdElement = this.panel.querySelector('#device-id');
        const devicePlatformElement = this.panel.querySelector('#device-platform');
        const deviceActivationsElement = this.panel.querySelector('#device-activations');
        
        if (deviceIdElement && deviceSummary.fingerprint) {
            deviceIdElement.textContent = deviceSummary.fingerprint;
        }
        
        if (devicePlatformElement && deviceSummary.platform) {
            devicePlatformElement.textContent = deviceSummary.platform;
        }
        
        if (deviceActivationsElement) {
            // This would need to be calculated based on actual license data
            deviceActivationsElement.textContent = '1/1';
        }
    }

    /**
     * Update system information
     * @private
     */
    updateSystemInfo() {
        // Update database size
        this.calculateDatabaseSize().then(size => {
            const element = this.panel.querySelector('#database-size');
            if (element) {
                element.textContent = `${size} MB`;
            }
        });
        
        // Update cache usage
        const cacheElement = this.panel.querySelector('#cache-usage');
        if (cacheElement) {
            // This would calculate actual cache usage
            cacheElement.textContent = '0 MB';
        }
    }

    /**
     * Calculate database size
     * @returns {Promise<number>} Database size in MB
     * @private
     */
    async calculateDatabaseSize() {
        try {
            if (window.electronAPI && window.electronAPI.executeQuery) {
                // This would calculate actual database size
                return 2.5; // Placeholder
            }
            return 0;
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error calculating database size:', error);
            return 0;
        }
    }

    /**
     * Clear application cache
     * @private
     */
    clearCache() {
        const confirmed = confirm('¿Estás seguro de que quieres limpiar el caché? Esto puede afectar el rendimiento temporalmente.');
        if (!confirmed) return;
        
        try {
            // Clear localStorage cache items
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('coredesk_cache_')) {
                    localStorage.removeItem(key);
                }
            });
            
            console.log('Config', '[ConfigurationPanel] Cache cleared', );
            
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Caché limpiado exitosamente', 'success');
            }
            
            // Update cache usage display
            this.updateSystemInfo();
            
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error clearing cache:', error);
            
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Error al limpiar caché', 'error');
            }
        }
    }

    /**
     * Export system logs
     * @private
     */
    exportLogs() {
        try {
            // This would export actual system logs
            const logs = {
                timestamp: new Date().toISOString(),
                version: 'v2.0.0',
                logs: [
                    '[INFO] System started',
                    '[INFO] Configuration panel opened',
                    // ... more logs
                ]
            };
            
            const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `coredesk-logs-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            
            console.log('Config', '[ConfigurationPanel] Logs exported', );
            
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Logs exportados exitosamente', 'success');
            }
            
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error exporting logs:', error);
            
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Error al exportar logs', 'error');
            }
        }
    }

    /**
     * Run system diagnostics
     * @private
     */
    runSystemDiagnostics() {
        console.log('Config', '[ConfigurationPanel] Running system diagnostics...', );
        
        // This would run comprehensive system diagnostics
        const diagnostics = {
            timestamp: new Date().toISOString(),
            system: {
                version: 'v2.0.0',
                platform: navigator.platform,
                userAgent: navigator.userAgent
            },
            database: {
                connected: !!window.electronAPI,
                size: '2.5 MB'
            },
            sync: {
                enabled: window.dataSyncService?.getSyncStatus().syncEnabled || false,
                cloudEnabled: window.dataSyncService?.getSyncStatus().cloudSyncEnabled || false
            },
            license: {
                activated: window.licenseManager?.getLicenseStatus().isActivated || false
            }
        };
        
        console.log('Config', '[ConfigurationPanel] Diagnostics:', diagnostics);
        
        if (window.coreDesk && window.coreDesk.showNotification) {
            window.coreDesk.showNotification('Diagnósticos completados - revisar consola', 'info');
        }
    }

    /**
     * Deactivate current license
     * @private
     */
    async deactivateLicense() {
        const confirmed = confirm('¿Estás seguro de que quieres desactivar la licencia actual?');
        if (!confirmed) return;
        
        try {
            if (window.licenseManager) {
                const result = await window.licenseManager.deactivateLicense();
                
                if (result.success) {
                    this.updateLicenseInfo();
                    
                    if (window.coreDesk && window.coreDesk.showNotification) {
                        window.coreDesk.showNotification('Licencia desactivada exitosamente', 'success');
                    }
                } else {
                    if (window.coreDesk && window.coreDesk.showNotification) {
                        window.coreDesk.showNotification(`Error al desactivar licencia: ${result.error}`, 'error');
                    }
                }
            }
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error deactivating license:', error);
            
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Error al desactivar licencia', 'error');
            }
        }
    }

    /**
     * Check license status
     * @private
     */
    async checkLicense() {
        try {
            if (window.licenseManager) {
                // This would trigger a license validation
                console.log('Config', '[ConfigurationPanel] Checking license...', );
                
                // Update license info
                this.updateLicenseInfo();
                
                if (window.coreDesk && window.coreDesk.showNotification) {
                    window.coreDesk.showNotification('Verificación de licencia completada', 'success');
                }
            }
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error checking license:', error);
            
            if (window.coreDesk && window.coreDesk.showNotification) {
                window.coreDesk.showNotification('Error al verificar licencia', 'error');
            }
        }
    }

    /**
     * Format license type for display
     * @param {string} type - License type
     * @returns {string} Formatted type
     * @private
     */
    formatLicenseType(type) {
        const types = {
            trial: 'Demo Gratuita',
            standard: 'Estándar',
            professional: 'Profesional',
            enterprise: 'Empresarial'
        };
        
        return types[type] || 'Desconocida';
    }

    /**
     * Get current settings
     * @returns {Object} Current settings
     */
    getSettings() {
        return { ...this.settings };
    }

    /**
     * Get configuration panel status
     * @returns {Object} Panel status
     */
    getStatus() {
        return {
            isVisible: this.isVisible,
            currentSection: this.currentSection,
            unsavedChanges: this.unsavedChanges,
            sections: this.sections.map(s => s.id)
        };
    }

    /**
     * Select CoreDesk directory
     * @private
     */
    async selectCoreDeskDirectory() {
        try {
            // For now, we'll prompt for manual entry
            // In a real implementation, this would open a directory picker dialog
            const currentPath = this.settings.general.coredesk_directory || '';
            const newPath = prompt('Ingrese la ruta del directorio de CoreDesk:', currentPath);
            
            if (newPath && newPath.trim() !== '') {
                // Validate and set the new path
                if (window.electronAPI && window.electronAPI.fileSystem) {
                    const result = await window.electronAPI.fileSystem.setCoreDeskPath(newPath.trim());
                    if (result.success) {
                        // Update the input field
                        const input = this.panel.querySelector('#setting-coredesk-directory');
                        if (input) {
                            input.value = result.newPath;
                            this.settings.general.coredesk_directory = result.newPath;
                            this.markUnsavedChanges();
                        }
                        
                        this.showNotification('Directorio de CoreDesk actualizado correctamente', 'success');
                    } else {
                        this.showNotification('Error al configurar el directorio: ' + result.error, 'error');
                    }
                } else {
                    // Fallback for when API is not available
                    const input = this.panel.querySelector('#setting-coredesk-directory');
                    if (input) {
                        input.value = newPath.trim();
                        this.settings.general.coredesk_directory = newPath.trim();
                        this.markUnsavedChanges();
                    }
                }
            }
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error selecting CoreDesk directory:', error);
            this.showNotification('Error al seleccionar directorio', 'error');
        }
    }

    /**
     * Reset CoreDesk directory to default
     * @private
     */
    async resetCoreDeskDirectory() {
        try {
            if (window.electronAPI && window.electronAPI.fileSystem) {
                // Get the default path
                const homeResult = await window.electronAPI.fileSystem.getHomeDirectory();
                if (homeResult.success) {
                    const defaultPath = homeResult.path + (navigator.platform.indexOf('Win') !== -1 ? '\\coredesk' : '/coredesk');
                    
                    // Set the default path
                    const result = await window.electronAPI.fileSystem.setCoreDeskPath(defaultPath);
                    if (result.success) {
                        // Update the input field
                        const input = this.panel.querySelector('#setting-coredesk-directory');
                        if (input) {
                            input.value = result.newPath;
                            this.settings.general.coredesk_directory = result.newPath;
                            this.markUnsavedChanges();
                        }
                        
                        this.showNotification('Directorio restablecido al valor predeterminado', 'success');
                    } else {
                        this.showNotification('Error al restablecer el directorio: ' + result.error, 'error');
                    }
                }
            }
        } catch (error) {
            console.error('Config', '[ConfigurationPanel] Error resetting CoreDesk directory:', error);
            this.showNotification('Error al restablecer directorio', 'error');
        }
    }

    /**
     * Initialize CoreDesk directory setting with current value
     * @private
     */
    async initializeCoreDeskDirectorySetting() {
        try {
            // Wait for the main process to be ready
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (window.electronAPI && window.electronAPI.fileSystem && window.electronAPI.fileSystem.getCoreDeskPath && !this.settings.general.coredesk_directory) {
                try {
                    const result = await window.electronAPI.fileSystem.getCoreDeskPath();
                    if (result && result.success) {
                        this.settings.general.coredesk_directory = result.path;
                        
                        // Update the input field if it exists
                        const input = this.panel?.querySelector('#setting-coredesk-directory');
                        if (input) {
                            input.value = result.path;
                        }
                    }
                } catch (error) {
                    console.warn('Config', '[ConfigurationPanel] Error getting CoreDesk path:', error);
                }
            }
        } catch (error) {
            console.warn('Config', '[ConfigurationPanel] Error initializing CoreDesk directory setting:', error);
        }
    }

    /**
     * Set up updates section event listeners
     * @private
     */
    setupUpdatesEventListeners() {
        // Check updates now button
        const checkUpdatesBtn = this.panel.querySelector('#check-updates-now');
        if (checkUpdatesBtn) {
            checkUpdatesBtn.addEventListener('click', async () => {
                try {
                    checkUpdatesBtn.disabled = true;
                    checkUpdatesBtn.innerHTML = '<span class="btn-icon">⏳</span>Verificando...';
                    
                    if (window.electronAPI && window.electronAPI.updateCheck) {
                        const result = await window.electronAPI.updateCheck();
                        if (result.success) {
                            this.showNotification('Verificación de actualizaciones completada', 'success');
                        } else {
                            this.showNotification('Error al verificar actualizaciones: ' + result.error, 'error');
                        }
                    } else {
                        this.showNotification('Sistema de actualizaciones no disponible', 'warning');
                    }
                } catch (error) {
                    console.error('Error checking for updates:', error);
                    this.showNotification('Error al verificar actualizaciones', 'error');
                } finally {
                    checkUpdatesBtn.disabled = false;
                    checkUpdatesBtn.innerHTML = '<span class="btn-icon">🔍</span>Verificar Actualizaciones Ahora';
                }
            });
        }

        // View update history button
        const viewHistoryBtn = this.panel.querySelector('#view-update-history');
        if (viewHistoryBtn) {
            viewHistoryBtn.addEventListener('click', () => {
                // TODO: Implement update history dialog
                this.showNotification('Historial de actualizaciones - Próximamente', 'info');
            });
        }

        // Auto-check dependency - disable interval when auto-check is off
        const autoCheckbox = this.panel.querySelector('#setting-auto-check');
        const intervalSelect = this.panel.querySelector('#setting-check-interval');
        
        if (autoCheckbox && intervalSelect) {
            autoCheckbox.addEventListener('change', () => {
                intervalSelect.disabled = !autoCheckbox.checked;
                this.markUnsavedChanges();
            });
        }

        // Auto-download dependency - auto-install requires auto-download
        const autoDownloadCheckbox = this.panel.querySelector('#setting-auto-download');
        const autoInstallCheckbox = this.panel.querySelector('#setting-auto-install');
        
        if (autoDownloadCheckbox && autoInstallCheckbox) {
            autoDownloadCheckbox.addEventListener('change', () => {
                if (!autoDownloadCheckbox.checked) {
                    autoInstallCheckbox.checked = false;
                    autoInstallCheckbox.disabled = true;
                } else {
                    autoInstallCheckbox.disabled = false;
                }
                this.markUnsavedChanges();
            });
        }

        // Channel change handler
        const channelSelect = this.panel.querySelector('#setting-update-channel');
        if (channelSelect) {
            channelSelect.addEventListener('change', async () => {
                const newChannel = channelSelect.value;
                try {
                    if (window.electronAPI && window.electronAPI.updateSetChannel) {
                        const result = await window.electronAPI.updateSetChannel(newChannel);
                        if (result.success) {
                            this.showNotification(`Canal de actualizaciones cambiado a: ${newChannel}`, 'success');
                        } else {
                            this.showNotification('Error al cambiar el canal de actualizaciones', 'error');
                        }
                    }
                    this.markUnsavedChanges();
                } catch (error) {
                    console.error('Error changing update channel:', error);
                    this.showNotification('Error al cambiar el canal', 'error');
                }
            });
        }
    }

    /**
     * Render updates settings section
     * @returns {string} HTML content
     * @private
     */
    renderUpdatesSection() {
        return `
            <div class="config-section">
                <h3>Configuración de Actualizaciones</h3>
                <p class="section-description">Gestiona cómo CoreDesk verifica y aplica actualizaciones</p>
                
                <div class="setting-group">
                    <h4>Verificación de Actualizaciones</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-auto-check" data-setting="updates.auto_check" class="setting-checkbox">
                            Verificar Actualizaciones Automáticamente
                        </label>
                        <span class="setting-description">Verificar automáticamente nuevas versiones en segundo plano</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Frecuencia de Verificación</label>
                        <select id="setting-check-interval" data-setting="updates.check_interval_hours" class="setting-select">
                            <option value="1">Cada hora</option>
                            <option value="4">Cada 4 horas</option>
                            <option value="12">Cada 12 horas</option>
                            <option value="24">Cada 24 horas</option>
                            <option value="168">Cada semana</option>
                        </select>
                        <span class="setting-description">Con qué frecuencia verificar actualizaciones</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Canal de Actualizaciones</label>
                        <select id="setting-update-channel" data-setting="updates.channel" class="setting-select">
                            <option value="stable">Estable</option>
                            <option value="beta">Beta</option>
                            <option value="development">Desarrollo</option>
                        </select>
                        <span class="setting-description">Qué tipo de actualizaciones recibir</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">Entorno de Descarga</label>
                        <select id="setting-update-environment" data-setting="updates.environment" class="setting-select">
                            <option value="production">Producción (https://api.coredeskpro.com)</option>
                            <option value="development">Desarrollo (localhost:3000)</option>
                        </select>
                        <span class="setting-description">Desde dónde descargar las actualizaciones</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Descarga e Instalación</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-auto-download" data-setting="updates.auto_download" class="setting-checkbox">
                            Descargar Automáticamente
                        </label>
                        <span class="setting-description">Descargar actualizaciones automáticamente cuando estén disponibles</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-auto-install" data-setting="updates.auto_install" class="setting-checkbox">
                            Instalar Automáticamente
                        </label>
                        <span class="setting-description">Instalar actualizaciones automáticamente al cerrar la aplicación</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Notificaciones</h4>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-notify-available" data-setting="updates.notify_available" class="setting-checkbox">
                            Notificar Actualizaciones Disponibles
                        </label>
                        <span class="setting-description">Mostrar notificación cuando hay actualizaciones disponibles</span>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="setting-notify-downloaded" data-setting="updates.notify_downloaded" class="setting-checkbox">
                            Notificar Descarga Completada
                        </label>
                        <span class="setting-description">Mostrar notificación cuando la descarga esté completa</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>Acciones Manuales</h4>
                    
                    <div class="setting-item">
                        <button id="check-updates-now" class="btn btn-primary">
                            <span class="btn-icon">🔍</span>
                            Verificar Actualizaciones Ahora
                        </button>
                        <span class="setting-description">Verificar manualmente si hay actualizaciones disponibles</span>
                    </div>
                    
                    <div class="setting-item">
                        <button id="view-update-history" class="btn btn-secondary">
                            <span class="btn-icon">📋</span>
                            Ver Historial de Actualizaciones
                        </button>
                        <span class="setting-description">Ver el historial de actualizaciones instaladas</span>
                    </div>
                </div>
            </div>
        `;
    }
}

// Create global instance
window.configurationPanel = new ConfigurationPanel();

console.log('Config', '[ConfigurationPanel] Class defined and global instance created successfully', );