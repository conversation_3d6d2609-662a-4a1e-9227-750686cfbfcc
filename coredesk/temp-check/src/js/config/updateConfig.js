/**
 * Update Configuration Service
 * Handles environment-specific update settings for CoreDesk
 * 
 * Supports:
 * - Development and production URLs
 * - Update channel configuration
 * - Auto-update preferences
 * - Environment detection
 */

require('dotenv').config();

class UpdateConfig {
    constructor() {
        // Environment detection
        this.environment = process.env.NODE_ENV || 'development';
        this.isDevelopment = this.environment === 'development';
        
        // Update server URLs
        this.updateServers = {
            development: process.env.UPDATE_SERVER_DEV || 'http://localhost:3000',
            production: process.env.UPDATE_SERVER_PROD || 'https://coredeskpro.com'
        };
        
        // Update configuration
        this.config = {
            // Environment-specific server URL
            serverUrl: this.updateServers[this.environment],
            
            // Update channels
            channel: process.env.UPDATE_CHANNEL || 'stable',
            availableChannels: ['stable', 'beta', 'development'],
            
            // Update checking
            checkInterval: parseInt(process.env.UPDATE_CHECK_INTERVAL) || 14400000, // 4 hours
            autoCheck: true,
            
            // Download and installation
            autoDownload: process.env.AUTO_DOWNLOAD_UPDATES === 'true',
            autoInstallOnQuit: process.env.AUTO_INSTALL_ON_QUIT === 'true',
            
            // User notifications
            notifyUser: process.env.NOTIFY_USER_UPDATES !== 'false',
            silentUpdates: false,
            
            // Development-specific settings
            enableInDevelopment: false,
            
            // Electron-builder publish configuration
            publishConfig: {
                provider: 'generic',
                url: this.updateServers.production + '/download'
            }
        };
        
        // Override settings for development
        if (this.isDevelopment) {
            this.config.checkInterval = 60000; // 1 minute for testing
            this.config.enableInDevelopment = true;
        }
    }

    /**
     * Get current environment
     */
    getEnvironment() {
        return this.environment;
    }

    /**
     * Check if running in development
     */
    isDev() {
        return this.isDevelopment;
    }

    /**
     * Get update server URL for current environment
     */
    getUpdateServerUrl() {
        return this.config.serverUrl;
    }

    /**
     * Get update server URL for specific environment
     */
    getUpdateServerUrlFor(env) {
        return this.updateServers[env] || this.updateServers.development;
    }

    /**
     * Get current update channel
     */
    getChannel() {
        return this.config.channel;
    }

    /**
     * Set update channel
     */
    setChannel(channel) {
        if (!this.config.availableChannels.includes(channel)) {
            throw new Error(`Invalid channel: ${channel}. Available: ${this.config.availableChannels.join(', ')}`);
        }
        this.config.channel = channel;
        this.saveConfig();
    }

    /**
     * Get available update channels
     */
    getAvailableChannels() {
        return [...this.config.availableChannels];
    }

    /**
     * Get check interval in milliseconds
     */
    getCheckInterval() {
        return this.config.checkInterval;
    }

    /**
     * Set check interval
     */
    setCheckInterval(interval) {
        if (interval < 60000) { // Minimum 1 minute
            throw new Error('Check interval must be at least 60000ms (1 minute)');
        }
        this.config.checkInterval = interval;
        this.saveConfig();
    }

    /**
     * Get auto-download setting
     */
    isAutoDownloadEnabled() {
        return this.config.autoDownload;
    }

    /**
     * Set auto-download setting
     */
    setAutoDownload(enabled) {
        this.config.autoDownload = enabled;
        this.saveConfig();
    }

    /**
     * Get auto-install setting
     */
    isAutoInstallEnabled() {
        return this.config.autoInstallOnQuit;
    }

    /**
     * Set auto-install setting
     */
    setAutoInstall(enabled) {
        this.config.autoInstallOnQuit = enabled;
        this.saveConfig();
    }

    /**
     * Get user notification setting
     */
    isNotifyUserEnabled() {
        return this.config.notifyUser;
    }

    /**
     * Set user notification setting
     */
    setNotifyUser(enabled) {
        this.config.notifyUser = enabled;
        this.saveConfig();
    }

    /**
     * Get silent updates setting
     */
    isSilentUpdatesEnabled() {
        return this.config.silentUpdates;
    }

    /**
     * Set silent updates setting
     */
    setSilentUpdates(enabled) {
        this.config.silentUpdates = enabled;
        this.saveConfig();
    }

    /**
     * Check if updates are enabled in development
     */
    isEnabledInDevelopment() {
        return this.config.enableInDevelopment;
    }

    /**
     * Get publish configuration for electron-builder
     */
    getPublishConfig() {
        return this.config.publishConfig;
    }

    /**
     * Get full configuration object
     */
    getConfig() {
        return {
            ...this.config,
            environment: this.environment,
            isDevelopment: this.isDevelopment,
            updateServers: this.updateServers
        };
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig) {
        // Validate configuration
        if (newConfig.channel && !this.config.availableChannels.includes(newConfig.channel)) {
            throw new Error(`Invalid channel: ${newConfig.channel}`);
        }
        
        if (newConfig.checkInterval && newConfig.checkInterval < 60000) {
            throw new Error('Check interval must be at least 60000ms (1 minute)');
        }
        
        // Update configuration
        this.config = { ...this.config, ...newConfig };
        this.saveConfig();
    }

    /**
     * Reset configuration to defaults
     */
    resetConfig() {
        this.config = {
            serverUrl: this.updateServers[this.environment],
            channel: 'stable',
            checkInterval: 14400000,
            autoDownload: true,
            autoInstallOnQuit: true,
            notifyUser: true,
            silentUpdates: false,
            enableInDevelopment: false,
            publishConfig: {
                provider: 'generic',
                url: this.updateServers.production + '/download'
            }
        };
        
        if (this.isDevelopment) {
            this.config.checkInterval = 60000;
            this.config.enableInDevelopment = true;
        }
        
        this.saveConfig();
    }

    /**
     * Save configuration to localStorage (renderer process)
     */
    saveConfig() {
        if (typeof window !== 'undefined' && window.localStorage) {
            try {
                localStorage.setItem('coredesk-update-config', JSON.stringify(this.config));
            } catch (error) {
                console.warn('[UpdateConfig] Failed to save config to localStorage:', error);
            }
        }
    }

    /**
     * Load configuration from localStorage (renderer process)
     */
    loadConfig() {
        if (typeof window !== 'undefined' && window.localStorage) {
            try {
                const saved = localStorage.getItem('coredesk-update-config');
                if (saved) {
                    const parsedConfig = JSON.parse(saved);
                    this.config = { ...this.config, ...parsedConfig };
                }
            } catch (error) {
                console.warn('[UpdateConfig] Failed to load config from localStorage:', error);
            }
        }
    }

    /**
     * Get API endpoints for update checking
     */
    getApiEndpoints() {
        const baseUrl = this.config.serverUrl;
        return {
            checkUpdates: `${baseUrl}/updates/api/check`,
            downloadUpdate: `${baseUrl}/download`,
            getReleases: `${baseUrl}/updates`,
            getChangelog: `${baseUrl}/updates/changelog`,
            subscribe: `${baseUrl}/updates/notify`
        };
    }

    /**
     * Validate configuration
     */
    validateConfig() {
        const errors = [];
        
        if (!this.config.serverUrl) {
            errors.push('Update server URL is required');
        }
        
        if (!this.config.availableChannels.includes(this.config.channel)) {
            errors.push(`Invalid channel: ${this.config.channel}`);
        }
        
        if (this.config.checkInterval < 60000) {
            errors.push('Check interval must be at least 60000ms');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Get configuration summary for logging
     */
    getConfigSummary() {
        return {
            environment: this.environment,
            serverUrl: this.config.serverUrl,
            channel: this.config.channel,
            checkInterval: this.config.checkInterval,
            autoDownload: this.config.autoDownload,
            autoInstall: this.config.autoInstallOnQuit,
            notifyUser: this.config.notifyUser
        };
    }
}

// Create singleton instance
const updateConfig = new UpdateConfig();

// Load saved configuration on initialization
updateConfig.loadConfig();

// Make available globally
if (typeof window !== 'undefined') {
    window.updateConfig = updateConfig;
}

module.exports = updateConfig;