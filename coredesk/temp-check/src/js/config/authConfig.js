/**
 * Global CoreDesk Authentication Configuration
 * This file provides the core authentication configuration and constants
 * as specified in the PRD section 3.2
 */

// TokenManager will be available globally
// const tokenManager = require('../auth/TokenManager');

// Global authentication configuration object
window.CoreDeskAuth = {
    environment: (typeof process !== 'undefined' && process.env && process.env.NODE_ENV) || 'production', // Force production environment for remote API
    api: {
        baseUrl: 'https://api.coredeskpro.com/v1', // Correct production API URL
        endpoints: {
            login: '/auth/login',
            loginWithLicense: '/auth/login-with-license',
            logout: '/auth/logout',
            refresh: '/auth/refresh-token',
            profile: '/auth/profile'
        }
    },
    storage: {
        keys: {
            token: 'coredesk_token',
            user: 'coredesk_user',
            refreshToken: 'coredesk_refresh_token',
            tokenExpiry: 'coredesk_token_expiry'
        }
    },
    session: {
        tokenRefreshInterval: 15 * 60 * 1000, // 15 minutes
        maxInactivityTime: 30 * 60 * 1000, // 30 minutes
        rememberMeDuration: 30 * 24 * 60 * 60 * 1000 // 30 days
    },
    events: {
        onLogin: 'coredesk:auth:login',
        onLogout: 'coredesk:auth:logout',
        onTokenRefresh: 'coredesk:auth:token-refresh',
        onSessionExpired: 'coredesk:auth:session-expired'
    }
};

// Authentication utility functions
window.CoreDeskAuth.utils = {
    /**
     * Check if user is currently authenticated
     * @returns {boolean} True if authenticated
     */
    isAuthenticated() {
        // CRITICAL: If we're in the middle of logout, always return false
        if (window._isLoggingOut) {
            console.log('authConfig', '[isAuthenticated] LOGOUT IN PROGRESS - returning false');
            return false;
        }
        
        console.log('authConfig', '[isAuthenticated] Checking authentication status...');
        
        // Primary check using TokenManager if available
        if (window.tokenManager) {
            const isValid = window.tokenManager.isTokenValid();
            console.log('authConfig', '[isAuthenticated] TokenManager check result:', isValid);
            return isValid;
        }
        
        // Fallback check for stored token
        console.log('authConfig', '[isAuthenticated] TokenManager not available, using fallback...');
        const token = localStorage.getItem('coredesk_token');
        const expiry = localStorage.getItem('coredesk_token_expiry');
        
        console.log('authConfig', '[isAuthenticated] Token exists:', !!token);
        console.log('authConfig', '[isAuthenticated] Expiry exists:', !!expiry);
        
        if (!token || !expiry) {
            console.log('authConfig', '[isAuthenticated] Missing token or expiry - not authenticated');
            return false;
        }
        
        // Handle both ISO string and numeric timestamp formats
        let expiryTime;
        if (expiry.includes('T') || expiry.includes('-')) {
            // ISO string format
            expiryTime = new Date(expiry).getTime();
        } else {
            // Numeric timestamp
            expiryTime = parseInt(expiry);
        }
        
        const now = new Date().getTime();
        const isValid = !isNaN(expiryTime) && now < expiryTime;
        
        console.log('authConfig', '[isAuthenticated] Expiry time:', new Date(expiryTime));
        console.log('authConfig', '[isAuthenticated] Current time:', new Date(now));
        console.log('authConfig', '[isAuthenticated] Token is valid:', isValid);
        
        return isValid;
    },

    /**
     * Get current user data from localStorage
     * @returns {Object|null} User object or null if not found
     */
    getCurrentUser() {
        try {
            const userData = localStorage.getItem(window.CoreDeskAuth.storage.keys.user);
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('authConfig', 'Error parsing user data:', error);
            return null;
        }
    },

    /**
     * Get current authentication token
     * @returns {string|null} Token string or null if not found
     */
    getToken() {
        if (window.tokenManager) {
            return window.tokenManager.getToken();
        }
        // Fallback - try to get encrypted token and decrypt manually
        const encryptedToken = localStorage.getItem('coredesk_token');
        if (!encryptedToken) return null;
        
        // Simple browser decryption for fallback
        if (encryptedToken.startsWith('browser:')) {
            try {
                return atob(encryptedToken.substring(8));
            } catch (error) {
                console.warn('authConfig: Failed to decrypt token:', error);
                return null;
            }
        }
        return encryptedToken; // Return as-is if not encrypted
    },

    /**
     * Clear all authentication data from localStorage
     */
    clearAuthData() {
        if (window.tokenManager) {
            window.tokenManager.clearTokens();
        } else {
            // Fallback manual cleanup
            localStorage.removeItem('coredesk_token');
            localStorage.removeItem('coredesk_refresh_token');
            localStorage.removeItem('coredesk_token_expiry');
        }
        localStorage.removeItem(window.CoreDeskAuth.storage.keys.user);
    },

    /**
     * Store authentication data in localStorage
     * @param {Object} authData - Authentication data object
     */
    storeAuthData(authData) {
        // CRITICAL: Check for persistent logout protection before storing any auth data
        const persistentLogoutFlag = localStorage.getItem('_forceLogoutActive');
        const logoutTimestamp = localStorage.getItem('_logoutTimestamp');
        const now = Date.now();
        
        // If logout was forced within the last 10 seconds, refuse to store auth data
        if (persistentLogoutFlag === 'true' || (logoutTimestamp && (now - parseInt(logoutTimestamp)) < 10000)) {
            console.log('authConfig.storeAuthData: BLOCKED - Persistent logout protection active, refusing to store auth data');
            return;
        }
        
        // CRITICAL: Don't store auth data if logout is in progress
        if (window._isLoggingOut) {
            console.log('authConfig.storeAuthData: BLOCKED - logout in progress, refusing to store auth data');
            return;
        }
        
        // Store tokens using TokenManager
        if (window.tokenManager) {
            window.tokenManager.storeTokens(authData);
        } else {
            console.error('authConfig: TokenManager not available, cannot store tokens');
            // Fallback - store tokens directly (not recommended, but with protection)
            if (authData.token) {
                localStorage.setItem('coredesk_token', authData.token);
            }
            if (authData.expiresIn) {
                const expiryTime = new Date(Date.now() + (authData.expiresIn * 1000));
                localStorage.setItem('coredesk_token_expiry', expiryTime.toISOString());
            }
            if (authData.refreshToken) {
                localStorage.setItem('coredesk_refresh_token', authData.refreshToken);
            }
        }
        
        // Store user data separately (also protected)
        if (authData.user) {
            localStorage.setItem(window.CoreDeskAuth.storage.keys.user, JSON.stringify(authData.user));
        }
    },

    /**
     * Create authorization headers for API requests
     * @returns {Object} Headers object with Authorization
     */
    getAuthHeaders() {
        const token = this.getToken();
        if (!token) {
            return {};
        }
        
        return {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    },

    /**
     * Check if token needs refresh (within 5 minutes of expiry)
     * @returns {boolean} True if token needs refresh
     */
    needsTokenRefresh() {
        if (window.tokenManager) {
            return window.tokenManager.needsRefresh();
        }
        // Fallback check
        const expiry = localStorage.getItem('coredesk_token_expiry');
        if (!expiry) return false;
        
        const expiryTime = new Date(expiry).getTime();
        const timeUntilExpiry = expiryTime - Date.now();
        const fiveMinutes = 5 * 60 * 1000;
        
        return timeUntilExpiry > 0 && timeUntilExpiry < fiveMinutes;
    }
};

// Initialize authentication state on load
document.addEventListener('DOMContentLoaded', () => {
    console.log('authConfig', '[CoreDeskAuth] Configuration initialized');
    
    // IMPORTANT: Authentication checking is now handled centrally by AuthGuard
    // This prevents race conditions and conflicting authentication checks
    
    console.log('authConfig', '[CoreDeskAuth] Deferring authentication to AuthGuard - preventing race conditions');
    
    // Only initialize dependencies - no authentication checking
    setTimeout(() => {
        // Ensure dependencies are available
        if (window.unifiedAuthManager) {
            window.unifiedAuthManager.initializeDependencies();
        }
        
        console.log('authConfig', '[CoreDeskAuth] Dependencies initialized - AuthGuard will handle authentication');
    }, 50); // Smaller delay just for dependency initialization
});

console.log('authConfig', '[CoreDeskAuth] Global configuration loaded successfully', );