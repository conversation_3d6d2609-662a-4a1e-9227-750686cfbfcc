/**
 * Firebase Configuration
 * Initializes Firebase app and exports configured services
 */

// Import the functions you need from the SDKs
const { initializeApp } = require('firebase/app');
const { getAuth, connectAuthEmulator } = require('firebase/auth');
const { getFirestore, connectFirestoreEmulator } = require('firebase/firestore');
const { getStorage, connectStorageEmulator } = require('firebase/storage');
const { getAnalytics } = require('firebase/analytics');

// Firebase configuration from environment variables
const firebaseConfig = {
    apiKey: process.env.FIREBASE_API_KEY || '',
    authDomain: process.env.FIREBASE_AUTH_DOMAIN || '',
    projectId: process.env.FIREBASE_PROJECT_ID || '',
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET || '',
    messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || '',
    appId: process.env.FIREBASE_APP_ID || '',
    measurementId: process.env.FIREBASE_MEASUREMENT_ID || ''
};

// Initialize Firebase
let app;
let auth;
let firestore;
let storage;
let analytics;

try {
    // Initialize Firebase app
    app = initializeApp(firebaseConfig);
    console.log('Firebase', '[Firebase] App initialized successfully');

    // Initialize services
    auth = getAuth(app);
    firestore = getFirestore(app);
    storage = getStorage(app);
    
    // Analytics is only available in browser environment
    if (typeof window !== 'undefined' && window.location && window.location.protocol !== 'file:') {
        try {
            analytics = getAnalytics(app);
            console.log('Firebase', '[Firebase] Analytics initialized');
        } catch (error) {
            console.warn('Firebase', '[Firebase] Analytics not available in this environment');
        }
    }

    // Connect to emulators in development mode
    if (process.env.NODE_ENV === 'development' && process.env.USE_FIREBASE_EMULATORS === 'true') {
        try {
            // Auth emulator
            if (process.env.FIREBASE_AUTH_EMULATOR_URL) {
                connectAuthEmulator(auth, process.env.FIREBASE_AUTH_EMULATOR_URL);
                console.log('Firebase', '[Firebase] Connected to Auth emulator');
            }

            // Firestore emulator
            if (process.env.FIRESTORE_EMULATOR_HOST) {
                const [host, port] = process.env.FIRESTORE_EMULATOR_HOST.split(':');
                connectFirestoreEmulator(firestore, host, parseInt(port));
                console.log('Firebase', '[Firebase] Connected to Firestore emulator');
            }

            // Storage emulator
            if (process.env.FIREBASE_STORAGE_EMULATOR_HOST) {
                const [host, port] = process.env.FIREBASE_STORAGE_EMULATOR_HOST.split(':');
                connectStorageEmulator(storage, host, parseInt(port));
                console.log('Firebase', '[Firebase] Connected to Storage emulator');
            }
        } catch (error) {
            console.error('Firebase', '[Firebase] Error connecting to emulators:', error);
        }
    }

} catch (error) {
    console.error('Firebase', '[Firebase] Initialization error:', error);
    throw new Error(`Firebase initialization failed: ${error.message}`);
}

// Firebase service configurations
const firestoreSettings = {
    // Enable offline persistence
    cacheSizeBytes: 50 * 1024 * 1024, // 50MB cache
    experimentalForceLongPolling: false,
    experimentalAutoDetectLongPolling: true
};

// Apply Firestore settings
if (firestore) {
    try {
        // Note: Settings should be applied before any other Firestore operations
        // Offline persistence is automatically enabled in Electron environments
        console.log('Firebase', '[Firebase] Firestore configured with offline persistence');
    } catch (error) {
        console.warn('Firebase', '[Firebase] Could not configure Firestore settings:', error);
    }
}

// Helper functions
const isFirebaseInitialized = () => {
    return !!app;
};

const getFirebaseApp = () => {
    if (!app) {
        throw new Error('Firebase app not initialized');
    }
    return app;
};

const getFirebaseAuth = () => {
    if (!auth) {
        throw new Error('Firebase Auth not initialized');
    }
    return auth;
};

const getFirebaseFirestore = () => {
    if (!firestore) {
        throw new Error('Firebase Firestore not initialized');
    }
    return firestore;
};

const getFirebaseStorage = () => {
    if (!storage) {
        throw new Error('Firebase Storage not initialized');
    }
    return storage;
};

const getFirebaseAnalytics = () => {
    if (!analytics) {
        console.warn('Firebase Analytics not available');
        return null;
    }
    return analytics;
};

// Export configured Firebase services
module.exports = {
    // Firebase app instance
    app,
    
    // Firebase services
    auth,
    firestore,
    storage,
    analytics,
    
    // Helper functions
    isFirebaseInitialized,
    getFirebaseApp,
    getFirebaseAuth,
    getFirebaseFirestore,
    getFirebaseStorage,
    getFirebaseAnalytics,
    
    // Configuration
    firebaseConfig
};

console.log('Firebase', '[Firebase] Configuration module loaded');