/**
 * ProtocolX Module Styles
 * Comprehensive styling for the digital protocol administration module
 */

/* ===================================
   PROTOCOLX MODULE LAYOUT
   =================================== */

.protocolx-module {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--background-primary);
    color: var(--foreground-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    /* Remove absolute positioning to prevent layout conflicts */
    position: relative;
    flex: 1;
}

.protocolx-layout {
    display: flex;
    height: 100%;
    overflow: hidden;
}

/* ===================================
   SIDEBAR NAVIGATION
   =================================== */

.protocolx-sidebar {
    width: 280px;
    min-width: 280px;
    background: var(--background-secondary);
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-primary);
}

.sidebar-header h2 {
    margin: 0 0 16px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.sidebar-menu {
    flex: 1;
    padding: 20px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.menu-item:hover {
    background: var(--background-quaternary);
}

.menu-item.active {
    background: var(--background-tertiary);
    border-left-color: var(--accent-primary);
    color: var(--accent-primary);
}

.menu-item .icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    fill: currentColor;
}

.menu-item .count {
    margin-left: auto;
    background: var(--accent-primary);
    color: var(--background-primary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
}

.sidebar-stats {
    padding: 20px;
    border-top: 1px solid var(--border-primary);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--foreground-secondary);
}

.stat-value {
    font-weight: 600;
    color: var(--accent-primary);
}

/* ===================================
   MAIN CONTENT AREA
   =================================== */

.protocolx-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.main-header {
    padding: 24px 32px;
    border-bottom: 1px solid var(--border-primary);
    background: var(--background-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h1 {
    margin: 0 0 4px 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.header-title p {
    margin: 0;
    color: var(--foreground-secondary);
    font-size: 0.875rem;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* ProtocolX specific main content - only apply when ProtocolX is active */
.protocolx-module .main-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px 32px;
    background: var(--background-primary);
}

/* ===================================
   DASHBOARD CONTENT
   =================================== */

.dashboard-content {
    height: 100%;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    align-content: start;
}

.dashboard-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 24px;
    height: fit-content;
}

.dashboard-card h3 {
    margin: 0 0 20px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

/* Stats Card */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.stat-box {
    text-align: center;
    padding: 16px;
    background: var(--background-tertiary);
    border-radius: 6px;
    border: 1px solid var(--border-secondary);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--foreground-secondary);
}

/* Recent Protocols */
.recent-protocols-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.protocol-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--background-tertiary);
    border-radius: 6px;
    border: 1px solid var(--border-secondary);
    transition: all 0.2s ease;
}

.protocol-item:hover {
    background: var(--background-quaternary);
    border-color: var(--border-primary);
}

.protocol-info {
    flex: 1;
}

.protocol-title {
    font-weight: 600;
    color: var(--foreground-primary);
    margin-bottom: 4px;
}

.protocol-number {
    font-size: 0.875rem;
    color: var(--foreground-secondary);
    margin-bottom: 8px;
}

.protocol-meta {
    display: flex;
    gap: 12px;
    font-size: 0.75rem;
}

.protocol-type, .protocol-status, .protocol-date {
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.protocol-type {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

.protocol-status {
    text-transform: capitalize;
}

.status-draft {
    background: var(--warning-bg);
    color: var(--warning-text);
}

.status-active {
    background: var(--success-bg);
    color: var(--success-text);
}

.status-finalized {
    background: var(--info-bg);
    color: var(--info-text);
}

.status-expired {
    background: var(--error-bg);
    color: var(--error-text);
}

.protocol-date {
    background: var(--background-quaternary);
    color: var(--foreground-secondary);
}

.protocol-actions {
    display: flex;
    gap: 8px;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px 16px;
    background: var(--background-tertiary);
    border: 1px solid var(--border-secondary);
    border-radius: 6px;
    color: var(--foreground-primary);
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
}

.action-btn:hover {
    background: var(--background-quaternary);
    border-color: var(--accent-primary);
    color: var(--accent-primary);
}

.action-btn .icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
}

/* ===================================
   PROTOCOLS VIEW
   =================================== */

.protocols-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.protocols-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
}

.search-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    padding: 8px 12px 8px 36px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--foreground-primary);
    font-size: 0.875rem;
    width: 300px;
}

.search-box input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px var(--accent-secondary);
}

.search-icon {
    position: absolute;
    left: 12px;
    width: 16px;
    height: 16px;
    fill: var(--foreground-secondary);
    pointer-events: none;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--foreground-primary);
    font-size: 0.875rem;
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px var(--accent-secondary);
}

.view-controls {
    display: flex;
    gap: 4px;
}

.view-btn {
    padding: 8px;
    border: 1px solid var(--border-primary);
    background: var(--background-primary);
    color: var(--foreground-secondary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn:hover {
    background: var(--background-tertiary);
    color: var(--foreground-primary);
}

.view-btn.active {
    background: var(--accent-primary);
    color: var(--background-primary);
    border-color: var(--accent-primary);
}

.view-btn svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.protocols-content {
    flex: 1;
    overflow-y: auto;
}

.protocols-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 20px;
}

.protocols-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Protocol Cards */
.protocol-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.protocol-card:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.protocol-header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 16px;
}

.protocol-number {
    font-size: 0.875rem;
    color: var(--foreground-secondary);
    font-weight: 600;
}

.protocol-status-badges {
    display: flex;
    gap: 8px;
}

.status-badge, .type-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
}

.type-badge {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

.protocol-body {
    margin-bottom: 16px;
}

.protocol-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground-primary);
    margin-bottom: 12px;
    line-height: 1.4;
}

.protocol-parties, .protocol-location, .protocol-date {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: var(--foreground-secondary);
    margin-bottom: 6px;
}

.protocol-parties .icon, .protocol-location .icon, .protocol-date .icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.protocol-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid var(--border-secondary);
}

.protocol-stats {
    display: flex;
    gap: 16px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.875rem;
    color: var(--foreground-secondary);
}

.stat-item .icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.protocol-actions {
    display: flex;
    gap: 8px;
}

.protocol-updated {
    font-size: 0.75rem;
    color: var(--foreground-tertiary);
    text-align: center;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid var(--border-secondary);
}

/* ===================================
   SIGNATURES AND EVENTS
   =================================== */

.signatures-list, .expiring-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.signature-item, .expiring-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--background-tertiary);
    border: 1px solid var(--border-secondary);
    border-radius: 6px;
}

.signature-info, .expiring-info {
    flex: 1;
}

.signature-protocol, .expiring-title {
    font-weight: 600;
    color: var(--foreground-primary);
    margin-bottom: 4px;
}

.signature-signer, .expiring-number {
    color: var(--foreground-secondary);
    font-size: 0.875rem;
    margin-bottom: 2px;
}

.signature-meta, .expiring-type {
    color: var(--foreground-tertiary);
    font-size: 0.75rem;
}

.signature-status {
    padding: 4px 12px;
    background: var(--warning-bg);
    color: var(--warning-text);
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
}

.expiring-date {
    text-align: center;
    margin-right: 16px;
}

.date-day {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
}

.date-month {
    font-size: 0.75rem;
    color: var(--foreground-secondary);
    text-transform: uppercase;
}

/* ===================================
   EMPTY STATES
   =================================== */

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--foreground-secondary);
}

.empty-state p {
    font-size: 1.125rem;
    margin: 0;
}

/* ===================================
   PLACEHOLDER CONTENT
   =================================== */

.placeholder-content {
    text-align: center;
    padding: 60px 20px;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    margin: 40px auto;
    max-width: 600px;
}

.placeholder-content h3 {
    margin: 0 0 16px 0;
    color: var(--foreground-primary);
    font-size: 1.5rem;
}

.placeholder-content p {
    margin: 0;
    color: var(--foreground-secondary);
    font-size: 1.125rem;
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .protocols-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
}

@media (max-width: 768px) {
    .protocolx-sidebar {
        width: 240px;
        min-width: 240px;
    }
    
    .main-header {
        padding: 16px 20px;
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .protocolx-module .main-content {
        padding: 16px 20px;
    }
    
    .protocols-toolbar {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .search-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box input {
        width: 100%;
    }
    
    .protocols-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* ===================================
   ACCESSIBILITY
   =================================== */

@media (prefers-reduced-motion: reduce) {
    .protocol-card,
    .menu-item,
    .action-btn,
    .view-btn {
        transition: none;
    }
}

/* Focus styles for keyboard navigation */
.menu-item:focus,
.protocol-card:focus,
.action-btn:focus,
.view-btn:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .protocol-card {
        border-width: 2px;
    }
    
    .status-badge,
    .type-badge {
        border: 1px solid currentColor;
    }
}