/**
 * LexFlow Module Styles
 * Comprehensive styling for the legal case management module
 */

/* ===================================
   LEXFLOW MODULE LAYOUT
   =================================== */

.lexflow-module {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--background-primary);
    color: var(--foreground-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    /* Remove absolute positioning to prevent layout conflicts */
    position: relative;
    flex: 1;
}

.lexflow-layout {
    display: flex;
    height: 100%;
    overflow: hidden;
}

/* ===================================
   SIDEBAR NAVIGATION
   =================================== */

.lexflow-sidebar {
    width: 280px;
    min-width: 280px;
    background: var(--background-secondary);
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-primary);
}

.sidebar-header h2 {
    margin: 0 0 16px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.sidebar-menu {
    flex: 1;
    padding: 20px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.menu-item:hover {
    background: var(--background-quaternary);
}

.menu-item.active {
    background: var(--background-tertiary);
    border-left-color: var(--accent-primary);
    color: var(--accent-primary);
}

.menu-item .icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    fill: currentColor;
}

.menu-item .count {
    margin-left: auto;
    background: var(--accent-primary);
    color: var(--background-primary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
}

.sidebar-stats {
    padding: 20px;
    border-top: 1px solid var(--border-primary);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--foreground-secondary);
}

.stat-value {
    font-weight: 600;
    color: var(--accent-primary);
}

/* ===================================
   MAIN CONTENT AREA
   =================================== */

.lexflow-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.main-header {
    padding: 20px 30px;
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--background-primary);
}

.header-title h1 {
    margin: 0 0 4px 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.header-title p {
    margin: 0;
    color: var(--foreground-secondary);
    font-size: 0.875rem;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* LexFlow specific main content - only apply when LexFlow is active */
.lexflow-module .main-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 30px;
}

/* ===================================
   DASHBOARD STYLES
   =================================== */

.dashboard-content {
    height: 100%;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto 1fr 1fr;
    gap: 20px;
    height: 100%;
}

.dashboard-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 20px;
    overflow: hidden;
}

.dashboard-card h3 {
    margin: 0 0 16px 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

/* Stats Card */
.stats-card {
    grid-column: 1 / -1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.stat-box {
    text-align: center;
    padding: 16px;
    background: var(--background-primary);
    border-radius: 6px;
    border: 1px solid var(--border-primary);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--foreground-secondary);
}

/* Recent Cases Card */
.recent-cases-card {
    grid-row: 2 / 4;
}

.recent-cases-list {
    max-height: 400px;
    overflow-y: auto;
}

.case-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-primary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.case-item:hover {
    background: var(--background-quaternary);
    margin: 0 -12px;
    padding: 12px;
    border-radius: 6px;
}

.case-item:last-child {
    border-bottom: none;
}

.case-info {
    flex: 1;
}

.case-title {
    font-weight: 600;
    color: var(--foreground-primary);
    margin-bottom: 4px;
}

.case-client {
    color: var(--foreground-secondary);
    font-size: 0.875rem;
    margin-bottom: 4px;
}

.case-meta {
    display: flex;
    gap: 8px;
    font-size: 0.75rem;
}

.case-number {
    color: var(--foreground-muted);
}

.case-status, .case-priority {
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: #e3f2fd;
    color: #1976d2;
}

.status-closed {
    background: #f3e5f5;
    color: #7b1fa2;
}

.status-suspended {
    background: #fff3e0;
    color: #f57c00;
}

.priority-low {
    background: #e8f5e8;
    color: #2e7d32;
}

.priority-normal {
    background: #e3f2fd;
    color: #1976d2;
}

.priority-high {
    background: #fff3e0;
    color: #f57c00;
}

.priority-urgent {
    background: #ffebee;
    color: #d32f2f;
}

/* Upcoming Events Card */
.upcoming-events-card {
    grid-row: 2 / 3;
}

.events-list {
    max-height: 300px;
    overflow-y: auto;
}

.event-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-primary);
}

.event-item:last-child {
    border-bottom: none;
}

.event-date {
    text-align: center;
    margin-right: 16px;
    min-width: 50px;
}

.date-day {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
    line-height: 1;
}

.date-month {
    font-size: 0.75rem;
    color: var(--foreground-secondary);
    text-transform: uppercase;
}

.event-info {
    flex: 1;
}

.event-title {
    font-weight: 600;
    color: var(--foreground-primary);
    margin-bottom: 2px;
}

.event-time {
    color: var(--foreground-secondary);
    font-size: 0.875rem;
    margin-bottom: 2px;
}

.event-case {
    color: var(--foreground-muted);
    font-size: 0.75rem;
}

/* Quick Actions Card */
.quick-actions-card {
    grid-row: 3 / 4;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.action-btn {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: var(--background-primary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    color: var(--foreground-primary);
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
}

.action-btn:hover {
    background: var(--background-quaternary);
    border-color: var(--accent-primary);
    color: var(--accent-primary);
}

.action-btn .icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    fill: currentColor;
}

/* ===================================
   CASES VIEW STYLES
   =================================== */

.cases-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.cases-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
}

.search-controls {
    display: flex;
    gap: 12px;
    align-items: center;
    flex: 1;
}

.search-box {
    position: relative;
    min-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--foreground-primary);
    font-size: 0.875rem;
}

.search-box input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(100, 122, 204, 0.1);
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    fill: var(--foreground-muted);
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--foreground-primary);
    font-size: 0.875rem;
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(100, 122, 204, 0.1);
}

.view-controls {
    display: flex;
    gap: 4px;
}

.view-btn {
    padding: 8px;
    border: 1px solid var(--border-primary);
    background: var(--background-primary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn:hover {
    background: var(--background-quaternary);
}

.view-btn.active {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--background-primary);
}

.view-btn svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.cases-content {
    flex: 1;
    overflow-y: auto;
}

/* Cases Grid */
.cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.case-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    cursor: pointer;
}

.case-card:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.case-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--background-primary);
    border-bottom: 1px solid var(--border-primary);
}

.case-number {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Fira Code', Consolas, 'Courier New', monospace;
    font-size: 0.875rem;
    color: var(--foreground-muted);
    font-weight: 600;
}

.case-status-badges {
    display: flex;
    gap: 6px;
}

.status-badge, .priority-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.case-body {
    padding: 20px;
}

.case-title {
    margin: 0 0 8px 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground-primary);
    line-height: 1.4;
}

.case-client {
    display: flex;
    align-items: center;
    color: var(--foreground-secondary);
    font-size: 0.875rem;
    margin-bottom: 6px;
}

.case-client .icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
    fill: currentColor;
}

.case-type {
    color: var(--accent-primary);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 8px;
}

.case-description {
    color: var(--foreground-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}

.case-footer {
    padding: 16px 20px;
    background: var(--background-primary);
    border-top: 1px solid var(--border-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.case-stats {
    display: flex;
    gap: 16px;
}

.stat-item {
    display: flex;
    align-items: center;
    color: var(--foreground-muted);
    font-size: 0.75rem;
}

.stat-item .icon {
    width: 12px;
    height: 12px;
    margin-right: 4px;
    fill: currentColor;
}

.case-actions {
    display: flex;
    gap: 8px;
}

.case-updated {
    position: absolute;
    bottom: 4px;
    right: 12px;
    font-size: 0.75rem;
    color: var(--foreground-muted);
}

/* Cases List View */
.cases-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.cases-list .case-card {
    border-radius: 6px;
}

.cases-list .case-card:hover {
    transform: none;
}

/* ===================================
   EMPTY STATES
   =================================== */

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--foreground-secondary);
}

.empty-state p {
    margin: 0;
    font-size: 1.125rem;
}

/* ===================================
   BUTTONS AND FORMS
   =================================== */

.btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--foreground-primary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 6px;
}

.btn:hover {
    background: var(--background-quaternary);
}

.btn-primary {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--background-primary);
}

.btn-primary:hover {
    background: var(--accent-hover);
    border-color: var(--accent-hover);
}

.btn-secondary {
    background: var(--background-secondary);
    border-color: var(--border-primary);
}

.btn-secondary:hover {
    background: var(--background-quaternary);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.75rem;
}

.btn .icon {
    width: 14px;
    height: 14px;
    fill: currentColor;
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .recent-cases-card {
        grid-row: auto;
    }
    
    .upcoming-events-card {
        grid-row: auto;
    }
    
    .quick-actions-card {
        grid-row: auto;
    }
}

@media (max-width: 768px) {
    .lexflow-sidebar {
        width: 240px;
        min-width: 240px;
    }
    
    .main-header {
        padding: 16px 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .lexflow-module .main-content {
        padding: 16px 20px;
    }
    
    .cases-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .search-controls {
        flex-direction: column;
        gap: 8px;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .cases-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* ===================================
   DARK THEME ADJUSTMENTS
   =================================== */

[data-theme="dark"] .lexflow-module {
    --accent-color-rgb: 100, 181, 246;
}

[data-theme="dark"] .case-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .stat-box {
    background: var(--background-tertiary);
}

/* ===================================
   LOADING AND TRANSITIONS
   =================================== */

.lexflow-module.loading {
    opacity: 0.6;
    pointer-events: none;
}

.lexflow-module.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-primary);
    border-top-color: var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* ===================================
   MODAL STYLES
   =================================== */

.lexflow-modal .modal {
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.lexflow-modal .modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lexflow-modal .modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.lexflow-modal .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--foreground-secondary);
    cursor: pointer;
    padding: 4px;
    line-height: 1;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.lexflow-modal .modal-close:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

.lexflow-modal .modal-body {
    padding: 24px;
}

.lexflow-modal .modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-primary);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* Form Styles */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group-full {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--foreground-primary);
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 8px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--foreground-primary);
    font-size: 0.875rem;
    font-family: inherit;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(100, 122, 204, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* File Input Styles */
.file-input-container {
    position: relative;
}

.file-input-container input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.file-input-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-primary);
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.file-input-display:hover {
    border-color: var(--accent-primary);
}

#file-name {
    color: var(--foreground-secondary);
    font-size: 0.875rem;
}

/* ===================================
   CASE DETAIL STYLES
   =================================== */

.case-detail-view {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.case-detail-header {
    margin-bottom: 24px;
}

.case-info-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.info-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 20px;
}

.info-card h4 {
    margin: 0 0 16px 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-item label {
    font-weight: 500;
    color: var(--foreground-secondary);
    font-size: 0.875rem;
}

.info-item span {
    color: var(--foreground-primary);
    font-size: 0.875rem;
}

.case-description {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 20px;
}

.case-description h4 {
    margin: 0 0 12px 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.case-description p {
    margin: 0;
    color: var(--foreground-primary);
    line-height: 1.5;
}

/* Case Detail Tabs */
.case-detail-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.tab-headers {
    display: flex;
    border-bottom: 1px solid var(--border-primary);
    margin-bottom: 20px;
}

.tab-btn {
    padding: 12px 20px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--foreground-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    color: var(--foreground-primary);
    background: var(--background-quaternary);
}

.tab-btn.active {
    color: var(--accent-primary);
    border-bottom-color: var(--accent-primary);
}

.tab-content {
    flex: 1;
    overflow: hidden;
}

.tab-panel {
    display: none;
    height: 100%;
    overflow-y: auto;
}

.tab-panel.active {
    display: block;
}

/* Documents in Detail View */
.documents-list .document-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.documents-list .document-item:hover {
    border-color: var(--accent-primary);
    background: var(--background-quaternary);
}

.document-icon {
    font-size: 1.5rem;
    margin-right: 16px;
}

.document-info {
    flex: 1;
}

.document-name {
    font-weight: 600;
    color: var(--foreground-primary);
    margin-bottom: 4px;
}

.document-meta {
    color: var(--foreground-secondary);
    font-size: 0.75rem;
}

.document-actions {
    display: flex;
    gap: 8px;
}

/* Events in Detail View */
.events-list .event-item {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    margin-bottom: 12px;
}

.event-date {
    text-align: center;
    margin-right: 16px;
    min-width: 50px;
}

.date-day {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
    line-height: 1;
}

.date-month {
    font-size: 0.75rem;
    color: var(--foreground-secondary);
    text-transform: uppercase;
}

.event-info {
    flex: 1;
}

.event-title {
    font-weight: 600;
    color: var(--foreground-primary);
    margin-bottom: 4px;
}

.event-meta {
    color: var(--foreground-secondary);
    font-size: 0.875rem;
    margin-bottom: 4px;
}

.event-description {
    color: var(--foreground-primary);
    font-size: 0.875rem;
    margin-top: 8px;
}

.event-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: auto;
}

/* Notes Section */
.notes-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.new-note-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
}

.new-note-form textarea {
    min-height: 80px;
    resize: vertical;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 12px;
    background: var(--background-primary);
    color: var(--foreground-primary);
    font-family: inherit;
}

.new-note-form textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(100, 122, 204, 0.1);
}

.notes-list {
    flex: 1;
    overflow-y: auto;
}

.note-item {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
}

.note-content {
    color: var(--foreground-primary);
    line-height: 1.5;
    margin-bottom: 8px;
}

.note-meta {
    color: var(--foreground-secondary);
    font-size: 0.75rem;
}

/* ===================================
   DOCUMENTS VIEW STYLES
   =================================== */

.documents-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.documents-toolbar {
    margin-bottom: 20px;
    padding: 16px 20px;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
}

.documents-grid {
    flex: 1;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.document-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    cursor: pointer;
}

.document-card:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--background-primary);
    border-bottom: 1px solid var(--border-primary);
}

.file-type-icon {
    font-size: 1.5rem;
}

.document-body {
    padding: 20px;
}

.document-name {
    margin: 0 0 8px 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground-primary);
    line-height: 1.4;
}

.document-case {
    color: var(--accent-primary);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 8px;
}

.document-meta {
    color: var(--foreground-secondary);
    font-size: 0.75rem;
    margin-bottom: 8px;
}

.document-description {
    color: var(--foreground-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}

/* ===================================
   EVENTS VIEW STYLES
   =================================== */

.events-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.events-toolbar {
    margin-bottom: 20px;
    padding: 16px 20px;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
}

.events-timeline {
    flex: 1;
    overflow-y: auto;
}

.event-timeline-item {
    display: flex;
    margin-bottom: 24px;
    position: relative;
}

.event-timeline-item::before {
    content: '';
    position: absolute;
    left: 75px;
    top: 0;
    bottom: -24px;
    width: 2px;
    background: var(--border-primary);
}

.event-timeline-item:last-child::before {
    display: none;
}

.event-timeline-item.upcoming::before {
    background: var(--accent-primary);
}

.event-date-marker {
    width: 80px;
    text-align: center;
    margin-right: 20px;
    position: relative;
    z-index: 1;
}

.event-date-marker::after {
    content: '';
    position: absolute;
    right: -11px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--background-secondary);
    border: 2px solid var(--border-primary);
}

.event-timeline-item.upcoming .event-date-marker::after {
    border-color: var(--accent-primary);
    background: var(--accent-primary);
}

.date-day {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
    line-height: 1;
}

.date-month {
    font-size: 0.75rem;
    color: var(--foreground-secondary);
    text-transform: uppercase;
}

.date-year {
    font-size: 0.75rem;
    color: var(--foreground-muted);
}

.event-content {
    flex: 1;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 20px;
}

.event-timeline-item.upcoming .event-content {
    border-color: var(--accent-primary);
    box-shadow: 0 2px 8px rgba(100, 122, 204, 0.1);
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.event-title {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.event-type-badge {
    padding: 4px 8px;
    background: var(--accent-primary);
    color: var(--background-primary);
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.event-details {
    margin-bottom: 12px;
}

.event-time,
.event-location,
.event-case {
    color: var(--foreground-secondary);
    font-size: 0.875rem;
    margin-bottom: 4px;
}

.event-description {
    color: var(--foreground-primary);
    line-height: 1.4;
    margin-bottom: 12px;
}

/* ===================================
   RESPONSIVE ADJUSTMENTS FOR NEW COMPONENTS
   =================================== */

@media (max-width: 768px) {
    .case-info-grid {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .documents-grid {
        grid-template-columns: 1fr;
    }
    
    .event-timeline-item {
        flex-direction: column;
    }
    
    .event-timeline-item::before {
        display: none;
    }
    
    .event-date-marker {
        width: auto;
        margin-right: 0;
        margin-bottom: 12px;
    }
    
    .event-date-marker::after {
        display: none;
    }
}