/* FinSync Module CSS - Placeholder */
/* Este módulo está en desarrollo - CSS básico */

.finsync-container {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 16px;
}

.finsync-header {
    background: linear-gradient(135deg, #fd7e14, #ffc107);
    color: white;
    padding: 24px;
    border-radius: 8px 8px 0 0;
    text-align: center;
}

.finsync-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0;
}

.finsync-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 8px 0 0 0;
}

.finsync-content {
    padding: 32px;
    text-align: center;
    background: white;
    border-radius: 0 0 8px 8px;
}

.finsync-icon {
    font-size: 64px;
    margin-bottom: 24px;
    display: block;
}

.finsync-message {
    font-size: 18px;
    color: #6c757d;
    margin-bottom: 16px;
}

.finsync-features {
    list-style: none;
    padding: 0;
    margin: 24px 0;
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.finsync-features li {
    padding: 8px 0;
    color: #495057;
    font-size: 16px;
    border-bottom: 1px solid #e9ecef;
}

.finsync-features li:before {
    content: "💰 ";
    margin-right: 8px;
}

.coming-soon-badge {
    background: #dc3545;
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
    display: inline-block;
    margin: 16px 0;
}