/* Light theme styles - Already defined in variables.css but this file can contain theme-specific overrides */

[data-theme="light"] {
    /* Additional light theme specific styles can go here */
    
    /* Custom scrollbar for light theme */
    --scrollbar-track: rgba(0, 0, 0, 0.05);
    --scrollbar-thumb: rgba(0, 0, 0, 0.2);
    --scrollbar-thumb-hover: rgba(0, 0, 0, 0.3);
    
    /* Enhanced focus states for light theme */
    --focus-border: #0078d4;
    
    /* Code syntax highlighting colors for light theme */
    --syntax-keyword: #0000ff;
    --syntax-string: #a31515;
    --syntax-comment: #008000;
    --syntax-number: #098658;
    --syntax-function: #795e26;
    --syntax-variable: #001080;
    
    /* Custom component colors */
    --welcome-accent: #0078d4;
    --module-lexflow: #0078d4;
    --module-protocolx: #5856d6;
    --module-auditpro: #30d158;
    --module-finsync: #ff3b30;
}

/* Light theme specific component adjustments */
[data-theme="light"] .app-container {
    background: linear-gradient(135deg, var(--background-primary) 0%, #f8f9fa 100%);
}

[data-theme="light"] .welcome-screen {
    background: radial-gradient(circle at center, rgba(0, 120, 212, 0.05) 0%, var(--background-primary) 70%);
}

[data-theme="light"] .module-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .module-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

[data-theme="light"] .modal {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
}

/* Light theme loading animation */
[data-theme="light"] .loading-spinner {
    border-color: rgba(0, 0, 0, 0.1);
    border-top-color: var(--accent-primary);
}

/* Light theme syntax highlighting for code blocks */
[data-theme="light"] code,
[data-theme="light"] pre {
    background: #f6f8fa;
    color: #24292f;
    border: 1px solid var(--border-primary);
}

/* Light theme form enhancements */
[data-theme="light"] .form-input:focus {
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

/* Light theme status indicators */
[data-theme="light"] .status-online {
    color: var(--success);
}

[data-theme="light"] .status-offline {
    color: var(--error);
}

[data-theme="light"] .status-warning {
    color: var(--warning);
}

/* Light theme notification styles */
[data-theme="light"] .notification {
    background: var(--background-primary);
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .notification.success {
    border-left: 4px solid var(--success);
}

[data-theme="light"] .notification.warning {
    border-left: 4px solid var(--warning);
}

[data-theme="light"] .notification.error {
    border-left: 4px solid var(--error);
}

[data-theme="light"] .notification.info {
    border-left: 4px solid var(--info);
}

/* Light theme button variations */
[data-theme="light"] .btn-outline {
    background: transparent;
    color: var(--accent-primary);
    border: 1px solid var(--accent-primary);
}

[data-theme="light"] .btn-outline:hover {
    background: var(--accent-primary);
    color: #ffffff;
}

/* Light theme table styles */
[data-theme="light"] .table {
    background: var(--background-primary);
}

[data-theme="light"] .table th {
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-primary);
}

[data-theme="light"] .table td {
    border-bottom: 1px solid var(--border-secondary);
}

[data-theme="light"] .table tr:hover {
    background: var(--background-secondary);
}

/* Light theme menu styles */
[data-theme="light"] .dropdown-menu {
    background: var(--background-primary);
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .dropdown-item:hover {
    background: var(--background-secondary);
}

/* Light theme scrollbar */
[data-theme="light"] ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

[data-theme="light"] ::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
}

[data-theme="light"] ::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 6px;
}

[data-theme="light"] ::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
}

/* Light theme selection */
[data-theme="light"] ::selection {
    background: var(--selection-background);
    color: var(--selection-foreground);
}

[data-theme="light"] ::-moz-selection {
    background: var(--selection-background);
    color: var(--selection-foreground);
}

/* Light theme focus styles */
[data-theme="light"] *:focus-visible {
    outline: 2px solid var(--focus-border);
    outline-offset: 2px;
}