/* Dark theme styles - Already defined in variables.css but this file can contain theme-specific overrides */

[data-theme="dark"] {
    /* Additional dark theme specific styles can go here */
    
    /* Custom scrollbar for dark theme */
    --scrollbar-track: rgba(255, 255, 255, 0.05);
    --scrollbar-thumb: rgba(255, 255, 255, 0.2);
    --scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
    
    /* Enhanced focus states for dark theme */
    --focus-border: #007acc;
    
    /* Code syntax highlighting colors for dark theme */
    --syntax-keyword: #569cd6;
    --syntax-string: #ce9178;
    --syntax-comment: #6a9955;
    --syntax-number: #b5cea8;
    --syntax-function: #dcdcaa;
    --syntax-variable: #9cdcfe;
    
    /* Custom component colors */
    --welcome-accent: #0e639c;
    --module-lexflow: #4a90e2;
    --module-protocolx: #7b68ee;
    --module-auditpro: #50c878;
    --module-finsync: #ff6b6b;
}

/* Dark theme specific component adjustments */
[data-theme="dark"] .app-container {
    background: linear-gradient(135deg, var(--background-primary) 0%, #1a1a1a 100%);
}

[data-theme="dark"] .welcome-screen {
    background: radial-gradient(circle at center, rgba(14, 99, 156, 0.1) 0%, var(--background-primary) 70%);
}

[data-theme="dark"] .module-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .module-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .modal {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.6);
}

/* Dark theme scrollbar */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 6px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
}

/* Dark theme selection */
[data-theme="dark"] ::selection {
    background: var(--selection-background);
    color: var(--selection-foreground);
}

[data-theme="dark"] ::-moz-selection {
    background: var(--selection-background);
    color: var(--selection-foreground);
}

/* Dark theme focus styles */
[data-theme="dark"] *:focus-visible {
    outline: 2px solid var(--focus-border);
    outline-offset: 2px;
}

/* Dark theme loading spinner */
[data-theme="dark"] .loading-spinner {
    border-color: rgba(255, 255, 255, 0.1);
    border-top-color: var(--accent-primary);
}

/* Dark theme code blocks */
[data-theme="dark"] code,
[data-theme="dark"] pre {
    background: #2d2d30;
    color: #d4d4d4;
    border: 1px solid var(--border-primary);
}

/* Dark theme form enhancements */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    background: var(--input-background);
    color: var(--input-foreground);
    border: 1px solid var(--input-border);
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
    border-color: var(--border-focus);
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

/* Dark theme status indicators */
[data-theme="dark"] .status-online {
    color: var(--success);
}

[data-theme="dark"] .status-offline {
    color: var(--error);
}

[data-theme="dark"] .status-warning {
    color: var(--warning);
}

/* Dark theme loading animation */
[data-theme="dark"] .loading-spinner {
    border-color: rgba(255, 255, 255, 0.1);
    border-top-color: var(--accent-primary);
}

/* Dark theme syntax highlighting for code blocks */
[data-theme="dark"] code,
[data-theme="dark"] pre {
    background: #1e1e1e;
    color: #d4d4d4;
    border: 1px solid var(--border-primary);
}

/* Dark theme form enhancements */
[data-theme="dark"] .form-input:focus {
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.3);
}

/* Dark theme status indicators */
[data-theme="dark"] .status-online {
    color: var(--success);
}

[data-theme="dark"] .status-offline {
    color: var(--error);
}

[data-theme="dark"] .status-warning {
    color: var(--warning);
}

/* Dark theme notification styles */
[data-theme="dark"] .notification {
    background: var(--background-tertiary);
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .notification.success {
    border-left: 4px solid var(--success);
}

[data-theme="dark"] .notification.warning {
    border-left: 4px solid var(--warning);
}

[data-theme="dark"] .notification.error {
    border-left: 4px solid var(--error);
}

[data-theme="dark"] .notification.info {
    border-left: 4px solid var(--info);
}