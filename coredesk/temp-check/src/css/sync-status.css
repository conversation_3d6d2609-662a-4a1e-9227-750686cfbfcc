/**
 * Sync Status Panel Styles
 * Styles for the data synchronization status and control interface
 */

/* Sync Status Panel */
.sync-status-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    overflow: hidden;
    animation: syncPanelSlideIn 0.3s ease-out;
}

@keyframes syncPanelSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Panel Header */
.sync-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
}

.sync-panel-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
}

.sync-panel-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.sync-panel-close:hover {
    background: var(--hover-bg);
    color: var(--text-color);
}

/* Panel Content */
.sync-panel-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Sync Sections */
.sync-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-light);
}

.sync-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.sync-section h4 {
    margin: 0 0 12px 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Status Rows */
.status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    font-size: 0.9rem;
}

.status-label {
    color: var(--text-muted);
    font-weight: 500;
}

.status-value {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color);
}

/* Status Indicators */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.online {
    background: #28a745;
    box-shadow: 0 0 4px rgba(40, 167, 69, 0.4);
}

.status-indicator.offline {
    background: #dc3545;
    box-shadow: 0 0 4px rgba(220, 53, 69, 0.4);
}

.status-indicator.enabled {
    background: #17a2b8;
    box-shadow: 0 0 4px rgba(23, 162, 184, 0.4);
}

.status-indicator.disabled {
    background: #6c757d;
    box-shadow: 0 0 4px rgba(108, 117, 125, 0.4);
}

.status-indicator.syncing {
    background: #ffc107;
    box-shadow: 0 0 4px rgba(255, 193, 7, 0.4);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Progress Section */
.progress-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--border-light);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #17a2b8, #28a745);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: progressShimmer 1.5s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-color);
    min-width: 40px;
    text-align: right;
}

/* Sync Details */
.sync-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 12px;
}

.sync-detail-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: 4px;
    font-size: 0.85rem;
}

.sync-detail-item span:first-child {
    color: var(--text-muted);
    font-weight: 500;
}

.sync-detail-item span:last-child {
    color: var(--text-color);
}

/* Conflicts Section */
.conflicts-list {
    max-height: 200px;
    overflow-y: auto;
}

.conflict-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: var(--warning-bg);
    border: 1px solid var(--warning-border);
    border-radius: 6px;
    margin-bottom: 8px;
}

.conflict-info {
    flex: 1;
}

.conflict-type {
    display: block;
    font-weight: 600;
    color: var(--warning-text);
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.conflict-description {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    padding: 12px;
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: 6px;
    text-align: center;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 4px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Panel Footer */
.sync-panel-footer {
    padding: 16px 20px;
    background: var(--footer-bg);
    border-top: 1px solid var(--border-color);
}

.sync-controls {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.sync-controls .btn {
    flex: 1;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.sync-controls .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.sync-controls .btn:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn-icon {
    font-size: 1rem;
}

/* Button Variants */
.btn-primary {
    background: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
}

.btn-primary:not(:disabled):hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:not(:disabled):hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sync-status-panel {
        width: 95%;
        max-height: 90vh;
    }
    
    .sync-panel-header,
    .sync-panel-content,
    .sync-panel-footer {
        padding: 12px 16px;
    }
    
    .sync-details {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .sync-controls {
        flex-direction: column;
    }
    
    .sync-controls .btn {
        min-width: auto;
    }
    
    .conflict-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* Dark Mode Specific */
[data-theme="dark"] {
    --warning-bg: rgba(255, 193, 7, 0.1);
    --warning-border: rgba(255, 193, 7, 0.3);
    --warning-text: #ffc107;
}

/* Light Mode Specific */
[data-theme="light"] {
    --warning-bg: rgba(255, 193, 7, 0.1);
    --warning-border: rgba(255, 193, 7, 0.3);
    --warning-text: #856404;
}

/* Loading States */
.sync-status-panel .loading {
    position: relative;
    overflow: hidden;
}

.sync-status-panel .loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Status Panel Trigger Button */
.sync-status-trigger {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 4px;
}

.sync-status-trigger:hover {
    background: var(--hover-bg);
    color: var(--text-color);
}

.sync-status-trigger.syncing {
    color: var(--primary-color);
}

.sync-status-trigger.error {
    color: #dc3545;
}

.sync-status-trigger.success {
    color: #28a745;
}

/* Accessibility */
.sync-status-panel:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

.sync-controls .btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .status-indicator {
        border: 2px solid currentColor;
    }
    
    .progress-fill {
        background: var(--primary-color);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .sync-status-panel {
        animation: none;
    }
    
    .progress-fill::after {
        animation: none;
    }
    
    .status-indicator.syncing {
        animation: none;
    }
    
    .sync-controls .btn:not(:disabled):hover {
        transform: none;
    }
}