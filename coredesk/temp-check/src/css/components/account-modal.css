/**
 * Account Modal Component Styles
 * Styles for the user account management modal
 */

/* Modal container */
.account-modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--modal-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    backdrop-filter: blur(2px);
}

/* Modal */
.account-modal {
    background: var(--modal-background);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    /* Ensure consistent font sizing throughout the modal */
    font-size: var(--font-size-md);
    font-family: var(--font-family-primary);
}

/* Modal header */
.account-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    background: var(--background-secondary);
}

.account-modal-header h2 {
    margin: 0;
    color: var(--foreground-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

.account-modal-close {
    background: none;
    border: none;
    color: var(--foreground-muted);
    font-size: var(--font-size-xxl);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.account-modal-close:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

/* Modal body */
.account-modal-body {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

/* Account sections */
.account-section {
    margin-bottom: var(--spacing-xl);
}

.account-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--foreground-primary);
    margin-bottom: var(--spacing-md);
    display: block;
}

/* Profile section */
.account-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--background-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-secondary);
}

.profile-avatar {
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 64px;
    height: 64px;
    background: var(--background-quaternary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--border-primary);
}

.avatar-icon {
    font-size: var(--icon-size-lg);
    color: var(--foreground-muted);
}

.profile-info {
    flex: 1;
}

.profile-name {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--foreground-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

.profile-email {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--foreground-secondary);
    font-size: var(--font-size-md);
}

.account-type-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--accent-primary);
    color: #ffffff;
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

/* Account settings */
.account-setting-item {
    margin-bottom: var(--spacing-md);
}

.setting-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--foreground-primary);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
}

.setting-input {
    width: 100%;
    padding: var(--spacing-sm);
    background: var(--input-background);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    color: var(--input-foreground);
    font-size: var(--font-size-md);
    transition: all var(--transition-fast);
}

.setting-input:focus {
    outline: none;
    border-color: var(--border-focus);
    background: var(--input-background-focus);
}

.setting-input:read-only {
    background: var(--background-quaternary);
    color: var(--foreground-muted);
    cursor: not-allowed;
}

.setting-help {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--foreground-muted);
    font-size: var(--font-size-xs);
}

/* Checkbox labels */
.setting-label input[type="checkbox"] {
    margin-right: var(--spacing-xs);
}

/* License information */
.license-info {
    background: var(--background-tertiary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
}

.license-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.license-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.license-label {
    color: var(--foreground-secondary);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
}

.license-value {
    color: var(--foreground-primary);
    font-size: var(--font-size-md);
}

.license-value.active {
    color: var(--success);
}

.license-value.inactive {
    color: var(--warning);
}

.license-message {
    margin: var(--spacing-sm) 0 0 0;
    color: var(--foreground-secondary);
    font-size: var(--font-size-md);
    line-height: 1.4;
}

/* Quick actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-sm);
}

.action-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
}

.action-btn.primary {
    background: var(--button-primary-background);
    color: var(--button-primary-foreground);
    border-color: var(--button-primary-background);
}

.action-btn.primary:hover {
    background: var(--button-primary-hover);
    border-color: var(--button-primary-hover);
}

.action-btn.secondary {
    background: var(--button-secondary-background);
    color: var(--button-secondary-foreground);
    border-color: var(--border-primary);
}

.action-btn.secondary:hover {
    background: var(--button-secondary-hover);
    border-color: var(--border-hover);
}

/* Modal footer */
.account-modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    background: var(--background-secondary);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn.btn-primary {
    background: var(--button-primary-background);
    color: var(--button-primary-foreground);
    border-color: var(--button-primary-background);
}

.btn.btn-primary:hover {
    background: var(--button-primary-hover);
    border-color: var(--button-primary-hover);
}

.btn.btn-secondary {
    background: var(--button-secondary-background);
    color: var(--button-secondary-foreground);
    border-color: var(--border-primary);
}

.btn.btn-secondary:hover {
    background: var(--button-secondary-hover);
    border-color: var(--border-hover);
}

/* Theme specific styles */
[data-theme="light"] .account-modal {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .account-modal {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

/* Theme support for avatar icon */
[data-theme="light"] .avatar-icon {
    color: var(--foreground-muted);
}

[data-theme="dark"] .avatar-icon {
    color: var(--foreground-muted);
}

/* Responsive design */
@media (max-width: 768px) {
    .account-modal {
        width: 95%;
        max-height: 90vh;
    }
    
    .account-profile {
        flex-direction: column;
        text-align: center;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .account-modal-footer {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .account-modal-footer .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .account-modal-header,
    .account-modal-body,
    .account-modal-footer {
        padding: var(--spacing-md);
    }
    
    .license-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}