/* Notification Center Styles */

/* Toast Container */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    pointer-events: none;
}

/* Toast Styles */
.toast {
    min-width: 320px;
    max-width: 400px;
    background: var(--panel-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: flex-start;
    gap: 12px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.hiding {
    opacity: 0;
    transform: translateX(100%);
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-color);
}

.toast-info::before {
    background: #2196f3;
}

.toast-success::before {
    background: #4caf50;
}

.toast-warning::before {
    background: #ff9800;
}

.toast-error::before {
    background: #f44336;
}

.toast-icon {
    font-size: 20px;
    line-height: 1;
    flex-shrink: 0;
    margin-top: 2px;
}

.toast-content {
    flex: 1;
    min-width: 0;
}

.toast-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.3;
}

.toast-message {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
    word-wrap: break-word;
}

.toast-close {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.toast-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.toast-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
}

.toast-action {
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toast-action:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Notification Center Overlay */
.notification-center-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.notification-center {
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    background: var(--panel-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Notification Header */
.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.notification-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.notification-header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.btn-link {
    background: none;
    border: none;
    color: var(--accent-color);
    font-size: 13px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-link:hover {
    background: rgba(139, 125, 216, 0.1);
    color: var(--accent-color-light);
}

.close-notification-center {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.close-notification-center:hover {
    background: rgba(255, 107, 107, 0.2);
    border-color: #ff6b6b;
    color: #ff6b6b;
}

/* Notification Filters */
.notification-filters {
    display: flex;
    gap: 8px;
    padding: 15px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    overflow-x: auto;
}

.filter-btn {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.08);
    color: var(--text-primary);
}

.filter-btn.active {
    background: rgba(139, 125, 216, 0.2);
    border-color: var(--accent-color);
    color: var(--text-primary);
}

/* Notification List */
.notification-list {
    flex: 1;
    overflow-y: auto;
    padding: 20px 25px;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(139, 125, 216, 0.3);
}

.notification-item.unread {
    border-left: 3px solid var(--accent-color);
    background: rgba(139, 125, 216, 0.05);
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 20px;
    left: -6px;
    width: 8px;
    height: 8px;
    background: var(--accent-color);
    border-radius: 50%;
}

.notification-icon {
    font-size: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    flex-shrink: 0;
}

.notification-icon.info {
    background: rgba(33, 150, 243, 0.2);
}

.notification-icon.success {
    background: rgba(76, 175, 80, 0.2);
}

.notification-icon.warning {
    background: rgba(255, 152, 0, 0.2);
}

.notification-icon.error {
    background: rgba(244, 67, 54, 0.2);
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.3;
}

.notification-message {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: 8px;
    word-wrap: break-word;
}

.notification-meta {
    display: flex;
    gap: 12px;
    font-size: 11px;
    color: var(--text-secondary);
}

.notification-category {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.notification-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-end;
}

.notification-action {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-action:hover {
    background: rgba(255, 255, 255, 0.2);
}

.notification-close {
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.notification-close:hover {
    background: rgba(255, 107, 107, 0.2);
    border-color: #ff6b6b;
    color: #ff6b6b;
}

/* Empty Notifications */
.empty-notifications {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.empty-notifications h4 {
    font-size: 18px;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.empty-notifications p {
    font-size: 14px;
    margin: 0;
}

/* Notification Settings */
.notification-settings {
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    padding: 20px 25px;
    background: rgba(255, 255, 255, 0.02);
}

.notification-settings h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 15px 0;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.setting-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.setting-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.setting-item span {
    font-size: 13px;
    color: var(--text-primary);
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background: #f44336;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
    }
    
    .toast {
        min-width: auto;
        max-width: none;
    }
    
    .notification-center {
        width: 95%;
        max-height: 90vh;
    }
    
    .notification-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .notification-header-actions {
        justify-content: space-between;
    }
    
    .notification-filters {
        padding: 12px 20px;
    }
    
    .notification-list {
        padding: 15px 20px;
    }
    
    .notification-item {
        padding: 12px;
    }
    
    .notification-settings {
        padding: 15px 20px;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .notification-filters {
        padding: 10px 15px;
    }
    
    .filter-btn {
        padding: 6px 12px;
        font-size: 11px;
    }
    
    .notification-item {
        flex-direction: column;
        gap: 10px;
    }
    
    .notification-icon {
        align-self: flex-start;
    }
    
    .notification-actions {
        flex-direction: row;
        align-self: stretch;
        justify-content: flex-end;
    }
}