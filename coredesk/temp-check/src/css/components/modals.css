/* Welcome screen and modal styles */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--background-primary);
    padding: var(--spacing-xl);
}

.welcome-content {
    max-width: 800px;
    text-align: center;
}

.welcome-content h1 {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--foreground-primary);
    margin-bottom: var(--spacing-md);
}

.welcome-content p {
    font-size: var(--font-size-md);
    color: var(--foreground-muted);
    margin-bottom: var(--spacing-xl);
}

.welcome-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-bottom: var(--spacing-xl);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: var(--button-height-md);
}

.btn-primary {
    background: var(--button-primary-background);
    color: var(--button-primary-foreground);
    border: 1px solid var(--button-primary-background);
}

.btn-primary:hover {
    background: var(--button-primary-hover);
    border-color: var(--button-primary-hover);
}

.btn-secondary {
    background: var(--button-secondary-background);
    color: var(--button-secondary-foreground);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover {
    background: var(--button-secondary-hover);
}

/* Module preview grid */
.module-preview h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--foreground-primary);
    margin-bottom: var(--spacing-md);
}

.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

/* Enhanced loading progress styles */
.loading-progress {
    margin-top: 1rem;
    width: 100%;
    max-width: 300px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--background-secondary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.loading-progress small {
    color: var(--foreground-secondary);
    font-size: 0.8rem;
}

/* Module loading indicator styles */
.no-modules-message {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    min-height: 200px;
}

.no-modules-message .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.no-modules-message .module-icon-large {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.no-modules-message p {
    margin: 0 0 1rem 0;
    color: var(--foreground-primary);
    font-size: 1rem;
    font-weight: 500;
}

/* Fallback modules notice */
.fallback-modules-notice {
    grid-column: 1 / -1;
    background: var(--background-secondary);
    border: 1px solid var(--primary-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.fallback-modules-notice .notice-icon {
    font-size: 1.2rem;
}

.fallback-modules-notice p {
    margin: 0;
    color: var(--foreground-primary);
    font-size: 0.9rem;
}

.module-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    text-align: left;
}

.module-card:hover {
    background: var(--background-tertiary);
    border-color: var(--accent-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.module-card h4 {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--foreground-primary);
    margin-bottom: var(--spacing-xs);
}

.module-card p {
    font-size: var(--font-size-sm);
    color: var(--foreground-muted);
    margin: 0;
}

.module-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: var(--warning);
    color: #000000;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    padding: 2px var(--spacing-xs);
    border-radius: var(--border-radius);
}

/* Modal styles */
.modal {
    background: var(--modal-background);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--foreground-primary);
    margin: 0;
}

.modal-close {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    color: var(--foreground-muted);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    flex: 1;
    overflow: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* Form styles for modals */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--foreground-primary);
    margin-bottom: var(--spacing-xs);
}

.form-input {
    width: 100%;
    padding: var(--spacing-sm);
    background: var(--input-background);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    color: var(--input-foreground);
    font-size: var(--font-size-md);
    transition: border-color var(--transition-fast);
}

.form-input:focus {
    border-color: var(--accent-primary);
    outline: none;
}

.form-select {
    width: 100%;
    padding: var(--spacing-sm);
    background: var(--input-background);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    color: var(--input-foreground);
    font-size: var(--font-size-md);
    cursor: pointer;
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.form-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

/* Responsive modal adjustments */
@media (max-width: 768px) {
    .modal {
        width: 95%;
        max-height: 90vh;
    }
    
    .welcome-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .module-grid {
        grid-template-columns: 1fr;
    }
}

/* Spin animation for loading spinners */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}