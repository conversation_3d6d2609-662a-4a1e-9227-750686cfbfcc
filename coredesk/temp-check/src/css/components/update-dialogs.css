/* Update Dialogs Styles */

/* Dialog Overlay */
.update-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.update-dialog-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Main Dialog */
.update-dialog {
    background: var(--background-primary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    min-width: 480px;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* <PERSON><PERSON> Header */
.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-secondary);
    background: var(--background-secondary);
}

.dialog-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--foreground-primary);
    margin: 0;
}

.dialog-close {
    background: none;
    border: none;
    color: var(--foreground-secondary);
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dialog-close:hover {
    background: var(--background-hover);
    color: var(--foreground-primary);
}

/* Dialog Content */
.dialog-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Update Info Styles */
.update-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.version-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    padding: 16px;
    background: var(--background-tertiary);
    border-radius: 6px;
}

.current-version,
.new-version {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.version-info .label {
    font-size: 12px;
    font-weight: 500;
    color: var(--foreground-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.version-info .version {
    font-size: 18px;
    font-weight: 600;
    color: var(--foreground-primary);
}

.version-info .version.highlight {
    color: var(--accent-primary);
}

.update-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.release-date,
.download-size {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.update-details .label {
    font-size: 13px;
    color: var(--foreground-secondary);
}

.update-details .date,
.update-details .size {
    font-size: 13px;
    color: var(--foreground-primary);
    font-weight: 500;
}

.release-notes {
    margin-top: 8px;
}

.release-notes h4 {
    font-size: 14px;
    margin: 0 0 8px 0;
    color: var(--foreground-primary);
}

.notes-content {
    font-size: 13px;
    line-height: 1.5;
    color: var(--foreground-secondary);
    background: var(--background-tertiary);
    padding: 12px;
    border-radius: 4px;
    border-left: 4px solid var(--accent-primary);
}

/* Download Progress Styles */
.download-progress {
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-height: 120px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-text {
    font-size: 14px;
    color: var(--foreground-primary);
}

.progress-percent {
    font-size: 14px;
    font-weight: 600;
    color: var(--accent-primary);
}

.progress-bar-container {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--background-tertiary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-primary-hover));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.download-stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--foreground-secondary);
}

/* Install Ready Styles */
.install-ready {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.success-icon {
    font-size: 48px;
    margin-bottom: 8px;
}

.install-ready h3 {
    font-size: 18px;
    margin: 0;
    color: var(--foreground-primary);
}

.install-ready p {
    font-size: 14px;
    color: var(--foreground-secondary);
    margin: 8px 0;
}

.install-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    text-align: left;
    margin-top: 16px;
}

.install-options .option {
    padding: 12px;
    background: var(--background-tertiary);
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
}

.install-options .option strong {
    color: var(--foreground-primary);
}

/* Update Error Styles */
.update-error {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 8px;
}

.error-message h3 {
    font-size: 18px;
    margin: 0;
    color: var(--error);
}

.error-message p {
    font-size: 14px;
    color: var(--foreground-secondary);
    margin: 8px 0;
}

.error-details {
    text-align: left;
    margin-top: 16px;
    padding: 12px;
    background: var(--background-tertiary);
    border-radius: 6px;
    border-left: 4px solid var(--error);
}

.error-details strong {
    color: var(--foreground-primary);
    font-size: 13px;
}

.error-details code {
    display: block;
    margin-top: 8px;
    padding: 8px;
    background: var(--background-primary);
    border: 1px solid var(--border-secondary);
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    color: var(--error);
    word-break: break-word;
}

.error-suggestions {
    text-align: left;
    margin-top: 16px;
}

.error-suggestions p {
    margin-bottom: 8px;
    font-size: 13px;
}

.error-suggestions ul {
    margin: 0;
    padding-left: 20px;
}

.error-suggestions li {
    font-size: 13px;
    color: var(--foreground-secondary);
    margin-bottom: 4px;
}

/* Dialog Actions */
.dialog-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding: 16px 20px;
    border-top: 1px solid var(--border-secondary);
    background: var(--background-secondary);
}

.dialog-btn {
    padding: 8px 16px;
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.dialog-btn.btn-primary {
    background: var(--accent-primary);
    color: #ffffff;
    border-color: var(--accent-primary);
}

.dialog-btn.btn-primary:hover {
    background: var(--accent-primary-hover);
    border-color: var(--accent-primary-hover);
}

.dialog-btn.btn-secondary {
    background: var(--background-primary);
    color: var(--foreground-primary);
    border-color: var(--border-primary);
}

.dialog-btn.btn-secondary:hover {
    background: var(--background-hover);
    border-color: var(--border-hover);
}

.dialog-btn.btn-default {
    background: var(--background-tertiary);
    color: var(--foreground-secondary);
    border-color: var(--border-secondary);
}

.dialog-btn.btn-default:hover {
    background: var(--background-hover);
    color: var(--foreground-primary);
    border-color: var(--border-primary);
}

/* Update Notifications (Toasts) */
.update-notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 400px;
}

.update-toast {
    background: var(--background-primary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    padding: 16px;
    animation: toastSlideIn 0.3s ease-out;
    position: relative;
}

.update-toast.info {
    border-left: 4px solid var(--info);
}

.update-toast.success {
    border-left: 4px solid var(--success);
}

.update-toast.warning {
    border-left: 4px solid var(--warning);
}

.update-toast.error {
    border-left: 4px solid var(--error);
}

.update-toast.fade-out {
    animation: toastFadeOut 0.3s ease-in forwards;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes toastFadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.toast-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-right: 24px;
}

.toast-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--foreground-primary);
}

.toast-message {
    font-size: 13px;
    color: var(--foreground-secondary);
    line-height: 1.4;
}

.toast-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.toast-btn {
    padding: 4px 12px;
    border: 1px solid var(--border-primary);
    background: var(--background-secondary);
    color: var(--foreground-primary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
}

.toast-btn:hover {
    background: var(--background-hover);
    border-color: var(--border-hover);
}

.toast-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    color: var(--foreground-secondary);
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-close:hover {
    background: var(--background-hover);
    color: var(--foreground-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .update-dialog {
        min-width: 90vw;
        max-width: 90vw;
        margin: 20px;
    }
    
    .version-info,
    .update-details {
        grid-template-columns: 1fr;
    }
    
    .update-notifications {
        left: 20px;
        right: 20px;
        max-width: none;
    }
    
    .dialog-actions {
        flex-direction: column;
    }
    
    .dialog-actions .dialog-btn {
        width: 100%;
        justify-content: center;
    }
}