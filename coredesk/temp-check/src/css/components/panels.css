/* Panel specific styles */

/* Explorer panel */
.panel-explorer {
    /* Styles specific to explorer panel */
}

.file-tree {
    list-style: none;
    margin: 0;
    padding: 0;
}

.file-tree-item {
    display: flex;
    align-items: center;
    padding: 2px var(--spacing-xs);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: background var(--transition-fast);
}

.file-tree-item:hover {
    background: var(--background-quaternary);
}

.file-tree-item.selected {
    background: var(--selection-background);
    color: var(--selection-foreground);
}

.file-tree-icon {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
}

/* Cloud panel */
.panel-cloud {
    /* Styles specific to cloud panel */
}

.cloud-status {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--background-tertiary);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
}

.storage-quota {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.quota-bar {
    width: 100%;
    height: 6px;
    background: var(--background-quaternary);
    border-radius: 3px;
    overflow: hidden;
}

.quota-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success) 0%, var(--warning) 80%, var(--error) 100%);
    transition: width var(--transition-normal);
}

.quota-text {
    font-size: var(--font-size-xs);
    color: var(--foreground-muted);
}

/* Search panel */
.panel-search {
    /* Styles specific to search panel */
}

.search-box {
    width: 100%;
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    background: var(--input-background);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    color: var(--input-foreground);
    font-size: var(--font-size-sm);
}

.search-results {
    list-style: none;
    margin: 0;
    padding: 0;
}

.search-result-item {
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-secondary);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.search-result-item:hover {
    background: var(--background-quaternary);
}

.search-result-title {
    font-weight: var(--font-weight-medium);
    color: var(--foreground-primary);
    margin-bottom: var(--spacing-xs);
}

.search-result-path {
    font-size: var(--font-size-xs);
    color: var(--foreground-muted);
}

/* Modules panel */
.panel-modules {
    /* Styles specific to modules panel */
}

.module-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.module-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--background-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.module-item:hover {
    background: var(--background-quaternary);
    border-color: var(--accent-primary);
}

.module-item.active {
    background: var(--accent-primary);
    color: #ffffff;
    border-color: var(--accent-primary);
}

.module-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.module-icon {
    width: 24px;
    height: 24px;
    margin-right: var(--spacing-sm);
}

.module-info {
    flex: 1;
}

.module-name {
    font-weight: var(--font-weight-medium);
    margin-bottom: 2px;
}

.module-description {
    font-size: var(--font-size-xs);
    opacity: 0.8;
}

/* Extensions panel */
.panel-extensions {
    /* Styles specific to extensions panel */
}

.extension-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.extension-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background: var(--background-tertiary);
    border-radius: var(--border-radius);
}

.extension-info {
    flex: 1;
}

.extension-name {
    font-weight: var(--font-weight-medium);
    margin-bottom: 2px;
}

.extension-version {
    font-size: var(--font-size-xs);
    color: var(--foreground-muted);
}

.extension-toggle {
    width: 40px;
    height: 20px;
    background: var(--background-quaternary);
    border-radius: 10px;
    position: relative;
    cursor: pointer;
    transition: background var(--transition-fast);
}

.extension-toggle.enabled {
    background: var(--accent-primary);
}

.extension-toggle::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: #ffffff;
    border-radius: 50%;
    transition: transform var(--transition-fast);
}

.extension-toggle.enabled::after {
    transform: translateX(20px);
}

/* Panel resize handles */
.panel-resize-handle {
    position: absolute;
    background: transparent;
    z-index: 15;
    transition: background var(--transition-fast);
}

.panel-resize-handle.horizontal {
    top: 0;
    bottom: 0;
    width: 6px;
    cursor: ew-resize;
}

.panel-resize-handle.vertical {
    left: 0;
    right: 0;
    height: 6px;
    cursor: ns-resize;
}

.left-panel .panel-resize-handle {
    right: -3px;
    /* Ensure resize handle works with absolute positioning */
    z-index: 12;
}

.right-panel .panel-resize-handle {
    left: -3px;
}

.bottom-panel .panel-resize-handle {
    top: -3px;
}

.panel-resize-handle:hover {
    background: var(--accent-primary);
}

.panel-resize-handle:active {
    background: var(--accent-primary-hover);
}

/* Panel resizing state */
body.panel-resizing {
    user-select: none;
}

body.panel-resizing * {
    pointer-events: none;
}

body.panel-resizing .panel-resize-handle {
    pointer-events: auto;
    background: var(--accent-primary);
}

/* Visual feedback for resize zones */
.panel-resize-handle::before {
    content: '';
    position: absolute;
    background: var(--accent-primary);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.panel-resize-handle.horizontal::before {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 20px;
    border-radius: 1px;
}

.panel-resize-handle.vertical::before {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 2px;
    border-radius: 1px;
}

.panel-resize-handle:hover::before {
    opacity: 0.7;
}

.panel-resize-handle:active::before {
    opacity: 1;
}

/* Panel width constraints */
.side-panel {
    min-width: 200px;
    max-width: 800px;
    position: relative;
}

/* Left panel overlay positioning */
.side-panel.left-panel {
    /* Override position for overlay behavior */
    position: absolute !important;
    top: 0;
    left: var(--activitybar-width);
    bottom: 0;
    z-index: 10;
    /* Ensure proper sizing for overlay */
    height: 100%;
}

/* Right panel maintains normal flex behavior */
.side-panel.right-panel {
    position: relative;
    z-index: 1;
}

.bottom-panel {
    min-height: 100px;
    max-height: 600px;
    position: relative;
}

/* Panel animation states */
.panel-opening {
    animation: panelSlideIn var(--transition-normal) ease-out;
}

.panel-closing {
    animation: panelSlideOut var(--transition-normal) ease-in;
}

@keyframes panelSlideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes panelSlideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(-100%);
        opacity: 0;
    }
}

/* Right panel specific animations */
.right-panel.panel-opening {
    animation: panelSlideInRight var(--transition-normal) ease-out;
}

.right-panel.panel-closing {
    animation: panelSlideOutRight var(--transition-normal) ease-in;
}

@keyframes panelSlideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes panelSlideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Bottom panel specific animations */
.bottom-panel.panel-opening {
    animation: panelSlideInUp var(--transition-normal) ease-out;
}

.bottom-panel.panel-closing {
    animation: panelSlideOutDown var(--transition-normal) ease-in;
}

@keyframes panelSlideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes panelSlideOutDown {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(100%);
        opacity: 0;
    }
}