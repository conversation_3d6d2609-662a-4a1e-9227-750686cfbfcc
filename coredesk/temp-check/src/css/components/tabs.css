/* Tab system styling */
.tab-bar {
    height: var(--tab-height);
    background: var(--tab-inactive-background);
    border-bottom: 1px solid var(--tab-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    /* Ensure no unwanted spacing */
    margin: 0;
    padding: 0;
    border-left: none;
    border-right: none;
    border-top: none;
}

.tab-list {
    display: flex;
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
    /* Ensure no unwanted spacing */
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    align-items: stretch;
}

.tab-list::-webkit-scrollbar {
    display: none;
}

.tab {
    display: flex;
    align-items: center;
    min-width: 120px;
    max-width: 200px;
    height: 100%;
    padding: 0 var(--spacing-md);
    background: var(--tab-inactive-background);
    border-right: 1px solid var(--tab-border);
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: all var(--transition-fast);
    white-space: nowrap;
    overflow: hidden;
}

.tab:hover {
    background: var(--tab-hover-background);
}

.tab.active {
    background: var(--tab-active-background);
    border-bottom: 1px solid var(--tab-active-background);
    margin-bottom: -1px;
}

.tab.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--accent-primary);
}

/* Tab content */
.tab-icon {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
    flex-shrink: 0;
}

.tab-title {
    flex: 1;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--foreground-secondary);
}

.tab.active .tab-title {
    color: var(--foreground-primary);
    font-weight: var(--font-weight-medium);
}

.tab-close {
    width: 16px;
    height: 16px;
    margin-left: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    color: var(--foreground-muted);
    transition: all var(--transition-fast);
    flex-shrink: 0;
    opacity: 0;
}

.tab:hover .tab-close,
.tab.active .tab-close {
    opacity: 1;
}

.tab-close:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

/* Unsaved changes indicator */
.tab.has-changes .tab-close {
    opacity: 1;
}

.tab.has-changes .tab-close::before {
    content: '●';
    font-size: 12px;
    color: var(--warning);
}

.tab.has-changes .tab-close:hover::before {
    content: '×';
    color: var(--foreground-primary);
}

/* Tab controls */
.tab-controls {
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-sm);
    border-left: 1px solid var(--tab-border);
}

.new-tab-button {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    color: var(--foreground-muted);
    font-size: 16px;
    font-weight: var(--font-weight-normal);
    transition: all var(--transition-fast);
}

.new-tab-button:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

/* Tab content area */
.tab-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.tab-pane {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-fast);
}

.tab-pane.active {
    opacity: 1;
    pointer-events: all;
}

/* Tab scrolling controls */
.tab-scroll-left,
.tab-scroll-right {
    width: 24px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tab-inactive-background);
    border: none;
    color: var(--foreground-muted);
    cursor: pointer;
    transition: all var(--transition-fast);
    opacity: 0;
    pointer-events: none;
}

.tab-bar.scrollable .tab-scroll-left,
.tab-bar.scrollable .tab-scroll-right {
    opacity: 1;
    pointer-events: all;
}

.tab-scroll-left:hover,
.tab-scroll-right:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

.tab-scroll-left {
    border-right: 1px solid var(--tab-border);
}

.tab-scroll-right {
    border-left: 1px solid var(--tab-border);
}

/* Tab drag and drop */
.tab.dragging {
    opacity: 0.5;
    z-index: 1000;
}

.tab-drop-indicator {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--accent-primary);
    z-index: 999;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.tab-drop-indicator.visible {
    opacity: 1;
}

/* Tab context menu */
.tab-context-menu {
    position: fixed;
    background: var(--background-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: var(--spacing-xs) 0;
    z-index: var(--z-dropdown);
    min-width: 150px;
}

.tab-context-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.tab-context-item:hover {
    background: var(--background-quaternary);
}

.tab-context-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.tab-context-item.disabled:hover {
    background: transparent;
}

.tab-context-separator {
    height: 1px;
    background: var(--border-primary);
    margin: var(--spacing-xs) 0;
}

/* Module-specific tab styling */
.tab[data-module="lexflow"] {
    border-top: 2px solid transparent;
}

.tab[data-module="lexflow"].active {
    border-top-color: #4a90e2;
}

.tab[data-module="protocolx"].active {
    border-top-color: #7b68ee;
}

.tab[data-module="auditpro"].active {
    border-top-color: #50c878;
}

.tab[data-module="finsync"].active {
    border-top-color: #ff6b6b;
}

/* Tab animations */
.tab-opening {
    animation: tabSlideIn var(--transition-normal) ease-out;
}

.tab-closing {
    animation: tabSlideOut var(--transition-normal) ease-in;
}

@keyframes tabSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes tabSlideOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* Responsive tab adjustments */
@media (max-width: 768px) {
    .tab {
        min-width: 100px;
        padding: 0 var(--spacing-sm);
    }
    
    .tab-title {
        font-size: var(--font-size-xs);
    }
    
    .new-tab-button {
        width: 20px;
        height: 20px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .tab {
        min-width: 80px;
        max-width: 120px;
    }
    
    .tab-icon {
        display: none;
    }
}