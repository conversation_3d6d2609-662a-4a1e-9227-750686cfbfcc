/**
 * Configuration Panel Styles
 * Comprehensive styling for the CoreDesk configuration panel
 */

.config-panel-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--modal-overlay);
    backdrop-filter: blur(5px);
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    box-sizing: border-box;
}

.config-panel {
    width: 90%;
    max-width: 1200px;
    height: 85vh;
    background: var(--modal-background);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid var(--border-primary);
    /* Ensure consistent font sizing throughout the modal */
    font-size: var(--font-size-md);
    font-family: var(--font-family-primary);
}

/* Header */
.config-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-primary);
}

.config-panel-header h2 {
    margin: 0;
    color: var(--foreground-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

.config-panel-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--foreground-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: all var(--transition-normal);
}

.config-panel-close:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

/* Body */
.config-panel-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* Sidebar Navigation */
.config-sidebar {
    width: 250px;
    background: var(--background-secondary);
    border-right: 1px solid var(--border-primary);
    overflow-y: auto;
}

.config-nav {
    padding: 1rem 0;
}

.config-nav-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-lg);
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    transition: all var(--transition-normal);
    color: var(--foreground-secondary);
    font-size: var(--font-size-md);
}

.config-nav-item:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

.config-nav-item.active {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.nav-icon {
    margin-right: var(--spacing-sm);
    font-size: var(--font-size-md);
}

.nav-title {
    flex: 1;
}

/* Content Area */
.config-content {
    flex: 1;
    overflow-y: auto;
    background: var(--background-primary);
}

.config-content-area {
    padding: var(--spacing-xl);
}

.config-section h3 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--foreground-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

.section-description {
    margin: 0 0 var(--spacing-xl) 0;
    color: var(--foreground-secondary);
    font-size: var(--font-size-md);
    line-height: 1.5;
}

/* Setting Groups */
.setting-group {
    margin-bottom: 2.5rem;
}

.setting-group h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--foreground-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-primary);
}

/* Setting Items */
.setting-item {
    margin-bottom: 1.5rem;
}

.setting-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--foreground-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-md);
}

.setting-input {
    width: 100%;
    max-width: 300px;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    color: var(--foreground-primary);
    font-size: var(--font-size-md);
    transition: border-color var(--transition-normal);
}

.setting-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.setting-checkbox {
    margin-right: 0.5rem;
}

.setting-description {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--foreground-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

/* Theme Selector */
.theme-selector {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: 0.5rem;
}

.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: var(--spacing-md);
    border: 2px solid var(--border-primary);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--background-secondary);
}

.theme-option:hover {
    border-color: var(--primary-color);
}

.theme-option input[type="radio"]:checked + .theme-option,
.theme-option:has(input[type="radio"]:checked) {
    border-color: var(--primary-color);
    background: rgba(var(--primary-color-rgb), 0.1);
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid var(--border-primary);
}

.dark-preview {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.light-preview {
    background: linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%);
}

.theme-option span {
    font-size: var(--font-size-sm);
    color: var(--foreground-primary);
    font-weight: var(--font-weight-medium);
}

.theme-option input[type="radio"] {
    display: none;
}

/* Range Controls */
.font-size-control,
.scale-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: 0.5rem;
}

.setting-range {
    flex: 1;
    max-width: 200px;
}

.range-value {
    min-width: 50px;
    text-align: center;
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
    font-size: var(--font-size-md);
}

/* Module Grid */
.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.module-setting-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.module-setting-card:hover {
    border-color: var(--primary-color);
}

.module-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.module-header h5 {
    margin: 0;
    color: var(--foreground-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.module-description {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--foreground-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

.module-status {
    font-size: var(--font-size-sm);
    color: var(--foreground-secondary);
}

.status-indicator {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
}

.status-indicator.active {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.status-indicator.planned {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-primary);
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

input:disabled + .toggle-slider {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Sync Status Display */
.sync-status-display {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.status-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: var(--font-size-md);
}

.status-row:last-child {
    margin-bottom: 0;
}

.status-row span:first-child {
    color: var(--foreground-secondary);
}

.status-row span:last-child {
    color: var(--foreground-primary);
    font-weight: 500;
}

/* Sync Actions */
.sync-actions {
    display: flex;
    gap: 0.75rem;
}

/* License Info Card */
.license-info-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: var(--spacing-md);
}

.license-status {
    margin-bottom: var(--spacing-md);
}

.status-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid #28a745;
}

.status-badge.inactive {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid #dc3545;
}

.license-details {
    margin-top: 1rem;
}

.license-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: var(--font-size-md);
}

.license-detail:last-child {
    margin-bottom: 0;
}

.license-detail span:first-child {
    color: var(--foreground-secondary);
    font-weight: 500;
}

.license-detail span:last-child {
    color: var(--foreground-primary);
}

/* License Actions */
.license-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* Device Info */
.device-info {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: var(--spacing-md);
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: var(--font-size-md);
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row span:first-child {
    color: var(--foreground-secondary);
}

.info-row span:last-child {
    color: var(--foreground-primary);
    font-weight: 500;
}

/* System Info */
.system-info {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

/* Maintenance Actions */
.maintenance-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* Footer */
.config-panel-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: var(--background-secondary);
    border-top: 1px solid var(--border-primary);
}

.config-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-text {
    font-size: var(--font-size-md);
    color: var(--foreground-secondary);
}

.config-actions {
    display: flex;
    gap: 0.75rem;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: var(--font-size-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-color-dark);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--foreground-primary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--background-quaternary);
    border-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .config-panel-container {
        padding: var(--spacing-md);
    }
    
    .config-panel {
        width: 95%;
        height: 90vh;
    }
    
    .config-sidebar {
        width: 200px;
    }
    
    .config-content-area {
        padding: 1.5rem;
    }
    
    .module-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .config-panel-body {
        flex-direction: column;
    }
    
    .config-sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-primary);
    }
    
    .config-nav {
        display: flex;
        padding: 0.5rem;
        overflow-x: auto;
    }
    
    .config-nav-item {
        white-space: nowrap;
        padding: 0.5rem 1rem;
        margin-right: 0.5rem;
        border-radius: 4px;
    }
    
    .config-content-area {
        padding: var(--spacing-md);
    }
    
    .theme-selector {
        flex-direction: column;
    }
    
    .sync-actions,
    .license-actions,
    .maintenance-actions,
    .config-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .config-panel-container {
        padding: 0.5rem;
    }
    
    .config-panel {
        width: 100%;
        height: 95vh;
    }
    
    .config-panel-header,
    .config-panel-footer {
        padding: 0.75rem 1rem;
    }
    
    .config-content-area {
        padding: 0.75rem;
    }
    
    .setting-group {
        margin-bottom: 1.5rem;
    }
    
    .setting-item {
        margin-bottom: var(--spacing-md);
    }
}

/* Dark Theme Specific */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --bg-hover: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --border-color: #404040;
    --primary-color: #007acc;
    --primary-color-dark: #005a9e;
    --primary-color-rgb: 0, 122, 204;
}

/* Light Theme Specific */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    --bg-tertiary: #e9ecef;
    --bg-hover: rgba(0, 0, 0, 0.05);
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #d1d5db;
    --primary-color: #007acc;
    --primary-color-dark: #005a9e;
    --primary-color-rgb: 0, 122, 204;
}

/* Animation for smooth transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.config-panel {
    animation: fadeIn 0.3s ease-out;
}

/* Focus styles for accessibility */
.config-nav-item:focus,
.setting-input:focus,
.btn:focus,
.config-panel-close:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .config-panel {
        border: 2px solid;
    }
    
    .btn {
        border: 1px solid;
    }
    
    .setting-input {
        border: 2px solid;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.001s !important;
        transition-duration: 0.001s !important;
    }
}