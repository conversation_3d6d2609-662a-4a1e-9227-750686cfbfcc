/* Dashboard Components */

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 30px 40px;
    background: linear-gradient(135deg, rgba(139, 125, 216, 0.1) 0%, rgba(107, 91, 199, 0.05) 100%);
    border-radius: 12px;
    margin: 30px 40px 30px 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    /* Ensure header doesn't interfere with scrolling */
    flex-shrink: 0;
}

.user-greeting h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.user-greeting p {
    font-size: 16px;
    color: var(--text-secondary);
    margin: 0;
}

/* User Statistics */
.user-stats {
    display: flex;
    gap: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
    min-width: 120px;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(139, 125, 216, 0.3);
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(139, 125, 216, 0.2);
    border-radius: 8px;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 2px;
    font-weight: 500;
}

/* Dashboard Content */
.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    padding: 0 40px 40px 40px;
    /* Allow content to scroll properly */
    min-height: min-content;
    box-sizing: border-box;
}

.dashboard-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
}

.dashboard-section:hover {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.12);
}

.modules-section {
    grid-column: 1 / -1; /* Full width */
    min-height: 400px; /* Reduced height for better space utilization */
    overflow: visible; /* Allow content to be visible */
    padding: 20px; /* Reduced padding */
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.btn-link {
    background: none;
    border: none;
    color: var(--accent-color);
    font-size: 14px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-link:hover {
    background: rgba(139, 125, 216, 0.1);
    color: var(--accent-color-light);
}

/* Activity Feed */
.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.06);
}

.activity-icon {
    font-size: 18px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(139, 125, 216, 0.2);
    border-radius: 6px;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    font-size: 14px;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    line-height: 1.4;
}

.activity-time {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.quick-action-btn:hover {
    background: rgba(139, 125, 216, 0.1);
    border-color: rgba(139, 125, 216, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.action-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(139, 125, 216, 0.2);
    border-radius: 10px;
}

/* Module Filters */
.module-filters {
    display: flex;
    gap: 8px;
}

.filter-btn {
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active,
.filter-btn:hover {
    background: rgba(139, 125, 216, 0.2);
    border-color: rgba(139, 125, 216, 0.4);
    color: var(--text-primary);
}

/* Modern Modules Container */
.modules-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-height: 100%;
}

/* Module Actions Footer */
.module-actions-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0 0 12px 12px;
    backdrop-filter: blur(10px);
}

.install-third-party-btn-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 8px;
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.install-third-party-btn-modern:hover {
    background: rgba(139, 125, 216, 0.15);
    border-color: rgba(139, 125, 216, 0.3);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.install-third-party-btn-modern .btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: rgba(139, 125, 216, 0.2);
    font-size: 14px;
    font-weight: 600;
}

.install-third-party-btn-modern .btn-text {
    font-size: 12px;
    opacity: 0.9;
}

.modules-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 600;
}

.modules-header small {
    color: var(--text-secondary);
    font-size: 12px;
    margin-left: 10px;
}

.install-third-party-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.install-third-party-btn:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

/* Enhanced Module Grid */
.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
    /* Better spacing for different screen sizes */
    width: 100%;
    box-sizing: border-box;
}

.module-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.01);
    border-radius: 12px;
    backdrop-filter: blur(5px);
}

.module-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #8b7dd8 0%, #a78bfa 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.module-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
    border-color: rgba(139, 125, 216, 0.4);
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(139, 125, 216, 0.1);
}

.module-card:hover::before {
    opacity: 1;
}

.module-card.unavailable {
    opacity: 0.6;
    cursor: not-allowed;
}

.module-card.unavailable:hover {
    transform: none;
    box-shadow: none;
}

.module-icon {
    font-size: 28px;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(139, 125, 216, 0.2) 0%, rgba(167, 139, 250, 0.15) 100%);
    border-radius: 16px;
    margin-bottom: 16px;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.module-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(139, 125, 216, 0.1) 0%, rgba(167, 139, 250, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.module-card:hover .module-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(139, 125, 216, 0.3) 0%, rgba(167, 139, 250, 0.2) 100%);
}

.module-card:hover .module-icon::before {
    opacity: 1;
}

.module-card h4 {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    letter-spacing: -0.5px;
}

.module-card p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0 0 16px 0;
    flex-grow: 1;
    opacity: 0.8;
}

.module-status {
    margin-top: auto;
}

.status-active {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
    color: #4caf50;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(76, 175, 80, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-installed {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.2), rgba(33, 150, 243, 0.1));
    color: #2196f3;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(33, 150, 243, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-coming-soon {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
    color: #ffc107;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(255, 193, 7, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-available {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
    color: #4CAF50;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(76, 175, 80, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-unavailable {
    background: linear-gradient(135deg, rgba(158, 158, 158, 0.2), rgba(158, 158, 158, 0.1));
    color: #9e9e9e;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(158, 158, 158, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Module Action Buttons */
.module-actions {
    margin-top: 16px;
    display: flex;
    gap: 8px;
}

.install-btn, .uninstall-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    flex: 1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.install-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.install-btn:hover::before {
    left: 100%;
}

.install-btn:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.uninstall-btn {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.uninstall-btn:hover {
    background: linear-gradient(135deg, #d32f2f, #b71c1c);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
}

.install-btn:disabled, .uninstall-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.installed {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.installed:hover {
    background: linear-gradient(135deg, #1976D2, #1565C0);
}

/* Module Details */
.module-details {
    margin: 16px 0;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.module-details small {
    color: var(--text-secondary);
    font-size: 11px;
    font-weight: 500;
    opacity: 0.7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-details small::before {
    content: '•';
    color: rgba(139, 125, 216, 0.6);
    margin-right: 6px;
}

/* Notifications */
.notifications-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 250px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border-left: 3px solid var(--accent-color);
    transition: all 0.3s ease;
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.06);
}

.notification-icon {
    font-size: 18px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(139, 125, 216, 0.2);
    border-radius: 6px;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-content p {
    font-size: 14px;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    line-height: 1.4;
}

.notification-time {
    font-size: 12px;
    color: var(--text-secondary);
}

/* No content states */
.no-modules-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: var(--text-secondary);
    grid-column: 1 / -1;
}

.module-icon-large {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.loading-progress {
    margin-top: 20px;
    width: 200px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color) 0%, var(--accent-color-light) 100%);
    transition: width 0.3s ease;
    border-radius: 2px;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top-color: var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-content {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
        margin: 20px 30px;
        padding: 25px 30px;
    }
    
    .user-stats {
        justify-content: space-between;
    }
    
    .module-grid, .module-cards-container {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 12px;
    }
}

/* Optimize for 4 modules display */
@media (min-width: 1200px) {
    .module-cards-container {
        grid-template-columns: repeat(4, 1fr);
        max-width: 1200px;
        margin: 0 auto;
        gap: 20px;
        padding: 32px;
    }
}

@media (min-width: 1600px) {
    .module-cards-container {
        grid-template-columns: repeat(4, 1fr);
        max-width: 1400px;
        gap: 24px;
        padding: 40px;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 20px;
        margin: 15px 20px;
    }
    
    .dashboard-content {
        padding: 0 20px 30px 20px;
        gap: 20px;
    }
    
    .dashboard-section {
        padding: 20px;
    }
    
    .user-stats {
        flex-direction: column;
        gap: 12px;
    }
    
    .stat-card {
        min-width: auto;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .module-grid {
        grid-template-columns: 1fr;
    }
    
    .module-cards-container {
        grid-template-columns: 1fr;
        padding: 16px;
        gap: 16px;
    }
    
    .module-card {
        min-height: 240px;
        padding: 20px;
    }
    
    .module-actions-footer {
        padding: 16px;
    }
    
    .install-third-party-btn-modern {
        padding: 12px 20px;
        font-size: 14px;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .dashboard-header {
        margin: 10px 15px;
        padding: 15px 20px;
    }
    
    .dashboard-content {
        padding: 0 15px 25px 15px;
        gap: 15px;
    }
    
    .dashboard-section {
        padding: 15px;
    }
    
    .user-stats {
        gap: 10px;
    }
    
    .stat-card {
        padding: 12px 16px;
    }
}

/* Module Dropzone Styles */
.module-dropzone-section {
    margin-top: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.module-dropzone-section .section-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
}

.module-dropzone-section .section-header small {
    color: var(--text-secondary);
    font-size: 13px;
}

.module-dropzone {
    margin-top: 15px;
    border: 2px dashed rgba(139, 125, 216, 0.3);
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    background: rgba(139, 125, 216, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.module-dropzone:hover {
    border-color: rgba(139, 125, 216, 0.5);
    background: rgba(139, 125, 216, 0.08);
    transform: translateY(-2px);
}

.module-dropzone.dragover {
    border-color: #8b7dd8;
    background: rgba(139, 125, 216, 0.15);
    transform: scale(1.02);
}

.module-dropzone.uploading {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.dropzone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.dropzone-icon {
    font-size: 48px;
    color: rgba(139, 125, 216, 0.7);
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.module-dropzone:hover .dropzone-icon {
    color: #8b7dd8;
    transform: scale(1.1);
}

.dropzone-content p {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.dropzone-content small {
    color: var(--text-secondary);
    font-size: 13px;
}

.dropzone-actions {
    margin-top: 15px;
}

.browse-btn {
    background: linear-gradient(135deg, #8b7dd8 0%, #6b5bc7 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.browse-btn:hover {
    background: linear-gradient(135deg, #9a8ce0 0%, #7d6ed1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(139, 125, 216, 0.3);
}

.browse-btn:active {
    transform: translateY(0);
}

/* Upload Progress */
.upload-progress {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.upload-progress.active {
    opacity: 1;
    visibility: visible;
}

.upload-progress .progress-circle {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(139, 125, 216, 0.3);
    border-top: 4px solid #8b7dd8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.upload-progress .progress-text {
    color: white;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}