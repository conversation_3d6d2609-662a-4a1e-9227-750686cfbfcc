/* Cloud Panel Styles */

.cloud-panel {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Cloud Header */
.cloud-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.cloud-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-indicator.disconnected {
    background: #f44336;
    animation: none;
}

.status-indicator.connecting {
    background: #ff9800;
}

.status-indicator.connected {
    background: #4caf50;
    animation: none;
}

.status-indicator.syncing {
    background: #2196f3;
}

.status-indicator.error {
    background: #f44336;
    animation: blink 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

.status-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.cloud-actions {
    display: flex;
    gap: 8px;
}

.cloud-action-btn {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cloud-action-btn:hover {
    background: rgba(139, 125, 216, 0.2);
    border-color: var(--accent-color);
    color: var(--text-primary);
}

.action-icon {
    font-size: 16px;
}

/* Storage Info */
.storage-info {
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 10px;
}

.storage-usage {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.usage-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.usage-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color) 0%, var(--accent-color-light) 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.usage-text {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--text-secondary);
}

.usage-percentage {
    font-weight: 600;
}

/* Breadcrumb Navigation */
.cloud-breadcrumb {
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
}

.breadcrumb-item {
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.breadcrumb-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
}

.breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: 500;
    cursor: default;
}

.breadcrumb-separator {
    color: var(--text-secondary);
    margin: 0 2px;
}

/* View Controls */
.view-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.view-mode-toggle {
    display: flex;
    gap: 2px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    padding: 2px;
}

.view-mode-btn {
    width: 32px;
    height: 32px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-mode-btn:hover {
    color: var(--text-primary);
}

.view-mode-btn.active {
    background: rgba(139, 125, 216, 0.2);
    color: var(--text-primary);
}

.mode-icon {
    font-size: 14px;
}

.file-count {
    font-size: 12px;
    color: var(--text-secondary);
}

/* File Upload Zone */
.cloud-drop-zone {
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.cloud-drop-zone:hover,
.cloud-drop-zone.drag-over {
    border-color: var(--accent-color);
    background: rgba(139, 125, 216, 0.05);
}

.drop-zone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.drop-icon {
    font-size: 36px;
    opacity: 0.6;
}

.cloud-drop-zone p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 14px;
}

.upload-link {
    background: none;
    border: none;
    color: var(--accent-color);
    text-decoration: underline;
    cursor: pointer;
    font-size: 14px;
}

.upload-link:hover {
    color: var(--accent-color-light);
}

.cloud-drop-zone small {
    color: var(--text-secondary);
    font-size: 12px;
}

/* Files Container */
.cloud-files-container {
    flex: 1;
    overflow-y: auto;
}

/* Files List */
.files-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.files-list.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 15px;
}

/* Grid View */
.grid-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    text-align: center;
}

.grid-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(139, 125, 216, 0.3);
    transform: translateY(-2px);
}

.grid-item.selected {
    background: rgba(139, 125, 216, 0.1);
    border-color: var(--accent-color);
}

.file-thumbnail {
    position: relative;
    margin-bottom: 10px;
}

.file-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.file-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 6px;
}

.grid-item:hover .file-overlay {
    opacity: 1;
}

.file-action {
    width: 28px;
    height: 28px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.file-action:hover {
    background: rgba(255, 255, 255, 0.2);
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 10px;
    color: var(--text-secondary);
}

.sync-status-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 12px;
}

/* List View */
.list-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.list-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(139, 125, 216, 0.3);
}

.list-item.selected {
    background: rgba(139, 125, 216, 0.1);
    border-color: var(--accent-color);
}

.list-item .file-icon {
    font-size: 20px;
    width: 24px;
    text-align: center;
}

.list-item .file-name {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list-item .file-size {
    width: 80px;
    font-size: 12px;
    color: var(--text-secondary);
    text-align: right;
}

.list-item .file-date {
    width: 100px;
    font-size: 12px;
    color: var(--text-secondary);
    text-align: right;
}

.list-item .file-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.list-item:hover .file-actions {
    opacity: 1;
}

.list-item .sync-status {
    width: 24px;
    text-align: center;
    font-size: 14px;
}

/* Empty State */
.cloud-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.cloud-empty-state h3 {
    font-size: 18px;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.cloud-empty-state p {
    font-size: 14px;
    margin: 0 0 25px 0;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-color-light);
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 16px;
}

/* Upload Progress */
.upload-progress-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 320px;
    background: var(--panel-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1000;
}

.upload-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.upload-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-uploads {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.close-uploads:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.upload-list {
    padding: 15px 20px;
    max-height: 200px;
    overflow-y: auto;
}

.upload-item {
    margin-bottom: 15px;
}

.upload-item:last-child {
    margin-bottom: 0;
}

.upload-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.upload-name {
    font-size: 13px;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    margin-right: 10px;
}

.upload-percentage {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 600;
}

.upload-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 4px;
}

.upload-fill {
    height: 100%;
    background: var(--accent-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.upload-status {
    font-size: 11px;
    color: var(--text-secondary);
}

/* Folder Item Styling */
.folder-item {
    cursor: pointer;
}

.folder-item:hover {
    background: rgba(139, 125, 216, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .cloud-panel {
        padding: 15px;
    }
    
    .cloud-header {
        padding: 12px 15px;
    }
    
    .storage-info {
        padding: 12px 15px;
    }
    
    .files-list.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 12px;
    }
    
    .grid-item {
        padding: 12px;
    }
    
    .list-item {
        padding: 10px 12px;
    }
    
    .list-item .file-size,
    .list-item .file-date {
        display: none;
    }
    
    .upload-progress-container {
        width: calc(100% - 40px);
        bottom: 10px;
        right: 20px;
        left: 20px;
    }
}

@media (max-width: 480px) {
    .cloud-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .view-controls {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .files-list.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}