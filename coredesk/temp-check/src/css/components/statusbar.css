/* Status bar styling */
.status-bar {
    height: var(--statusbar-height);
    background: var(--statusbar-background);
    color: var(--statusbar-foreground);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-sm);
    font-size: var(--font-size-xs);
    user-select: none;
    border-top: 1px solid var(--border-primary);
}

.status-left,
.status-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 0 var(--spacing-xs);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.status-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.status-icon {
    font-size: var(--font-size-xs);
}

.status-text {
    font-weight: var(--font-weight-normal);
}

/* Status indicators */
.status-icon.online {
    color: var(--success);
}

.status-icon.offline {
    color: var(--error);
}

.status-icon.syncing {
    animation: spin 1s linear infinite;
}

/* Module status styling */
#current-module .status-text {
    font-weight: var(--font-weight-medium);
}

/* License status styling */
#license-status .status-text {
    font-weight: var(--font-weight-medium);
}

#license-status.trial {
    color: var(--warning);
}

#license-status.premium {
    color: var(--success);
}

#license-status.expired {
    color: var(--error);
}

/* Sync status with progress */
#sync-status.syncing {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 2px var(--spacing-xs);
}

/* Spin animation for sync icon */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Enhanced status item styling */
.status-item {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.status-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.status-item.active {
    background: rgba(0, 122, 204, 0.2);
}

/* Status icon enhanced styling */
.status-icon {
    display: inline-block;
    font-size: var(--font-size-sm);
    margin-right: var(--spacing-xs);
}

/* Make circular module status more prominent */
#current-module .status-icon {
    width: var(--icon-size-sm);
    height: var(--icon-size-sm);
    background: #333;
    color: #fff;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
}

#current-module.active .status-icon {
    background: var(--accent-primary, #007acc);
}

/* Make sync status more rectangular */
#sync-status {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    padding: 6px 12px;
}

/* Simple sync modal styles */
.sync-modal {
    background: var(--background-primary, #1e1e1e);
    border: 1px solid var(--border-primary, #454545);
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
}

.sync-modal .modal-header {
    background: var(--background-secondary, #252526);
    border-bottom: 1px solid var(--border-primary, #454545);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sync-modal .modal-title {
    margin: 0;
    color: var(--foreground-primary, #cccccc);
    font-size: 1.1rem;
    font-weight: 600;
}

.sync-modal .modal-close {
    background: none;
    border: none;
    color: var(--foreground-muted, #888);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.sync-modal .modal-close:hover {
    background: var(--background-quaternary, #333);
    color: var(--foreground-primary, #cccccc);
}

.sync-modal .modal-body {
    padding: 20px;
}

.sync-info .status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-primary, #454545);
}

.sync-info .status-row:last-child {
    border-bottom: none;
}

.sync-info .status-label {
    color: var(--foreground-muted, #888);
    font-weight: 500;
}

.sync-info .status-value {
    color: var(--foreground-primary, #cccccc);
}

.sync-actions {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.sync-actions .btn {
    background: var(--accent-primary, #007acc);
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background 0.2s ease;
}

.sync-actions .btn:hover {
    background: var(--accent-secondary, #005a9e);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .status-bar {
        font-size: var(--font-size-xs);
    }
    
    .status-left,
    .status-right {
        gap: var(--spacing-sm);
    }
    
    .status-text {
        display: none;
    }
}