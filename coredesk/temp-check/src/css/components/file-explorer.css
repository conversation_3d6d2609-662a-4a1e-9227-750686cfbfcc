/**
 * File Explorer Component Styles
 * Styles for the real file system browser
 */

.file-explorer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--background-primary);
    color: var(--foreground-primary);
}

/* Navigation Bar */
.explorer-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-primary);
    flex-shrink: 0;
}

.nav-buttons {
    display: flex;
    gap: 2px;
}

.nav-btn {
    width: 28px;
    height: 28px;
    background: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    color: var(--foreground-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-md);
    transition: all var(--transition-fast);
}

.nav-btn:hover:not(:disabled) {
    background: var(--background-quaternary);
    border-color: var(--border-primary);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.address-bar {
    flex: 1;
    display: flex;
    gap: var(--spacing-xs);
}

.path-input {
    flex: 1;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--background-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    color: var(--foreground-primary);
    font-family: var(--font-family-mono);
    font-size: var(--font-size-sm);
}

.path-input:focus {
    outline: none;
    border-color: var(--accent-primary);
}

.go-btn {
    width: 28px;
    height: 28px;
    background: var(--accent-primary);
    border: none;
    border-radius: var(--border-radius);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background var(--transition-fast);
}

.go-btn:hover {
    background: var(--accent-primary-hover);
}

/* Content Layout */
.explorer-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* Sidebar */
.explorer-sidebar {
    width: 200px;
    background: var(--background-secondary);
    border-right: 1px solid var(--border-primary);
    padding: var(--spacing-sm);
    overflow-y: auto;
    flex-shrink: 0;
}

/* Main Content Area */
.explorer-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Bookmarks Section */
.bookmarks-section {
    margin-bottom: var(--spacing-md);
}

.bookmarks-section h5 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--foreground-secondary);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: var(--font-weight-semibold);
    padding: 0 var(--spacing-xs);
}

.bookmark-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 4px var(--spacing-xs);
    margin-bottom: 1px;
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    line-height: 1.3;
    min-height: 24px;
}

.bookmark-item:hover {
    background: var(--background-quaternary);
    transform: translateX(2px);
}

.bookmark-item:active {
    background: var(--background-tertiary);
    transform: translateX(1px);
}

.bookmark-icon {
    font-size: 14px;
    width: 14px;
    text-align: center;
    color: var(--foreground-secondary);
    flex-shrink: 0;
}

.bookmark-name {
    flex: 1;
    color: var(--foreground-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: var(--font-weight-medium);
}

/* Bookmarks Separator */
.bookmarks-separator {
    height: 1px;
    background: var(--border-primary);
    margin: var(--spacing-sm) var(--spacing-xs);
    opacity: 0.5;
}

/* Recent Files Section */
.recent-files-section {
    margin-top: var(--spacing-md);
}

.recent-files-section h5 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--foreground-secondary);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: var(--font-weight-semibold);
    padding: 0 var(--spacing-xs);
}

.recent-file-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 3px var(--spacing-xs);
    margin-bottom: 1px;
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-xs);
    line-height: 1.2;
    min-height: 22px;
}

.recent-file-item:hover {
    background: var(--background-quaternary);
    transform: translateX(2px);
}

.recent-file-item:active {
    background: var(--background-tertiary);
    transform: translateX(1px);
}

.recent-file-item .file-icon {
    font-size: 12px;
    width: 12px;
    text-align: center;
    color: var(--foreground-secondary);
    flex-shrink: 0;
}

.recent-file-item .file-name {
    flex: 1;
    color: var(--foreground-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: var(--font-weight-regular);
}

.recent-file-item .file-date {
    color: var(--foreground-tertiary);
    font-size: var(--font-size-xs);
    flex-shrink: 0;
    opacity: 0.7;
}

/* Toolbar */
.explorer-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-primary);
    flex-shrink: 0;
}

.view-options {
    display: flex;
    gap: 2px;
}

.view-btn {
    width: 28px;
    height: 28px;
    background: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    color: var(--foreground-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-md);
    transition: all var(--transition-fast);
}

.view-btn:hover {
    background: var(--background-quaternary);
    border-color: var(--border-primary);
}

.view-btn.active {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.sort-options {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.sort-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--background-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    color: var(--foreground-primary);
    font-size: var(--font-size-sm);
}

.sort-order-btn {
    width: 28px;
    height: 28px;
    background: none;
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    color: var(--foreground-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-md);
    transition: all var(--transition-fast);
}

.sort-order-btn:hover {
    background: var(--background-quaternary);
}

.hidden-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--foreground-secondary);
    cursor: pointer;
}

.hidden-toggle input {
    margin: 0;
}

/* File Listing */
.file-listing {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-sm);
}

.file-item {
    display: grid;
    grid-template-columns: 24px 1fr auto auto;
    gap: var(--spacing-sm);
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    margin-bottom: 1px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background var(--transition-fast);
    font-size: var(--font-size-sm);
}

.file-item:hover {
    background: var(--background-quaternary);
}

.file-item.directory {
    font-weight: var(--font-weight-medium);
}

.file-item.directory .file-name {
    color: var(--accent-primary);
}

.file-icon {
    font-size: var(--font-size-md);
    text-align: center;
    width: 20px;
}

.file-name {
    color: var(--foreground-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-size {
    color: var(--foreground-secondary);
    font-size: var(--font-size-xs);
    text-align: right;
    min-width: 60px;
}

.file-modified {
    color: var(--foreground-secondary);
    font-size: var(--font-size-xs);
    text-align: right;
    min-width: 80px;
}

/* Special states */
.empty-directory {
    text-align: center;
    color: var(--foreground-secondary);
    padding: var(--spacing-xl);
    font-style: italic;
}

.explorer-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: var(--spacing-md);
    color: var(--foreground-secondary);
}

.explorer-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    gap: var(--spacing-md);
    text-align: center;
}

.error-icon {
    font-size: 2rem;
}

.error-message {
    color: var(--foreground-secondary);
    font-size: var(--font-size-md);
}

.retry-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--accent-primary);
    border: none;
    border-radius: var(--border-radius);
    color: white;
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: background var(--transition-fast);
}

.retry-btn:hover {
    background: var(--accent-primary-hover);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .file-explorer-container {
        flex-direction: column;
    }
    
    .explorer-sidebar {
        width: 100%;
        max-height: 150px;
        border-right: none;
        border-bottom: 1px solid var(--border-primary);
    }
    
    .explorer-nav {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }
    
    .address-bar {
        flex: 1;
        min-width: 200px;
    }
    
    .explorer-toolbar {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .file-item {
        grid-template-columns: 24px 1fr;
        gap: var(--spacing-sm);
    }
    
    .file-size,
    .file-modified {
        display: none;
    }
}

/* Dark theme specific adjustments */
[data-theme="dark"] .file-item.directory .file-name {
    color: var(--accent-primary);
}

[data-theme="dark"] .path-input {
    background: var(--background-tertiary);
}

/* Light theme specific adjustments */
[data-theme="light"] .file-item.directory .file-name {
    color: var(--accent-primary);
}

[data-theme="light"] .path-input {
    background: white;
}

/* Animation for loading spinner */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-primary);
    border-top: 2px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}