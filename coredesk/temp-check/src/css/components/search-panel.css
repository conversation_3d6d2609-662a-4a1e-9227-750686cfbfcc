/* Search Panel Styles */

.search-panel {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Search Input */
.search-input-container {
    position: sticky;
    top: 0;
    background: var(--panel-bg);
    z-index: 10;
    padding-bottom: 10px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: 12px 40px 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(139, 125, 216, 0.1);
}

.search-input::placeholder {
    color: var(--text-secondary);
}

.search-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.search-btn:hover {
    color: var(--accent-color);
    background: rgba(139, 125, 216, 0.1);
}

.clear-search {
    position: absolute;
    right: 36px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.clear-search:hover {
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

/* Search Filters */
.search-filters {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.filter-option {
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.filter-option:hover {
    background: rgba(255, 255, 255, 0.08);
    color: var(--text-primary);
}

.filter-option.active {
    background: rgba(139, 125, 216, 0.2);
    border-color: var(--accent-color);
    color: var(--text-primary);
}

/* Search Results Container */
.search-results-container {
    flex: 1;
    overflow-y: auto;
}

.search-results {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Initial State */
.search-initial-state {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.search-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    padding: 20px;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Search History */
.search-history {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.search-history-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-history-item:hover {
    background: rgba(255, 255, 255, 0.06);
}

.history-icon {
    font-size: 14px;
    color: var(--text-secondary);
}

.history-text {
    font-size: 13px;
    color: var(--text-primary);
}

/* Search Suggestions */
.search-suggestions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 10px;
}

.suggestion-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 10px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.suggestion-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(139, 125, 216, 0.3);
    transform: translateY(-2px);
}

.suggestion-icon {
    font-size: 24px;
}

.suggestion-item span {
    font-size: 12px;
    color: var(--text-primary);
    font-weight: 500;
}

/* Search Tips */
.search-tips {
    background: rgba(139, 125, 216, 0.05);
    border: 1px solid rgba(139, 125, 216, 0.2);
}

.tips-list {
    margin: 0;
    padding-left: 20px;
    color: var(--text-secondary);
}

.tips-list li {
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 6px;
}

/* Search Results Header */
.search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.search-results-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* Results Sections */
.results-section {
    margin-bottom: 25px;
}

.results-section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 15px 0;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-icon {
    font-size: 16px;
}

/* Results List */
.results-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Result Items */
.result-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.result-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(139, 125, 216, 0.3);
    transform: translateY(-1px);
}

.result-icon {
    font-size: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(139, 125, 216, 0.2);
    border-radius: 8px;
    flex-shrink: 0;
}

.result-content {
    flex: 1;
    min-width: 0;
}

.result-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 6px 0;
    line-height: 1.3;
}

.result-title mark {
    background: rgba(255, 235, 59, 0.3);
    color: #fff176;
    padding: 1px 2px;
    border-radius: 2px;
}

.result-description {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0 0 8px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.result-description mark {
    background: rgba(255, 235, 59, 0.2);
    color: #fff176;
    padding: 1px 2px;
    border-radius: 2px;
}

.result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    font-size: 11px;
    color: var(--text-secondary);
}

.result-meta span {
    background: rgba(255, 255, 255, 0.05);
    padding: 2px 6px;
    border-radius: 4px;
    white-space: nowrap;
}

.result-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

.result-action {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.result-action:hover {
    background: rgba(139, 125, 216, 0.2);
    border-color: var(--accent-color);
    color: var(--text-primary);
}

/* No Results */
.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: var(--text-secondary);
}

.no-results-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.no-results h3 {
    font-size: 18px;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.no-results p {
    font-size: 14px;
    margin: 0 0 30px 0;
}

.no-results-suggestions {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    padding: 20px;
    max-width: 300px;
}

.no-results-suggestions h4 {
    font-size: 14px;
    color: var(--text-primary);
    margin: 0 0 10px 0;
}

.no-results-suggestions ul {
    margin: 0;
    padding-left: 20px;
    text-align: left;
}

.no-results-suggestions li {
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 6px;
}

/* Loading State */
.search-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top-color: var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.search-loading p {
    color: var(--text-secondary);
    font-size: 14px;
    margin: 0;
}

/* Error State */
.search-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: var(--text-secondary);
}

.error-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.search-error h3 {
    font-size: 18px;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.search-error p {
    font-size: 14px;
    margin: 0 0 20px 0;
}

.retry-search {
    padding: 8px 16px;
    background: var(--accent-color);
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.3s ease;
}

.retry-search:hover {
    background: var(--accent-color-light);
}

/* Animations */
@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-panel {
        padding: 15px;
    }
    
    .search-filters {
        padding: 12px;
    }
    
    .filter-options {
        gap: 4px;
    }
    
    .filter-option {
        padding: 4px 8px;
        font-size: 11px;
    }
    
    .search-suggestions {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    .result-item {
        padding: 12px;
    }
    
    .result-meta {
        flex-direction: column;
        gap: 4px;
    }
}