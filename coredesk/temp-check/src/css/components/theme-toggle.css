/**
 * Theme Toggle Component Styles
 * Styles for the theme switching interface
 */

/* Theme toggle button in titlebar */
#theme-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 30px;
    border: none;
    background: transparent;
    color: var(--foreground-secondary);
    cursor: pointer;
    transition: all var(--transition-normal);
    border-radius: var(--border-radius);
}

#theme-toggle-btn:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

#theme-toggle-btn:active {
    background: var(--background-tertiary);
    transform: scale(0.95);
}

#theme-toggle-btn .panel-icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* Theme change notification */
.theme-change-notification {
    animation: slideInFromRight 0.3s ease-out;
}

.theme-change-notification .notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: var(--font-weight-medium);
}

.theme-change-notification .panel-icon {
    width: 18px;
    height: 18px;
    fill: var(--accent-primary);
}

@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Settings theme selector */
.settings-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-secondary);
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section-title {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--foreground-primary);
}

.settings-item {
    margin-bottom: var(--spacing-md);
}

.settings-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    color: var(--foreground-primary);
}

.settings-select {
    width: 100%;
    max-width: 200px;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    background: var(--input-background);
    color: var(--input-foreground);
    font-size: var(--font-size-md);
    transition: all var(--transition-normal);
}

.settings-select:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.settings-description {
    margin: var(--spacing-xs) 0 0 0;
    font-size: var(--font-size-sm);
    color: var(--foreground-muted);
    line-height: 1.4;
}

/* Theme preview indicators */
.theme-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    background: var(--background-secondary);
    font-size: var(--font-size-sm);
    color: var(--foreground-secondary);
}

.theme-indicator.active {
    background: var(--accent-primary);
    color: #ffffff;
    border-color: var(--accent-primary);
}

.theme-indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* Smooth theme transitions */
html {
    transition: background-color var(--transition-normal),
                color var(--transition-normal);
}

*,
*::before,
*::after {
    transition: background-color var(--transition-normal),
                border-color var(--transition-normal),
                color var(--transition-normal);
}

/* Disable transitions during theme switch for performance */
.theme-switching *,
.theme-switching *::before,
.theme-switching *::after {
    transition: none !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    #theme-toggle-btn {
        border: 1px solid var(--border-primary);
    }
    
    #theme-toggle-btn:hover {
        border-color: var(--border-focus);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    #theme-toggle-btn,
    .theme-change-notification,
    .settings-select {
        transition: none;
    }
    
    .theme-change-notification {
        animation: none;
    }
    
    html,
    *,
    *::before,
    *::after {
        transition: none !important;
    }
}

/* Dark theme specific adjustments */
[data-theme="dark"] #theme-toggle-btn {
    color: #cccccc;
}

[data-theme="dark"] #theme-toggle-btn:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .theme-change-notification {
    background: #2d2d30;
    border-color: #454545;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Light theme specific adjustments */
[data-theme="light"] #theme-toggle-btn {
    color: #666666;
}

[data-theme="light"] #theme-toggle-btn:hover {
    color: #333333;
    background: rgba(0, 0, 0, 0.05);
}

[data-theme="light"] .theme-change-notification {
    background: #ffffff;
    border-color: #e0e0e0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Accessibility enhancements */
@media (prefers-reduced-motion: no-preference) {
    #theme-toggle-btn {
        transition: transform 0.1s ease;
    }
    
    #theme-toggle-btn:active {
        transform: scale(0.95);
    }
}

/* Focus indicators for keyboard navigation */
#theme-toggle-btn:focus-visible {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}

.settings-select:focus-visible {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}

/* Theme selector dropdown custom styling */
.settings-select option {
    background: var(--background-primary);
    color: var(--foreground-primary);
    padding: var(--spacing-sm);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    #theme-toggle-btn {
        width: 36px;
        height: 36px;
    }
    
    #theme-toggle-btn .panel-icon {
        width: 18px;
        height: 18px;
    }
    
    .theme-change-notification {
        right: 10px;
        top: 10px;
        font-size: var(--font-size-sm);
    }
    
    .settings-select {
        max-width: 100%;
    }
}

/* Print styles */
@media print {
    #theme-toggle-btn,
    .theme-change-notification {
        display: none;
    }
}