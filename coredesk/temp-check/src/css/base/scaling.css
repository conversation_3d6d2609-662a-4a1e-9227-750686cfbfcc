/* 
 * Scaling and DPI Optimizations
 * Automatic scaling adjustments for different display configurations
 */

/* Base scaling detection and variables */
:root {
    --detected-scale: 1;
    --platform-adjustment: 1;
    --optimal-font-base: 14px;
}

/* Auto-detect and adjust for different pixel densities */
html {
    /* Improved text rendering for all platforms */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    /* Prevent unwanted zoom behaviors */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    
    /* Better scaling foundation */
    box-sizing: border-box;
}

*, *::before, *::after {
    box-sizing: inherit;
}

/* Platform-specific font optimizations */
@supports (-webkit-appearance: none) {
    /* WebKit/Chromium based (including Electron) */
    body {
        font-feature-settings: "liga" 1, "kern" 1;
        font-variant-ligatures: common-ligatures;
    }
}

/* Windows ClearType optimizations */
@media screen and (-ms-high-contrast: none) {
    body {
        font-family: 'Segoe UI', system-ui, sans-serif;
    }
}

/* Electron-specific optimizations */
@media screen and (pointer: fine) {
    /* Desktop/Electron environment detected */
    :root {
        --desktop-scaling: 1;
    }
    
    /* Improved click targets for desktop */
    button, input, select, textarea {
        min-height: 32px;
        min-width: 32px;
    }
}

/* High DPI detection and adjustments */
@media (-webkit-min-device-pixel-ratio: 1.25) {
    :root {
        --detected-scale: 1.25;
        --optimal-font-base: 15px;
    }
}

@media (-webkit-min-device-pixel-ratio: 1.5) {
    :root {
        --detected-scale: 1.5;
        --optimal-font-base: 16px;
    }
    
    /* Slightly larger elements for 1.5x displays */
    .form-input, .btn {
        padding: 13px 17px;
    }
}

@media (-webkit-min-device-pixel-ratio: 2) {
    :root {
        --detected-scale: 2;
        --optimal-font-base: 16px;
    }
    
    /* Optimized for 2x (Retina) displays */
    .form-input, .btn {
        padding: 14px 18px;
        border-width: 1px;
    }
    
    /* Better icon rendering */
    .agent-icon {
        width: 48px;
        height: 48px;
        font-size: 24px;
    }
}

@media (-webkit-min-device-pixel-ratio: 3) {
    :root {
        --detected-scale: 3;
        --optimal-font-base: 17px;
    }
    
    /* Ultra-high DPI optimizations */
    .form-input, .btn {
        padding: 15px 20px;
    }
}

/* Screen size based adjustments */
@media (max-width: 1366px) and (max-height: 768px) {
    /* Common laptop resolution optimizations */
    :root {
        --spacing-scale: 0.9;
    }
    
    .auth-container {
        height: 95vh;
        max-height: 850px;
    }
}

@media (min-width: 1920px) and (min-height: 1080px) {
    /* Full HD and larger displays */
    :root {
        --spacing-scale: 1.1;
    }
    
    .auth-container {
        max-width: 1500px;
        max-height: 950px;
    }
}

@media (min-width: 2560px) {
    /* 2K and 4K displays */
    :root {
        --spacing-scale: 1.2;
    }
    
    .auth-container {
        max-width: 1700px;
        max-height: 1100px;
    }
    
    .brand-title {
        font-size: 44px;
    }
    
    .main-title, .sub-title {
        font-size: 52px;
    }
}

/* WSL/Linux specific font improvements */
@supports (font-variant-east-asian: normal) {
    body {
        font-variant-numeric: tabular-nums;
        font-variant-ligatures: common-ligatures;
    }
}

/* Improved contrast for certain displays */
@media (prefers-contrast: high) {
    :root {
        --contrast-adjustment: 1.2;
    }
    
    .form-input {
        border-width: 2px;
    }
    
    .btn {
        border-width: 2px;
        font-weight: 600;
    }
}

/* Motion preferences */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Debug mode - shows scaling information */
[data-debug="true"]::before {
    content: "Scale: " var(--detected-scale) " | Platform: " var(--platform-adjustment);
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    z-index: 10000;
    pointer-events: none;
}
