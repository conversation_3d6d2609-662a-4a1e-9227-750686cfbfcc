/* CSS Custom Properties (Variables) */
:root {
    /* Primary fonts - using system fonts for better rendering */
    --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* DPI and scaling detection */
    --base-font-size: 14px;
    --scale-factor: 1;
    
    /* Layout dimensions - Responsive with proper scaling */
    --titlebar-height: clamp(28px, 2.5vw, 40px);
    --activitybar-width: clamp(50px, 4vw, 70px);
    --statusbar-height: clamp(22px, 2vw, 30px);
    --panel-min-width: clamp(200px, 15vw, 280px);
    --panel-max-width: clamp(400px, 30vw, 600px);
    --tab-height: clamp(32px, 3vw, 48px);
    
    /* Spacing - Responsive scaling */
    --spacing-xs: clamp(4px, 0.4vw, 8px);
    --spacing-sm: clamp(8px, 0.8vw, 12px);
    --spacing-md: clamp(12px, 1.2vw, 20px);
    --spacing-lg: clamp(16px, 1.6vw, 28px);
    --spacing-xl: clamp(24px, 2.4vw, 40px);
    
    /* Border radius */
    --border-radius: 3px;
    --border-radius-lg: 6px;
    
    /* Transitions */
    --transition-fast: 0.1s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;
    
    /* Z-index levels */
    --z-modal: 1000;
    --z-overlay: 999;
    --z-dropdown: 100;
    --z-titlebar: 90;
    --z-panels: 10;
    
    /* Font weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Font sizes - Responsive with better scaling */
    --font-size-xs: clamp(11px, 0.85rem, 14px);
    --font-size-sm: clamp(12px, 0.9rem, 15px);
    --font-size-md: clamp(13px, 1rem, 16px);
    --font-size-lg: clamp(14px, 1.1rem, 18px);
    --font-size-xl: clamp(16px, 1.3rem, 22px);
    --font-size-xxl: clamp(20px, 1.6rem, 28px);
    
    /* Icon sizes - Responsive scaling */
    --icon-size-sm: clamp(16px, 1.4rem, 24px);
    --icon-size-md: clamp(20px, 1.8rem, 32px);
    --icon-size-lg: clamp(28px, 2.4rem, 40px);
    
    /* Button sizes - Responsive scaling */
    --button-height-sm: clamp(24px, 2rem, 32px);
    --button-height-md: clamp(28px, 2.4rem, 36px);
    --button-height-lg: clamp(32px, 2.8rem, 44px);
}

/* Dark theme variables (default) */
[data-theme="dark"] {
    /* Background colors */
    --background-primary: #1e1e1e;
    --background-secondary: #252526;
    --background-tertiary: #2d2d30;
    --background-quaternary: #3c3c3c;
    
    /* Foreground colors */
    --foreground-primary: #cccccc;
    --foreground-secondary: #c6c6c6;
    --foreground-muted: #969696;
    --foreground-disabled: #6a6a6a;
    
    /* Border colors */
    --border-primary: #454545;
    --border-secondary: #3c3c3c;
    --border-focus: #007acc;
    
    /* Accent colors */
    --accent-primary: #0e639c;
    --accent-primary-hover: #1177bb;
    --accent-secondary: #094771;
    --accent-hover: #1177bb;
    
    /* RGB versions for transparency */
    --accent-primary-rgb: 14, 99, 156;
    --success-rgb: 137, 209, 133;
    --error-rgb: 244, 135, 113;
    --warning-rgb: 255, 204, 2;
    
    /* Status colors */
    --success: #89d185;
    --warning: #ffcc02;
    --error: #f48771;
    --info: #75beff;
    
    /* Panel specific */
    --titlebar-background: var(--background-secondary);
    --titlebar-foreground: var(--foreground-primary);
    --activitybar-background: var(--background-tertiary);
    --activitybar-foreground: var(--foreground-secondary);
    --statusbar-background: var(--accent-primary);
    --statusbar-foreground: #ffffff;
    
    /* Scrollbar */
    --scrollbar-track: transparent;
    --scrollbar-thumb: #424242;
    --scrollbar-thumb-hover: #4f4f4f;
    
    /* Selection */
    --selection-background: #264f78;
    --selection-foreground: #ffffff;
    --background-selection: #264f78;
    --foreground-selection: #ffffff;
    --background-hover: #2a2d2e;
    
    /* Input colors */
    --input-background: var(--background-tertiary);
    --input-background-focus: var(--background-secondary);
    --input-border: var(--border-primary);
    --input-foreground: var(--foreground-primary);
    
    /* Button colors */
    --button-primary-background: var(--accent-primary);
    --button-primary-foreground: #ffffff;
    --button-primary-hover: var(--accent-hover);
    --button-secondary-background: var(--background-quaternary);
    --button-secondary-foreground: var(--foreground-primary);
    --button-secondary-hover: #4a4a4a;
    --button-background: var(--background-quaternary);
    --button-foreground: var(--foreground-primary);
    --button-background-hover: #4a4a4a;
    --border-hover: #606060;
    
    /* Modal colors */
    --modal-background: var(--background-secondary);
    --modal-overlay: rgba(0, 0, 0, 0.6);
    
    /* Tab colors */
    --tab-active-background: var(--background-primary);
    --tab-inactive-background: var(--background-secondary);
    --tab-hover-background: var(--background-quaternary);
    --tab-border: var(--border-primary);
}

/* Light theme variables */
[data-theme="light"] {
    /* Background colors */
    --background-primary: #ffffff;
    --background-secondary: #f3f3f3;
    --background-tertiary: #ebebeb;
    --background-quaternary: #e0e0e0;
    
    /* Foreground colors */
    --foreground-primary: #333333;
    --foreground-secondary: #444444;
    --foreground-muted: #6a6a6a;
    --foreground-disabled: #a0a0a0;
    
    /* Border colors */
    --border-primary: #e0e0e0;
    --border-secondary: #d0d0d0;
    --border-focus: #0078d4;
    
    /* Accent colors */
    --accent-primary: #0078d4;
    --accent-primary-hover: #1890f1;
    --accent-secondary: #106ebe;
    --accent-hover: #1890f1;
    
    /* RGB versions for transparency */
    --accent-primary-rgb: 0, 120, 212;
    --success-rgb: 16, 124, 16;
    --error-rgb: 209, 52, 56;
    --warning-rgb: 255, 185, 0;
    
    /* Status colors */
    --success: #107c10;
    --warning: #ffb900;
    --error: #d13438;
    --info: #0078d4;
    
    /* Panel specific */
    --titlebar-background: var(--background-secondary);
    --titlebar-foreground: var(--foreground-primary);
    --activitybar-background: var(--background-tertiary);
    --activitybar-foreground: var(--foreground-secondary);
    --statusbar-background: var(--accent-primary);
    --statusbar-foreground: #ffffff;
    
    /* Scrollbar */
    --scrollbar-track: transparent;
    --scrollbar-thumb: #c0c0c0;
    --scrollbar-thumb-hover: #a0a0a0;
    
    /* Selection */
    --selection-background: #add6ff;
    --selection-foreground: #000000;
    --background-selection: #add6ff;
    --foreground-selection: #000000;
    --background-hover: #f0f0f0;
    
    /* Input colors */
    --input-background: #ffffff;
    --input-background-focus: #f8f8f8;
    --input-border: var(--border-primary);
    --input-foreground: var(--foreground-primary);
    
    /* Button colors */
    --button-primary-background: var(--accent-primary);
    --button-primary-foreground: #ffffff;
    --button-primary-hover: var(--accent-hover);
    --button-secondary-background: var(--background-quaternary);
    --button-secondary-foreground: var(--foreground-primary);
    --button-secondary-hover: #d6d6d6;
    --button-background: var(--background-quaternary);
    --button-foreground: var(--foreground-primary);
    --button-background-hover: #d6d6d6;
    --border-hover: #c0c0c0;
    
    /* Modal colors */
    --modal-background: #ffffff;
    --modal-overlay: rgba(0, 0, 0, 0.4);
    
    /* Tab colors */
    --tab-active-background: var(--background-primary);
    --tab-inactive-background: var(--background-secondary);
    --tab-hover-background: var(--background-quaternary);
    --tab-border: var(--border-primary);
}

/* Density variations */
[data-density="compact"] {
    --titlebar-height: 26px;
    --statusbar-height: 20px;
    --tab-height: 30px;
    --spacing-sm: 6px;
    --spacing-md: 8px;
    --spacing-lg: 12px;
}

[data-density="spacious"] {
    --titlebar-height: 36px;
    --statusbar-height: 26px;
    --tab-height: 40px;
    --spacing-sm: 12px;
    --spacing-md: 16px;
    --spacing-lg: 20px;
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    :root {
        /* Optimize for high DPI displays */
        --font-size-xs: clamp(12px, 0.9rem, 15px);
        --font-size-sm: clamp(13px, 1rem, 16px);
        --font-size-md: clamp(14px, 1.1rem, 17px);
        --font-size-lg: clamp(15px, 1.2rem, 19px);
        --font-size-xl: clamp(17px, 1.4rem, 23px);
        --font-size-xxl: clamp(21px, 1.7rem, 29px);
        
        /* Slightly larger spacing for high DPI */
        --spacing-xs: clamp(5px, 0.5vw, 9px);
        --spacing-sm: clamp(9px, 0.9vw, 13px);
        --spacing-md: clamp(13px, 1.3vw, 21px);
        --spacing-lg: clamp(17px, 1.7vw, 29px);
    }
}

/* Windows specific optimizations */
@media screen and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    :root {
        /* Windows ClearType optimizations */
        --font-size-md: 14px;
        --font-size-lg: 15px;
    }
}

/* Linux/WSL specific optimizations */
@supports (font-variant-ligatures: normal) {
    :root {
        /* Better font rendering for Linux environments */
        --font-family-primary: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Ubuntu', 'Roboto', sans-serif;
    }
}

/* Ultra-wide display optimizations */
@media (min-width: 2560px) {
    :root {
        --panel-max-width: clamp(500px, 25vw, 800px);
        --spacing-xl: clamp(32px, 3.2vw, 56px);
    }
}

/* Small screens and compact mode */
@media (max-width: 1024px) {
    :root {
        --font-size-xs: 11px;
        --font-size-sm: 12px;
        --font-size-md: 13px;
        --font-size-lg: 14px;
        --font-size-xl: 16px;
        --font-size-xxl: 20px;
        
        --spacing-xs: 4px;
        --spacing-sm: 6px;
        --spacing-md: 10px;
        --spacing-lg: 14px;
        --spacing-xl: 20px;
    }
}