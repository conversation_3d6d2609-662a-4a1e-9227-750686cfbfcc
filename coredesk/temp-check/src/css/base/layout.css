/* Main application layout */
* {
    box-sizing: border-box;
}

/* Ensure html and body fill the window completely */
html {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    /* Improve text rendering on high-DPI displays */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-rendering: optimizeLegibility;
}

body {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    background: var(--background-primary, #1e1e1e);
    color: var(--foreground-primary, #cccccc);
    /* Ensure no unwanted spacing */
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    /* Prevent any zoom-related issues */
    transform: none;
    transform-origin: initial;
}

/* When maximized, ensure no margins */
body.maximized {
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}

body.maximized .app-container {
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}

.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    /* Eliminate any gaps or spacing */
    gap: 0;
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    /* Ensure children align correctly */
    align-items: stretch;
    /* Add relative positioning to contain absolute left panel */
    position: relative;
}

/* Direct children of main-content should not have margins or shrink unexpectedly */
.main-content > * {
    margin: 0;
    padding: 0;
    flex-shrink: 0;
    /* Eliminar cualquier borde que pueda causar espaciado */
    border: none;
    outline: none;
}

/* Left panel should not affect flex layout when overlayed */
.main-content > .side-panel.left-panel {
    flex-shrink: 1;
    flex-basis: 0;
    flex-grow: 0;
}

.activity-bar {
    flex-shrink: 0;
    /* Ensure activity bar stays above left panel overlay */
    position: relative;
    z-index: 11;
}

.side-panel {
    flex-shrink: 0;
}

/* The editor container is the only one that should grow */
.editor-container {
    flex-grow: 1;
    flex-shrink: 1;
}

/* Editor container layout */
.editor-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;
    /* Eliminar cualquier espaciado no deseado */
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
}

/* Tab content area positioning */
.tab-content {
    position: relative;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Panel layout */
.side-panel {
    display: flex;
    flex-direction: column;
    /* Remove default width - let JavaScript control it per content type */
    min-width: var(--panel-min-width);
    max-width: var(--panel-max-width);
    background: var(--background-secondary);
    border-right: 1px solid var(--border-primary);
    transition: width var(--transition-normal);
    /* Ensure no gaps or unwanted spacing */
    margin: 0;
    padding: 0;
    border-top: none;
    border-bottom: none;
    flex-shrink: 0;
}

.side-panel.left-panel {
    border-left: none;
    border-right: 1px solid var(--border-primary);
    /* Overlay positioning for left panel - must be after activity bar */
    position: absolute !important;
    top: 0;
    left: var(--activitybar-width) !important;
    bottom: 0;
    z-index: 10;
    height: 100%;
    /* Remove from flex layout */
    flex-shrink: 1;
    flex-basis: 0;
    flex-grow: 0;
}

.side-panel.right-panel {
    border-left: 1px solid var(--border-primary);
    border-right: none;
}

.side-panel.hidden {
    width: 0;
    min-width: 0;
    border: none;
    overflow: hidden;
}

.bottom-panel {
    height: 200px;
    background: var(--background-secondary);
    border-top: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    transition: height var(--transition-normal);
}

.bottom-panel.hidden {
    height: 0;
    border: none;
    overflow: hidden;
}

/* Panel headers */
.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--tab-height);
    padding: 0 var(--spacing-md);
    background: var(--background-tertiary);
    border-bottom: 1px solid var(--border-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.panel-close {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    color: var(--foreground-muted);
    transition: all var(--transition-fast);
}

.panel-close:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

.panel-content {
    flex: 1;
    overflow: auto;
    /* Remove default padding to eliminate gaps */
    padding: 0;
    margin: 0;
    border: none;
    outline: none;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-overlay);
    transition: opacity var(--transition-slow);
    /* Ocultar por defecto */
    opacity: 0;
    pointer-events: none;
}

.loading-overlay.show {
    opacity: 1;
    pointer-events: all;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fallback styles if variables don't load */
.titlebar {
    background: #252526;
    color: #cccccc;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
}

.activity-bar {
    background: var(--activitybar-background, #2d2d30);
    width: var(--activitybar-width, 48px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    /* Ensure no gaps */
    margin: 0;
    padding: 0;
    border-right: 1px solid var(--border-primary);
}

/* Activity bar structure - detailed styles moved to activitybar.css */

.status-bar {
    background: #0e639c;
    color: #ffffff;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    font-size: 12px;
}

.welcome-screen {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--background-primary);
    color: var(--foreground-primary);
    /* Enable scrolling for dashboard content */
    overflow-y: auto;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
}

.welcome-content h1 {
    color: #cccccc;
    margin-bottom: 16px;
}

.module-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin: 0; /* Remove margin to prevent inconsistent spacing */
    cursor: pointer;
    transition: all var(--transition-fast);
}

.module-card:hover {
    border-color: var(--accent-primary);
    background: var(--background-tertiary);
}

/* Module grid layout */
.module-grid {
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    gap: var(--spacing-md);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.btn {
    background: #0e639c;
    color: #ffffff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin: 4px;
}

.btn:hover {
    background: #1177bb;
}

/* LexFlow Module Basic Styles */
.lexflow-module {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #1e1e1e;
    color: #cccccc;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.lexflow-layout {
    display: flex;
    height: 100%;
}

.lexflow-sidebar {
    width: 280px;
    min-width: 280px;
    background: #252526;
    border-right: 1px solid #454545;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.lexflow-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #454545;
}

.sidebar-header h2 {
    margin: 0 0 16px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #cccccc;
}

.main-header {
    padding: 20px 30px;
    border-bottom: 1px solid #454545;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #1e1e1e;
}

.lexflow-main-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 30px;
}

/* Modal container */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--modal-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-normal);
}

.modal-container:not(.hidden) {
    opacity: 1;
    pointer-events: all;
}

/* Utility classes */
.hidden {
    display: none !important;
}

.invisible {
    visibility: hidden;
}

.flex {
    display: flex;
}

.flex-1 {
    flex: 1;
}

.flex-column {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-auto {
    overflow: auto;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .side-panel {
        width: 240px;
        min-width: 240px;
    }
}

@media (max-width: 768px) {
    .titlebar-center {
        display: none;
    }
    
    .panel-toggles {
        display: none;
    }
    
    .side-panel {
        position: fixed;
        top: var(--titlebar-height);
        bottom: var(--statusbar-height);
        z-index: var(--z-panels);
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }
    
    .side-panel:not(.hidden) {
        transform: translateX(0);
    }
    
    .side-panel.right-panel {
        right: 0;
        transform: translateX(100%);
    }
    
    .side-panel.right-panel:not(.hidden) {
        transform: translateX(0);
    }
}

/* High-DPI display optimizations - More conservative scaling */
@media (-webkit-min-device-pixel-ratio: 2), 
       (min-resolution: 192dpi), 
       (min-resolution: 2dppx) {
    :root {
        /* Slightly increase font sizes instead of global zoom */
        --font-size-xs: 14px;
        --font-size-sm: 15px;
        --font-size-md: 16px;
        --font-size-lg: 17px;
        --font-size-xl: 19px;
        --font-size-xxl: 23px;
    }
}

@media (-webkit-min-device-pixel-ratio: 3), 
       (min-resolution: 288dpi), 
       (min-resolution: 3dppx) {
    :root {
        /* Further increase for very high DPI displays */
        --font-size-xs: 15px;
        --font-size-sm: 16px;
        --font-size-md: 17px;
        --font-size-lg: 18px;
        --font-size-xl: 20px;
        --font-size-xxl: 24px;
    }
}

/* Window maximized state adjustments */
body.window-maximized {
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden;
}

body.window-maximized .app-container {
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
}

body.window-maximized .titlebar {
    border-radius: 0 !important;
}

body.window-maximized .main-content {
    border-radius: 0 !important;
}