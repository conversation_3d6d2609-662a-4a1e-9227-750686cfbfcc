/**
 * CoreDesk Framework CSS Reset
 * Comprehensive reset for consistent cross-browser behavior
 */

/* Universal box-sizing and reset */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* HTML and body setup */
html, body {
    height: 100%;
    width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-size: clamp(12px, 1rem, 16px);
    line-height: 1.4;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    
    /* Improved scaling support */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    
    /* Ensure no margins or padding */
    margin: 0;
    padding: 0;
    /* Prevent any default browser spacing */
    position: relative;
}

/* Headings reset */
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
    font-weight: inherit;
    font-size: inherit;
}

/* Paragraphs reset */
p {
    margin: 0;
    padding: 0;
}

/* Lists reset */
ul, ol {
    margin: 0;
    padding: 0;
    list-style: none;
}

li {
    margin: 0;
    padding: 0;
}

/* Buttons reset */
button {
    margin: 0;
    padding: 0;
    border: none;
    background: none;
    outline: none;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

/* Form elements reset */
input, textarea, select {
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

img {
    user-select: none;
    -webkit-user-drag: none;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
}

/* Selection styling */
::selection {
    background: var(--selection-background);
    color: var(--selection-foreground);
}

/* Focus outline */
:focus {
    outline: 1px solid var(--focus-border);
    outline-offset: -1px;
}

/* Animation disable class */
.no-animations * {
    animation-duration: 0s !important;
    transition-duration: 0s !important;
}