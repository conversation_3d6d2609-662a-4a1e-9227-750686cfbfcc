/* Typography styles */

/* Base typography */
body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-md);
    line-height: 1.4;
    color: var(--foreground-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Remove aggressive zoom that causes cropping */
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: var(--font-weight-semibold);
    line-height: 1.2;
    color: var(--foreground-primary);
}

h1 {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
}

h2 {
    font-size: var(--font-size-xl);
}

h3 {
    font-size: var(--font-size-lg);
}

h4 {
    font-size: var(--font-size-md);
}

h5 {
    font-size: var(--font-size-sm);
}

h6 {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Paragraphs */
p {
    margin: 0 0 var(--spacing-md) 0;
    line-height: 1.5;
}

p:last-child {
    margin-bottom: 0;
}

/* Links */
a {
    color: var(--accent-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--accent-hover);
    text-decoration: underline;
}

a:focus {
    outline: 1px solid var(--focus-border);
    outline-offset: 2px;
}

/* Lists */
ul, ol {
    margin: 0 0 var(--spacing-md) 0;
    padding-left: var(--spacing-lg);
}

li {
    margin-bottom: var(--spacing-xs);
}

ul.unstyled,
ol.unstyled {
    list-style: none;
    padding-left: 0;
}

/* Code and preformatted text */
code {
    font-family: var(--font-family-mono);
    font-size: 0.9em;
    background: var(--background-tertiary);
    padding: 2px 4px;
    border-radius: var(--border-radius);
    color: var(--foreground-primary);
}

pre {
    font-family: var(--font-family-mono);
    background: var(--background-tertiary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-primary);
    overflow-x: auto;
    margin: 0 0 var(--spacing-md) 0;
    line-height: 1.4;
}

pre code {
    background: none;
    padding: 0;
    border-radius: 0;
    color: inherit;
}

/* Blockquotes */
blockquote {
    margin: 0 0 var(--spacing-md) 0;
    padding: var(--spacing-md);
    border-left: 4px solid var(--accent-primary);
    background: var(--background-secondary);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    font-style: italic;
}

blockquote p:last-child {
    margin-bottom: 0;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 0 0 var(--spacing-md) 0;
}

th, td {
    text-align: left;
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-primary);
}

th {
    font-weight: var(--font-weight-semibold);
    background: var(--background-secondary);
    color: var(--foreground-primary);
}

tr:hover {
    background: var(--background-secondary);
}

/* Form typography */
label {
    font-weight: var(--font-weight-medium);
    color: var(--foreground-primary);
}

input, textarea, select {
    font-family: inherit;
    font-size: var(--font-size-sm);
}

::placeholder {
    color: var(--foreground-muted);
    opacity: 1;
}

/* Utility typography classes */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-xxl { font-size: var(--font-size-xxl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--foreground-primary); }
.text-secondary { color: var(--foreground-secondary); }
.text-muted { color: var(--foreground-muted); }
.text-disabled { color: var(--foreground-disabled); }

.text-accent { color: var(--accent-primary); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-info { color: var(--info); }

.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-capitalize { text-transform: capitalize; }

.text-underline { text-decoration: underline; }
.text-no-underline { text-decoration: none; }

.line-height-tight { line-height: 1.1; }
.line-height-normal { line-height: 1.4; }
.line-height-loose { line-height: 1.6; }

.letter-spacing-tight { letter-spacing: -0.025em; }
.letter-spacing-normal { letter-spacing: 0; }
.letter-spacing-wide { letter-spacing: 0.025em; }

/* Text truncation */
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Word breaking */
.break-words {
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
}

.break-all {
    word-break: break-all;
}

/* Selection styling */
.no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Responsive typography */
@media (max-width: 768px) {
    body {
        font-size: var(--font-size-sm);
    }
    
    h1 { font-size: var(--font-size-xl); }
    h2 { font-size: var(--font-size-lg); }
    h3 { font-size: var(--font-size-md); }
}

@media (max-width: 480px) {
    h1 { font-size: var(--font-size-lg); }
    h2 { font-size: var(--font-size-md); }
    h3 { font-size: var(--font-size-sm); }
}