/**
 * License Activation Modal Styles
 * Styles for the license activation and trial flow interface
 */

/* Modal Container */
.license-modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.license-modal {
    background: var(--bg-color);
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    border: 1px solid var(--border-color);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Modal Header */
.license-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--header-bg);
}

.license-modal-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.license-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.license-modal-close:hover {
    background: var(--hover-bg);
    color: var(--text-color);
}

/* Modal Body */
.license-modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Modal Footer */
.license-modal-footer {
    padding: 20px 24px;
    border-top: 1px solid var(--border-color);
    background: var(--footer-bg);
}

/* Step Styles */
.license-step {
    min-height: 300px;
}

.step-header {
    text-align: center;
    margin-bottom: 30px;
}

.step-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 16px;
}

.step-number {
    background: var(--primary-color);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.step-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
}

.step-header h3 {
    margin: 0 0 8px 0;
    font-size: 1.4rem;
    color: var(--text-color);
}

.step-header p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.95rem;
}

/* Welcome Step */
.welcome-step .activation-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.option-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.option-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);
}

.option-card.trial-option:hover {
    border-color: #28a745;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.option-icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
}

.option-card h4 {
    margin: 0 0 8px 0;
    font-size: 1.2rem;
    color: var(--text-color);
}

.option-card p {
    margin: 0 0 16px 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.option-card ul {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    text-align: left;
}

.option-card li {
    padding: 4px 0;
    color: var(--text-muted);
    font-size: 0.85rem;
}

.option-card button {
    width: 100%;
    padding: 12px;
    border-radius: 6px;
    font-weight: 500;
}

.device-info {
    background: var(--info-bg);
    border: 1px solid var(--info-border);
    border-radius: 6px;
    padding: 16px;
    text-align: center;
    margin-top: 20px;
}

.device-info p {
    margin: 4px 0;
    font-size: 0.9rem;
}

/* Form Styles */
.step-content {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--text-color);
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    background: var(--input-bg);
    color: var(--text-color);
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-input.verification-input {
    text-align: center;
    font-size: 1.2rem;
    letter-spacing: 0.2em;
    font-family: 'Monaco', 'Consolas', monospace;
}

.form-input.license-key-input {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 1.1rem;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

.form-help {
    display: block;
    margin-top: 4px;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    flex-shrink: 0;
}

.checkbox-label a {
    color: var(--primary-color);
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

/* Device Summary */
.device-summary {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin: 20px 0;
}

.device-summary h4 {
    margin: 0 0 12px 0;
    font-size: 1rem;
    color: var(--text-color);
}

.device-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.device-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-light);
}

.device-info-item:last-child {
    border-bottom: none;
}

.device-info-item label {
    font-weight: 500;
    color: var(--text-muted);
    font-size: 0.85rem;
}

.device-info-item span {
    color: var(--text-color);
    font-size: 0.85rem;
}

.device-info-item code {
    background: var(--code-bg);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8rem;
    border: 1px solid var(--border-light);
}

/* Trial Info */
.trial-info {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 6px;
    padding: 16px;
    margin: 20px 0;
}

.trial-info h4 {
    margin: 0 0 12px 0;
    font-size: 1rem;
}

.trial-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.trial-info li {
    padding: 2px 0;
    font-size: 0.9rem;
}

/* License Display */
.license-display {
    background: var(--card-bg);
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.license-display label {
    display: block;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--text-color);
}

.license-key-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    background: var(--code-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 8px;
}

.license-key-display code {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    letter-spacing: 0.1em;
}

.copy-button {
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.copy-button:hover {
    background: var(--hover-bg);
}

/* Resend Section */
.resend-section {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-light);
}

.resend-section p {
    margin: 0 0 8px 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Loading Indicator */
.loading-indicator {
    text-align: center;
    padding: 20px;
}

.spinner {
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-indicator p {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0;
}

/* Error Message */
.error-message {
    background: #dc3545;
    color: white;
    padding: 12px 16px;
    border-radius: 6px;
    margin: 16px 0;
    font-size: 0.9rem;
    text-align: center;
    border: 1px solid #c82333;
}

/* Success Step */
.success-step {
    text-align: center;
}

.success-header {
    margin-bottom: 30px;
}

.success-icon {
    font-size: 3rem;
    margin-bottom: 16px;
}

.success-header h3 {
    color: #28a745;
    margin-bottom: 8px;
}

.license-details {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: left;
}

.license-details h4 {
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    color: var(--text-color);
    text-align: center;
}

.license-info-grid {
    display: grid;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-light);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 500;
    color: var(--text-muted);
    margin-bottom: 0;
}

.info-item span {
    color: var(--text-color);
    text-align: right;
}

.info-item code {
    background: var(--code-bg);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
    border: 1px solid var(--border-light);
}

.next-steps {
    background: var(--success-bg);
    border: 1px solid var(--success-border);
    border-radius: 6px;
    padding: 16px;
    text-align: left;
}

.next-steps h4 {
    margin: 0 0 12px 0;
    color: #155724;
    text-align: center;
}

.next-steps ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.next-steps li {
    padding: 4px 0;
    color: #155724;
    font-size: 0.9rem;
}

/* Modal Actions */
.modal-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.modal-actions .btn {
    flex: 1;
    padding: 12px 24px;
    font-weight: 500;
}

.modal-actions .btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

.footer-info {
    text-align: center;
}

.footer-info p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.85rem;
}

.footer-info a {
    color: var(--primary-color);
    text-decoration: none;
}

.footer-info a:hover {
    text-decoration: underline;
}

/* Activation Info */
.activation-info {
    background: var(--info-bg);
    border: 1px solid var(--info-border);
    border-radius: 6px;
    padding: 16px;
    margin: 20px 0;
    text-align: center;
}

.activation-info h4 {
    margin: 0 0 8px 0;
    color: var(--text-color);
}

.activation-info p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Button Variants */
.btn-link {
    background: none;
    border: none;
    color: var(--primary-color);
    text-decoration: underline;
    cursor: pointer;
    font-size: 0.9rem;
    padding: 4px 0;
}

.btn-link:hover {
    color: var(--primary-hover);
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .license-modal {
        width: 95%;
        margin: 20px;
    }
    
    .license-modal-header,
    .license-modal-body,
    .license-modal-footer {
        padding: 16px;
    }
    
    .welcome-step .activation-options {
        grid-template-columns: 1fr;
    }
    
    .device-info-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-actions {
        flex-direction: column-reverse;
    }
    
    .modal-actions .btn {
        width: 100%;
    }
    
    .license-key-display {
        flex-direction: column;
    }
    
    .license-key-display code {
        font-size: 1rem;
        word-break: break-all;
    }
}

/* Dark Mode Adjustments */
[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --text-muted: #aaaaaa;
    --border-color: #333333;
    --border-light: #2a2a2a;
    --card-bg: #252525;
    --input-bg: #2a2a2a;
    --code-bg: #1e1e1e;
    --header-bg: #1e1e1e;
    --footer-bg: #1e1e1e;
    --hover-bg: #333333;
    --info-bg: #1e3a8a;
    --info-border: #3b82f6;
    --success-bg: #1e3a1e;
    --success-border: #28a745;
}

/* Light Mode Adjustments */
[data-theme="light"] {
    --bg-color: #ffffff;
    --text-color: #333333;
    --text-muted: #666666;
    --border-color: #e1e5e9;
    --border-light: #f1f3f4;
    --card-bg: #f8f9fa;
    --input-bg: #ffffff;
    --code-bg: #f1f3f4;
    --header-bg: #f8f9fa;
    --footer-bg: #f8f9fa;
    --hover-bg: #f1f3f4;
    --info-bg: #e3f2fd;
    --info-border: #2196f3;
    --success-bg: #e8f5e8;
    --success-border: #28a745;
}