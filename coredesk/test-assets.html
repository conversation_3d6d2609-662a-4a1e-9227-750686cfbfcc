<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Assets - CoreDesk</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1e1e1e;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
        }
        .icon-test {
            width: 24px;
            height: 24px;
            background-color: #007acc;
            margin: 5px;
            display: inline-block;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <h1>CoreDesk Assets Test</h1>
    
    <div class="test-section">
        <h2>Environment Information</h2>
        <div id="env-info"></div>
    </div>
    
    <div class="test-section">
        <h2>Asset Path Testing</h2>
        <div id="asset-path-info"></div>
    </div>
    
    <div class="test-section">
        <h2>Icon Loading Test</h2>
        <div id="icon-test-results"></div>
        <div id="icon-display"></div>
    </div>

    <script src="src/js/utils/AssetPath.js"></script>
    <script>
        // Test environment info
        function displayEnvironmentInfo() {
            const envDiv = document.getElementById('env-info');
            const info = window.AssetPath.getEnvironmentInfo();
            
            envDiv.innerHTML = `
                <p><strong>Is Packaged:</strong> <span class="${info.isPackaged ? 'success' : 'info'}">${info.isPackaged}</span></p>
                <p><strong>Base Path:</strong> <span class="info">${info.basePath}</span></p>
                <p><strong>Resources Path:</strong> <span class="info">${info.resourcesPath}</span></p>
                <p><strong>ElectronAPI Available:</strong> <span class="${info.electronAPIAvailable ? 'success' : 'error'}">${info.electronAPIAvailable}</span></p>
                <p><strong>Location:</strong> <span class="info">${info.location}</span></p>
                <p><strong>User Agent:</strong> <span class="info">${info.userAgent}</span></p>
            `;
        }

        // Test asset path resolution
        function testAssetPaths() {
            const pathDiv = document.getElementById('asset-path-info');
            const testPaths = [
                'icons/folder-mask.svg',
                'icons/cloud-mask.svg',
                'icons/search-mask.svg',
                'icons/grid-mask.svg',
                'icons/puzzle-mask.svg',
                'icons/settings-mask.svg'
            ];
            
            let html = '<h3>Resolved Paths:</h3>';
            testPaths.forEach(path => {
                const resolved = window.AssetPath.resolve(path);
                html += `<p><strong>${path}:</strong> <span class="info">${resolved}</span></p>`;
            });
            
            pathDiv.innerHTML = html;
        }

        // Test icon loading
        async function testIconLoading() {
            const resultsDiv = document.getElementById('icon-test-results');
            const displayDiv = document.getElementById('icon-display');
            
            const icons = [
                'folder-mask',
                'cloud-mask', 
                'search-mask',
                'grid-mask',
                'puzzle-mask',
                'settings-mask',
                'leftPanel-mask',
                'rightPanel-mask',
                'bottomPanel-mask'
            ];
            
            let results = '<h3>Loading Results:</h3>';
            let displayHtml = '<h3>Visual Test:</h3>';
            
            for (const icon of icons) {
                try {
                    const iconPath = window.AssetPath.icon(icon);
                    const exists = await window.AssetPath.exists(`icons/${icon}.svg`);
                    
                    results += `<p><strong>${icon}:</strong> <span class="${exists ? 'success' : 'error'}">${exists ? 'SUCCESS' : 'FAILED'}</span> - ${iconPath}</p>`;
                    
                    if (exists) {
                        displayHtml += `
                            <div class="icon-test" style="mask-image: url('${iconPath}'); -webkit-mask-image: url('${iconPath}');" title="${icon}"></div>
                        `;
                    }
                } catch (error) {
                    results += `<p><strong>${icon}:</strong> <span class="error">ERROR - ${error.message}</span></p>`;
                }
            }
            
            resultsDiv.innerHTML = results;
            displayDiv.innerHTML = displayHtml;
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('[AssetTest] Starting asset tests...');
            
            displayEnvironmentInfo();
            testAssetPaths();
            await testIconLoading();
            
            console.log('[AssetTest] Asset tests completed');
        });
    </script>
</body>
</html>
