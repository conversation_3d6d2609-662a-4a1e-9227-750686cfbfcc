/**
 * DynamicAssetLoader - Carga assets dinámicamente según el entorno
 * Maneja las diferencias entre desarrollo y aplicación empaquetada
 */

class DynamicAssetLoader {
    constructor() {
        this.assetPath = window.AssetPath;
        this.loadedAssets = new Set();
        this.cssRulesInjected = false;
        
        console.log('[DynamicAssetLoader] Initialized');
    }

    /**
     * Inicializa el cargador de assets dinámico
     */
    async initialize() {
        console.log('[DynamicAssetLoader] Starting initialization...');
        
        // Inyectar reglas CSS dinámicas para iconos
        await this.injectDynamicIconCSS();
        
        // Precargar iconos críticos
        await this.preloadCriticalIcons();
        
        console.log('[DynamicAssetLoader] Initialization completed');
    }

    /**
     * Inyecta reglas CSS dinámicas para los iconos
     */
    async injectDynamicIconCSS() {
        if (this.cssRulesInjected) {
            console.log('[DynamicAssetLoader] CSS rules already injected');
            return;
        }

        console.log('[DynamicAssetLoader] Injecting dynamic CSS rules...');

        // Crear elemento style para reglas dinámicas
        const styleElement = document.createElement('style');
        styleElement.id = 'dynamic-asset-css';
        
        // Definir iconos y sus clases CSS correspondientes
        const iconMappings = [
            { class: 'icon-explorer', icon: 'folder-mask' },
            { class: 'icon-cloud', icon: 'cloud-mask' },
            { class: 'icon-search', icon: 'search-mask' },
            { class: 'icon-modules', icon: 'grid-mask' },
            { class: 'icon-extensions', icon: 'puzzle-mask' },
            { class: 'icon-settings', icon: 'settings-mask' },
            { class: 'icon-layout-sidebar-left', icon: 'leftPanel-mask' },
            { class: 'icon-layout-sidebar-right', icon: 'rightPanel-mask' },
            { class: 'icon-layout-panel', icon: 'bottomPanel-mask' }
        ];

        // Generar CSS dinámico
        let cssRules = '/* Dynamic Asset CSS - Generated by DynamicAssetLoader */\n';
        
        for (const mapping of iconMappings) {
            const iconPath = this.assetPath.icon(mapping.icon);
            cssRules += `.${mapping.class} {\n`;
            cssRules += `    mask-image: url('${iconPath}') !important;\n`;
            cssRules += `}\n\n`;
        }

        styleElement.textContent = cssRules;
        document.head.appendChild(styleElement);
        
        this.cssRulesInjected = true;
        console.log('[DynamicAssetLoader] Dynamic CSS rules injected successfully');
        console.log('[DynamicAssetLoader] CSS content:', cssRules);
    }

    /**
     * Precarga iconos críticos para mejorar el rendimiento
     */
    async preloadCriticalIcons() {
        const criticalIcons = [
            'folder-mask',
            'cloud-mask',
            'search-mask',
            'grid-mask',
            'puzzle-mask',
            'settings-mask',
            'leftPanel-mask',
            'rightPanel-mask',
            'bottomPanel-mask'
        ];

        console.log('[DynamicAssetLoader] Preloading critical icons...');

        const preloadPromises = criticalIcons.map(async (iconName) => {
            try {
                const iconPath = this.assetPath.icon(iconName);
                
                // Verificar si el asset existe
                const exists = await this.assetPath.exists(`icons/${iconName}.svg`);
                if (!exists) {
                    console.warn(`[DynamicAssetLoader] Icon not found: ${iconName}`);
                    return false;
                }

                // Precargar el asset
                const response = await fetch(iconPath);
                if (response.ok) {
                    this.loadedAssets.add(iconPath);
                    console.log(`[DynamicAssetLoader] Preloaded: ${iconName}`);
                    return true;
                } else {
                    console.warn(`[DynamicAssetLoader] Failed to preload: ${iconName} (${response.status})`);
                    return false;
                }
            } catch (error) {
                console.error(`[DynamicAssetLoader] Error preloading ${iconName}:`, error);
                return false;
            }
        });

        const results = await Promise.all(preloadPromises);
        const successCount = results.filter(Boolean).length;
        
        console.log(`[DynamicAssetLoader] Preloaded ${successCount}/${criticalIcons.length} critical icons`);
    }

    /**
     * Carga un asset específico
     * @param {string} assetPath - Ruta del asset
     * @returns {Promise<string>} - URL del asset cargado
     */
    async loadAsset(assetPath) {
        const fullPath = this.assetPath.resolve(assetPath);
        
        if (this.loadedAssets.has(fullPath)) {
            console.log(`[DynamicAssetLoader] Asset already loaded: ${assetPath}`);
            return fullPath;
        }

        try {
            const response = await fetch(fullPath);
            if (response.ok) {
                this.loadedAssets.add(fullPath);
                console.log(`[DynamicAssetLoader] Loaded asset: ${assetPath}`);
                return fullPath;
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error(`[DynamicAssetLoader] Failed to load asset: ${assetPath}`, error);
            throw error;
        }
    }

    /**
     * Verifica el estado de carga de assets
     */
    getLoadStatus() {
        return {
            cssRulesInjected: this.cssRulesInjected,
            loadedAssetsCount: this.loadedAssets.size,
            loadedAssets: Array.from(this.loadedAssets),
            environment: this.assetPath.getEnvironmentInfo()
        };
    }

    /**
     * Fuerza la recarga de CSS dinámico
     */
    async reloadDynamicCSS() {
        console.log('[DynamicAssetLoader] Reloading dynamic CSS...');
        
        // Remover CSS existente
        const existingStyle = document.getElementById('dynamic-asset-css');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        this.cssRulesInjected = false;
        
        // Reinyectar CSS
        await this.injectDynamicIconCSS();
    }
}

// Crear instancia global
const dynamicAssetLoader = new DynamicAssetLoader();

// Exportar para uso en módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = dynamicAssetLoader;
}

// Hacer disponible globalmente en el navegador
if (typeof window !== 'undefined') {
    window.DynamicAssetLoader = dynamicAssetLoader;
}

console.log('[DynamicAssetLoader] Module loaded');
