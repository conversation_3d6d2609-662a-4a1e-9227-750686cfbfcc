/**
 * AssetPath - Utility for resolving asset paths in both development and production
 * 
 * En desarrollo: Los assets están en src/assets/
 * En producción (empaquetado): Los assets están en resources/assets/
 */

class AssetPath {
    constructor() {
        this.isPackaged = this.detectPackagedEnvironment();
        this.basePath = this.getBasePath();
        
        console.log('[AssetPath] Environment:', this.isPackaged ? 'PACKAGED' : 'DEVELOPMENT');
        console.log('[AssetPath] Base path:', this.basePath);

        if (this.isPackaged && window.electronAPI) {
            console.log('[AssetPath] Resources path:', window.electronAPI.resourcesPath);
            console.log('[AssetPath] Using file:// protocol for extraResources access');
        }
    }

    /**
     * Detecta si la aplicación está empaquetada
     */
    detectPackagedEnvironment() {
        // Método principal: usar electronAPI.isPackaged si está disponible
        if (typeof window !== 'undefined' && window.electronAPI) {
            return window.electronAPI.isPackaged || false;
        }

        // En Electron empaquetado, __dirname contiene 'app.asar'
        if (typeof __dirname !== 'undefined' && __dirname.includes('app.asar')) {
            return true;
        }

        // Fallback: verificar si estamos en un entorno de desarrollo
        // En desarrollo, usualmente tenemos localhost o file:// con rutas de desarrollo
        if (typeof window !== 'undefined') {
            const href = window.location.href;
            return !(href.includes('localhost') ||
                    href.includes('127.0.0.1') ||
                    href.includes('/src/'));
        }

        return false;
    }

    /**
     * Obtiene la ruta base para los assets
     */
    getBasePath() {
        if (this.isPackaged) {
            // En aplicación empaquetada, usar resourcesPath para acceder a extraResources
            if (window.electronAPI && window.electronAPI.resourcesPath) {
                // Normalizar la ruta para Windows y crear URL file://
                let resourcesPath = window.electronAPI.resourcesPath;

                // Convertir backslashes a forward slashes
                resourcesPath = resourcesPath.replace(/\\/g, '/');

                // Asegurar que la ruta comience con / para URLs file://
                if (!resourcesPath.startsWith('/')) {
                    resourcesPath = '/' + resourcesPath;
                }

                return `file://${resourcesPath}/assets/`;
            } else {
                // Fallback para casos donde resourcesPath no esté disponible
                return './assets/';
            }
        } else {
            // En desarrollo, los assets están en src/assets/
            return './src/assets/';
        }
    }

    /**
     * Resuelve la ruta completa de un asset
     * @param {string} assetPath - Ruta relativa del asset (ej: 'icons/folder-mask.svg')
     * @returns {string} - Ruta completa del asset
     */
    resolve(assetPath) {
        // Remover ./ o / del inicio si existe
        const cleanPath = assetPath.replace(/^\.?\//, '');
        const fullPath = this.basePath + cleanPath;
        
        console.log(`[AssetPath] Resolving: ${assetPath} -> ${fullPath}`);
        return fullPath;
    }

    /**
     * Resuelve la ruta de un icono SVG
     * @param {string} iconName - Nombre del icono (ej: 'folder-mask')
     * @returns {string} - Ruta completa del icono
     */
    icon(iconName) {
        const iconPath = `icons/${iconName}.svg`;
        return this.resolve(iconPath);
    }

    /**
     * Resuelve la ruta de una imagen
     * @param {string} imageName - Nombre de la imagen con extensión
     * @returns {string} - Ruta completa de la imagen
     */
    image(imageName) {
        const imagePath = `images/${imageName}`;
        return this.resolve(imagePath);
    }

    /**
     * Verifica si un asset existe
     * @param {string} assetPath - Ruta del asset
     * @returns {Promise<boolean>} - True si el asset existe
     */
    async exists(assetPath) {
        try {
            const fullPath = this.resolve(assetPath);
            const response = await fetch(fullPath, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            console.warn(`[AssetPath] Asset not found: ${assetPath}`, error);
            return false;
        }
    }

    /**
     * Preload assets para mejorar el rendimiento
     * @param {string[]} assetPaths - Array de rutas de assets
     */
    async preload(assetPaths) {
        console.log('[AssetPath] Preloading assets:', assetPaths);
        
        const promises = assetPaths.map(async (assetPath) => {
            try {
                const fullPath = this.resolve(assetPath);
                await fetch(fullPath);
                console.log(`[AssetPath] Preloaded: ${fullPath}`);
            } catch (error) {
                console.warn(`[AssetPath] Failed to preload: ${assetPath}`, error);
            }
        });

        await Promise.all(promises);
        console.log('[AssetPath] Preloading completed');
    }

    /**
     * Obtiene información del entorno
     */
    getEnvironmentInfo() {
        return {
            isPackaged: this.isPackaged,
            basePath: this.basePath,
            userAgent: navigator.userAgent,
            location: window.location.href,
            dirname: typeof __dirname !== 'undefined' ? __dirname : 'N/A',
            resourcesPath: window.electronAPI ? window.electronAPI.resourcesPath : 'N/A',
            electronAPIAvailable: !!window.electronAPI
        };
    }

    /**
     * Verifica si un asset existe
     * @param {string} assetPath - Ruta relativa del asset
     * @returns {Promise<boolean>} - True si el asset existe
     */
    async exists(assetPath) {
        try {
            const fullPath = this.resolve(assetPath);
            const response = await fetch(fullPath, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            console.log(`[AssetPath] Asset not found: ${assetPath}`, error);
            return false;
        }
    }
}

// Crear instancia global
const assetPath = new AssetPath();

// Exportar para uso en módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = assetPath;
}

// Hacer disponible globalmente en el navegador
if (typeof window !== 'undefined') {
    window.AssetPath = assetPath;
}

console.log('[AssetPath] Initialized with environment:', assetPath.getEnvironmentInfo());
